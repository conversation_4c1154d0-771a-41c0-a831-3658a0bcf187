{"sourceFile": "src/Core/PluginCore/Extensions/ServiceCollectionExtensions.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1751193465221, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751193572385, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,11 +15,17 @@\n     }\r\n     public static IServiceCollection AddEmailService(this IServiceCollection services, IConfiguration configuration)\r\n     {\r\n         services.Configure<EmailServiceSettings>(configuration.GetSection(\"EmailService\"));\r\n-        \r\n+\r\n         services.AddScoped<IEmailService, EmailSenderService>();\r\n         services.AddScoped<ISmtpClient, SmtpClientService>();\r\n+\r\n+        return services;\r\n+    }\r\n+    public static IServiceCollection AddPushNotificationService(this IServiceCollection services)\r\n+    {\r\n+        services.AddScoped<IPushNotificationService, PushNotificationServiceImplementation>();\r\n         \r\n         return services;\r\n     }\r\n }\r\n"}, {"date": 1751193653234, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,8 +24,14 @@\n     }\r\n     public static IServiceCollection AddPushNotificationService(this IServiceCollection services)\r\n     {\r\n         services.AddScoped<IPushNotificationService, PushNotificationServiceImplementation>();\r\n+\r\n+        return services;\r\n+    }\r\n+    public static IServiceCollection AddSmsService(this IServiceCollection services)\r\n+    {\r\n+        services.AddScoped<ISmsService, SmsServiceImplementation>();\r\n         \r\n         return services;\r\n     }\r\n }\r\n"}, {"date": 1751211136810, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,37 +1,160 @@\n using Microsoft.Extensions.DependencyInjection;\r\n-using PluginContract.Interfaces;\r\n+using Microsoft.Extensions.Configuration;\r\n+using Microsoft.Extensions.Logging;\r\n+using PluginCore.Base;\r\n+using PluginCore.Interfaces;\r\n using PluginCore.Services;\r\n \r\n namespace PluginCore.Extensions;\r\n \r\n+/// <summary>\r\n+/// Extension methods for configuring the plugin system in dependency injection.\r\n+/// </summary>\r\n public static class ServiceCollectionExtensions\r\n {\r\n+    /// <summary>\r\n+    /// Adds the core plugin system services.\r\n+    /// </summary>\r\n     public static IServiceCollection AddPluginSystem(this IServiceCollection services)\r\n     {\r\n+        // Core plugin services\r\n         services.AddSingleton<IPluginManager, PluginManager>();\r\n-        services.AddScoped<NotificationService>();\r\n \r\n+        // Message scheduling and storage services\r\n+        services.AddMessageSchedulingServices();\r\n+\r\n+        // Background processor for scheduled messages\r\n+        services.AddScheduledMessageProcessor();\r\n+\r\n         return services;\r\n     }\r\n-    public static IServiceCollection AddEmailService(this IServiceCollection services, IConfiguration configuration)\r\n+\r\n+    /// <summary>\r\n+    /// Adds message scheduling and storage services.\r\n+    /// </summary>\r\n+    public static IServiceCollection AddMessageSchedulingServices(this IServiceCollection services)\r\n     {\r\n-        services.Configure<EmailServiceSettings>(configuration.GetSection(\"EmailService\"));\r\n+        services.AddSingleton<IMessageSchedulingService, DefaultMessageSchedulingService>();\r\n+        services.AddSingleton<IMessageStorageService, DefaultMessageStorageService>();\r\n+        return services;\r\n+    }\r\n \r\n-        services.AddScoped<IEmailService, EmailSenderService>();\r\n-        services.AddScoped<ISmtpClient, SmtpClientService>();\r\n+    /// <summary>\r\n+    /// Adds the scheduled message processor as a hosted service.\r\n+    /// </summary>\r\n+    public static IServiceCollection AddScheduledMessageProcessor(this IServiceCollection services)\r\n+    {\r\n+        services.AddHostedService<ScheduledMessageProcessor>();\r\n+        return services;\r\n+    }\r\n \r\n+    /// <summary>\r\n+    /// Adds gateway plugins with their base classes.\r\n+    /// </summary>\r\n+    public static IServiceCollection AddGatewayPlugins(this IServiceCollection services)\r\n+    {\r\n+        // Register gateway interfaces for dependency injection\r\n+        services.AddTransient<IGatewayMessagePluginType>(provider =>\r\n+        {\r\n+            var pluginManager = provider.GetRequiredService<IPluginManager>();\r\n+            var plugins = pluginManager.GetPlugins<IGatewayMessagePluginType>();\r\n+            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No message gateway plugins found\");\r\n+        });\r\n+\r\n         return services;\r\n     }\r\n-    public static IServiceCollection AddPushNotificationService(this IServiceCollection services)\r\n+\r\n+    /// <summary>\r\n+    /// Loads plugins from the specified directory.\r\n+    /// </summary>\r\n+    public static async Task<IServiceCollection> LoadPluginsAsync(\r\n+        this IServiceCollection services,\r\n+        string pluginDirectory,\r\n+        IServiceProvider? serviceProvider = null)\r\n     {\r\n-        services.AddScoped<IPushNotificationService, PushNotificationServiceImplementation>();\r\n+        if (serviceProvider != null)\r\n+        {\r\n+            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();\r\n+            await pluginManager.LoadPluginsAsync(pluginDirectory);\r\n+        }\r\n \r\n         return services;\r\n     }\r\n-    public static IServiceCollection AddSmsService(this IServiceCollection services)\r\n+\r\n+    /// <summary>\r\n+    /// Adds specific gateway types for easier dependency injection.\r\n+    /// </summary>\r\n+    public static IServiceCollection AddGatewayTypes(this IServiceCollection services)\r\n     {\r\n-        services.AddScoped<ISmsService, SmsServiceImplementation>();\r\n-        \r\n+        // SMS Gateways\r\n+        services.AddTransient<ISmsPlugin>(provider =>\r\n+        {\r\n+            var pluginManager = provider.GetRequiredService<IPluginManager>();\r\n+            var plugins = pluginManager.GetPlugins<ISmsPlugin>();\r\n+            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No SMS plugins found\");\r\n+        });\r\n+\r\n+        // Email Gateways\r\n+        services.AddTransient<IEmailPlugin>(provider =>\r\n+        {\r\n+            var pluginManager = provider.GetRequiredService<IPluginManager>();\r\n+            var plugins = pluginManager.GetPlugins<IEmailPlugin>();\r\n+            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No Email plugins found\");\r\n+        });\r\n+\r\n+        // Push Gateways\r\n+        services.AddTransient<IPushPlugin>(provider =>\r\n+        {\r\n+            var pluginManager = provider.GetRequiredService<IPluginManager>();\r\n+            var plugins = pluginManager.GetPlugins<IPushPlugin>();\r\n+            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No Push plugins found\");\r\n+        });\r\n+\r\n+        // WebApp Gateways\r\n+        services.AddTransient<IWebAppPlugin>(provider =>\r\n+        {\r\n+            var pluginManager = provider.GetRequiredService<IPluginManager>();\r\n+            var plugins = pluginManager.GetPlugins<IWebAppPlugin>();\r\n+            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No WebApp plugins found\");\r\n+        });\r\n+\r\n         return services;\r\n     }\r\n+\r\n+    /// <summary>\r\n+    /// Configures the plugin system with options.\r\n+    /// </summary>\r\n+    public static IServiceCollection ConfigurePluginSystem(\r\n+        this IServiceCollection services,\r\n+        Action<PluginSystemOptions> configure)\r\n+    {\r\n+        services.Configure(configure);\r\n+        return services;\r\n+    }\r\n }\r\n+\r\n+/// <summary>\r\n+/// Configuration options for the plugin system.\r\n+/// </summary>\r\n+public class PluginSystemOptions\r\n+{\r\n+    /// <summary>\r\n+    /// Directory where plugins are located.\r\n+    /// </summary>\r\n+    public string PluginDirectory { get; set; } = \"plugins\";\r\n+\r\n+    /// <summary>\r\n+    /// Whether to automatically load plugins on startup.\r\n+    /// </summary>\r\n+    public bool AutoLoadPlugins { get; set; } = true;\r\n+\r\n+    /// <summary>\r\n+    /// Whether to enable plugin health monitoring.\r\n+    /// </summary>\r\n+    public bool EnableHealthMonitoring { get; set; } = true;\r\n+\r\n+    /// <summary>\r\n+    /// Interval for checking plugin health.\r\n+    /// </summary>\r\n+    public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(5);\r\n+}\r\n"}, {"date": 1751211266494, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,143 +1,122 @@\n using Microsoft.Extensions.DependencyInjection;\r\n-using Microsoft.Extensions.Configuration;\r\n-using Microsoft.Extensions.Logging;\r\n+using Microsoft.Extensions.Hosting;\r\n using PluginCore.Base;\r\n using PluginCore.Interfaces;\r\n using PluginCore.Services;\r\n \r\n namespace PluginCore.Extensions;\r\n \r\n /// <summary>\r\n-/// Extension methods for configuring the plugin system in dependency injection.\r\n+/// Extension methods for configuring the new gateway-based plugin system.\r\n /// </summary>\r\n public static class ServiceCollectionExtensions\r\n {\r\n     /// <summary>\r\n-    /// Adds the core plugin system services.\r\n+    /// Adds the complete gateway-based plugin system.\r\n     /// </summary>\r\n-    public static IServiceCollection AddPluginSystem(this IServiceCollection services)\r\n+    public static IServiceCollection AddGatewayPluginSystem(this IServiceCollection services)\r\n     {\r\n-        // Core plugin services\r\n+        // Core plugin manager\r\n         services.AddSingleton<IPluginManager, PluginManager>();\r\n \r\n-        // Message scheduling and storage services\r\n-        services.AddMessageSchedulingServices();\r\n-\r\n-        // Background processor for scheduled messages\r\n-        services.AddScheduledMessageProcessor();\r\n-\r\n-        return services;\r\n-    }\r\n-\r\n-    /// <summary>\r\n-    /// Adds message scheduling and storage services.\r\n-    /// </summary>\r\n-    public static IServiceCollection AddMessageSchedulingServices(this IServiceCollection services)\r\n-    {\r\n+        // Message scheduling and storage infrastructure\r\n         services.AddSingleton<IMessageSchedulingService, DefaultMessageSchedulingService>();\r\n         services.AddSingleton<IMessageStorageService, DefaultMessageStorageService>();\r\n-        return services;\r\n-    }\r\n \r\n-    /// <summary>\r\n-    /// Adds the scheduled message processor as a hosted service.\r\n-    /// </summary>\r\n-    public static IServiceCollection AddScheduledMessageProcessor(this IServiceCollection services)\r\n-    {\r\n+        // Background processor for scheduled messages\r\n         services.AddHostedService<ScheduledMessageProcessor>();\r\n-        return services;\r\n-    }\r\n \r\n-    /// <summary>\r\n-    /// Adds gateway plugins with their base classes.\r\n-    /// </summary>\r\n-    public static IServiceCollection AddGatewayPlugins(this IServiceCollection services)\r\n-    {\r\n-        // Register gateway interfaces for dependency injection\r\n-        services.AddTransient<IGatewayMessagePluginType>(provider =>\r\n-        {\r\n-            var pluginManager = provider.GetRequiredService<IPluginManager>();\r\n-            var plugins = pluginManager.GetPlugins<IGatewayMessagePluginType>();\r\n-            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No message gateway plugins found\");\r\n-        });\r\n-\r\n         return services;\r\n     }\r\n \r\n     /// <summary>\r\n-    /// Loads plugins from the specified directory.\r\n+    /// Registers gateway plugin types for dependency injection.\r\n+    /// Call this after loading plugins to make them available for injection.\r\n     /// </summary>\r\n-    public static async Task<IServiceCollection> LoadPluginsAsync(\r\n-        this IServiceCollection services,\r\n-        string pluginDirectory,\r\n-        IServiceProvider? serviceProvider = null)\r\n+    public static IServiceCollection RegisterGatewayPlugins(this IServiceCollection services)\r\n     {\r\n-        if (serviceProvider != null)\r\n-        {\r\n-            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();\r\n-            await pluginManager.LoadPluginsAsync(pluginDirectory);\r\n-        }\r\n-\r\n-        return services;\r\n-    }\r\n-\r\n-    /// <summary>\r\n-    /// Adds specific gateway types for easier dependency injection.\r\n-    /// </summary>\r\n-    public static IServiceCollection AddGatewayTypes(this IServiceCollection services)\r\n-    {\r\n         // SMS Gateways\r\n         services.AddTransient<ISmsPlugin>(provider =>\r\n         {\r\n             var pluginManager = provider.GetRequiredService<IPluginManager>();\r\n             var plugins = pluginManager.GetPlugins<ISmsPlugin>();\r\n-            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No SMS plugins found\");\r\n+            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No SMS plugins loaded\");\r\n         });\r\n \r\n         // Email Gateways\r\n         services.AddTransient<IEmailPlugin>(provider =>\r\n         {\r\n             var pluginManager = provider.GetRequiredService<IPluginManager>();\r\n             var plugins = pluginManager.GetPlugins<IEmailPlugin>();\r\n-            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No Email plugins found\");\r\n+            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No Email plugins loaded\");\r\n         });\r\n \r\n         // Push Gateways\r\n         services.AddTransient<IPushPlugin>(provider =>\r\n         {\r\n             var pluginManager = provider.GetRequiredService<IPluginManager>();\r\n             var plugins = pluginManager.GetPlugins<IPushPlugin>();\r\n-            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No Push plugins found\");\r\n+            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No Push plugins loaded\");\r\n         });\r\n \r\n         // WebApp Gateways\r\n         services.AddTransient<IWebAppPlugin>(provider =>\r\n         {\r\n             var pluginManager = provider.GetRequiredService<IPluginManager>();\r\n             var plugins = pluginManager.GetPlugins<IWebAppPlugin>();\r\n-            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No WebApp plugins found\");\r\n+            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No WebApp plugins loaded\");\r\n         });\r\n \r\n+        // Generic gateway access\r\n+        services.AddTransient<IGatewayMessagePluginType>(provider =>\r\n+        {\r\n+            var pluginManager = provider.GetRequiredService<IPluginManager>();\r\n+            var plugins = pluginManager.GetPlugins<IGatewayMessagePluginType>();\r\n+            return plugins.FirstOrDefault() ?? throw new InvalidOperationException(\"No gateway plugins loaded\");\r\n+        });\r\n+\r\n         return services;\r\n     }\r\n \r\n     /// <summary>\r\n+    /// Loads plugins from the specified directory and registers them.\r\n+    /// </summary>\r\n+    public static async Task<IServiceCollection> LoadAndRegisterPluginsAsync(\r\n+        this IServiceCollection services,\r\n+        string pluginDirectory,\r\n+        IServiceProvider serviceProvider)\r\n+    {\r\n+        var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();\r\n+        var result = await pluginManager.LoadPluginsAsync(pluginDirectory);\r\n+\r\n+        if (!result.IsSuccess)\r\n+        {\r\n+            throw new InvalidOperationException($\"Failed to load plugins: {result.Message}\");\r\n+        }\r\n+\r\n+        // Register the loaded plugins for dependency injection\r\n+        services.RegisterGatewayPlugins();\r\n+\r\n+        return services;\r\n+    }\r\n+\r\n+    /// <summary>\r\n     /// Configures the plugin system with options.\r\n     /// </summary>\r\n-    public static IServiceCollection ConfigurePluginSystem(\r\n+    public static IServiceCollection ConfigureGatewayPluginSystem(\r\n         this IServiceCollection services,\r\n-        Action<PluginSystemOptions> configure)\r\n+        Action<GatewayPluginSystemOptions> configure)\r\n     {\r\n         services.Configure(configure);\r\n         return services;\r\n     }\r\n }\r\n \r\n /// <summary>\r\n-/// Configuration options for the plugin system.\r\n+/// Configuration options for the gateway plugin system.\r\n /// </summary>\r\n-public class PluginSystemOptions\r\n+public class GatewayPluginSystemOptions\r\n {\r\n     /// <summary>\r\n     /// Directory where plugins are located.\r\n     /// </summary>\r\n@@ -156,5 +135,15 @@\n     /// <summary>\r\n     /// Interval for checking plugin health.\r\n     /// </summary>\r\n     public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(5);\r\n+\r\n+    /// <summary>\r\n+    /// Whether to enable message scheduling.\r\n+    /// </summary>\r\n+    public bool EnableMessageScheduling { get; set; } = true;\r\n+\r\n+    /// <summary>\r\n+    /// Whether to enable message storage for resending.\r\n+    /// </summary>\r\n+    public bool EnableMessageStorage { get; set; } = true;\r\n }\r\n"}, {"date": 1751240893231, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -25,8 +25,11 @@\n \r\n         // Background processor for scheduled messages\r\n         services.AddHostedService<ScheduledMessageProcessor>();\r\n \r\n+        // Template rendering service\r\n+        services.AddScoped<ITemplateRenderingService, TemplateRenderingService>();\r\n+\r\n         return services;\r\n     }\r\n \r\n     /// <summary>\r\n"}], "date": 1751193465221, "name": "Commit-0", "content": "using Microsoft.Extensions.DependencyInjection;\r\nusing PluginContract.Interfaces;\r\nusing PluginCore.Services;\r\n\r\nnamespace PluginCore.Extensions;\r\n\r\npublic static class ServiceCollectionExtensions\r\n{\r\n    public static IServiceCollection AddPluginSystem(this IServiceCollection services)\r\n    {\r\n        services.AddSingleton<IPluginManager, PluginManager>();\r\n        services.AddScoped<NotificationService>();\r\n\r\n        return services;\r\n    }\r\n    public static IServiceCollection AddEmailService(this IServiceCollection services, IConfiguration configuration)\r\n    {\r\n        services.Configure<EmailServiceSettings>(configuration.GetSection(\"EmailService\"));\r\n        \r\n        services.AddScoped<IEmailService, EmailSenderService>();\r\n        services.AddScoped<ISmtpClient, SmtpClientService>();\r\n        \r\n        return services;\r\n    }\r\n}\r\n"}]}