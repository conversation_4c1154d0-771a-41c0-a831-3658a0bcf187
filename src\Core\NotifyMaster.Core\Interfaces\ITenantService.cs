// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Interface for tenant management service
/// </summary>
public interface ITenantService
{
    /// <summary>
    /// Gets a tenant by ID
    /// </summary>
    Task<Tenant?> GetTenantAsync(string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a tenant by domain
    /// </summary>
    Task<Tenant?> GetTenantByDomainAsync(string domain, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all tenants with pagination
    /// </summary>
    Task<IReadOnlyList<Tenant>> GetTenantsAsync(int skip = 0, int take = 50, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Creates a new tenant
    /// </summary>
    Task<OperationResult<Tenant>> CreateTenantAsync(CreateTenantRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates an existing tenant
    /// </summary>
    Task<OperationResult<Tenant>> UpdateTenantAsync(string tenantId, UpdateTenantRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes a tenant
    /// </summary>
    Task<OperationResult> DeleteTenantAsync(string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Suspends a tenant
    /// </summary>
    Task<OperationResult> SuspendTenantAsync(string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Activates a suspended tenant
    /// </summary>
    Task<OperationResult> ActivateTenantAsync(string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates tenant usage statistics
    /// </summary>
    Task<OperationResult> UpdateTenantUsageAsync(string tenantId, TenantUsage usage, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Checks if tenant has reached usage limits
    /// </summary>
    Task<bool> HasReachedLimitAsync(string tenantId, string limitType, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets tenant plugins
    /// </summary>
    Task<IReadOnlyList<TenantPlugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Configures a plugin for a tenant
    /// </summary>
    Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default);
}
