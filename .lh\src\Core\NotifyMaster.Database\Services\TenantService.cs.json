{"sourceFile": "src/Core/NotifyMaster.Database/Services/TenantService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751225362175, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751233518271, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -214,8 +214,13 @@\n             return OperationResult.Failure($\"Failed to suspend tenant: {ex.Message}\");\n         }\n     }\n \n+    public async Task<OperationResult> SuspendTenantAsync(string tenantId, CancellationToken cancellationToken = default)\n+    {\n+        return await SuspendTenantAsync(tenantId, \"Suspended via API\", cancellationToken);\n+    }\n+\n     public async Task<OperationResult> ActivateTenantAsync(string tenantId, CancellationToken cancellationToken = default)\n     {\n         try\n         {\n"}], "date": 1751225362175, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Database.Services;\n\n/// <summary>\n/// Implementation of tenant management service using Entity Framework\n/// </summary>\npublic class TenantService : ITenantService\n{\n    private readonly NotifyMasterDbContext _context;\n    private readonly ILogger<TenantService> _logger;\n\n    public TenantService(NotifyMasterDbContext context, ILogger<TenantService> logger)\n    {\n        _context = context;\n        _logger = logger;\n    }\n\n    public async Task<Tenant?> GetTenantAsync(string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Tenants\n                .Include(t => t.Users)\n                .Include(t => t.Plugins)\n                .FirstOrDefaultAsync(t => t.Id == tenantId, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting tenant {TenantId}\", tenantId);\n            return null;\n        }\n    }\n\n    public async Task<Tenant?> GetTenantByDomainAsync(string domain, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Tenants\n                .Include(t => t.Users)\n                .Include(t => t.Plugins)\n                .FirstOrDefaultAsync(t => t.Domain == domain, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting tenant by domain {Domain}\", domain);\n            return null;\n        }\n    }\n\n    public async Task<IReadOnlyList<Tenant>> GetTenantsAsync(int skip = 0, int take = 50, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Tenants\n                .OrderBy(t => t.Name)\n                .Skip(skip)\n                .Take(take)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting tenants\");\n            return Array.Empty<Tenant>();\n        }\n    }\n\n    public async Task<OperationResult<Tenant>> CreateTenantAsync(CreateTenantRequest request, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Check if domain already exists\n            var existingTenant = await _context.Tenants\n                .FirstOrDefaultAsync(t => t.Domain == request.Domain, cancellationToken);\n            \n            if (existingTenant != null)\n            {\n                return OperationResult<Tenant>.Failure(\"A tenant with this domain already exists\");\n            }\n\n            var tenant = new Tenant\n            {\n                Id = Guid.NewGuid().ToString(),\n                Name = request.Name,\n                Description = request.Description,\n                Domain = request.Domain,\n                Plan = request.Plan,\n                Status = TenantStatus.Active,\n                CreatedAt = DateTime.UtcNow,\n                CreatedBy = request.CreatedBy,\n                Settings = request.Settings,\n                Limits = request.CustomLimits ?? GetDefaultLimits(request.Plan),\n                Usage = new TenantUsage()\n            };\n\n            _context.Tenants.Add(tenant);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Created tenant {TenantId} with domain {Domain}\", tenant.Id, tenant.Domain);\n            return OperationResult<Tenant>.Success(tenant);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error creating tenant {TenantName}\", request.Name);\n            return OperationResult<Tenant>.Failure($\"Failed to create tenant: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult<Tenant>> UpdateTenantAsync(string tenantId, UpdateTenantRequest request, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tenant = await _context.Tenants.FindAsync(tenantId);\n            if (tenant == null)\n            {\n                return OperationResult<Tenant>.Failure(\"Tenant not found\");\n            }\n\n            // Update properties if provided\n            if (!string.IsNullOrEmpty(request.Name))\n                tenant.Name = request.Name;\n            \n            if (!string.IsNullOrEmpty(request.Description))\n                tenant.Description = request.Description;\n            \n            if (!string.IsNullOrEmpty(request.Domain))\n            {\n                // Check if new domain already exists\n                var existingTenant = await _context.Tenants\n                    .FirstOrDefaultAsync(t => t.Domain == request.Domain && t.Id != tenantId, cancellationToken);\n                \n                if (existingTenant != null)\n                {\n                    return OperationResult<Tenant>.Failure(\"A tenant with this domain already exists\");\n                }\n                \n                tenant.Domain = request.Domain;\n            }\n            \n            if (request.Plan.HasValue)\n                tenant.Plan = request.Plan.Value;\n            \n            if (request.Status.HasValue)\n                tenant.Status = request.Status.Value;\n            \n            if (request.Settings != null)\n                tenant.Settings = request.Settings;\n            \n            if (request.Limits != null)\n                tenant.Limits = request.Limits;\n\n            tenant.UpdatedAt = DateTime.UtcNow;\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Updated tenant {TenantId}\", tenantId);\n            return OperationResult<Tenant>.Success(tenant);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error updating tenant {TenantId}\", tenantId);\n            return OperationResult<Tenant>.Failure($\"Failed to update tenant: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> DeleteTenantAsync(string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tenant = await _context.Tenants.FindAsync(tenantId);\n            if (tenant == null)\n            {\n                return OperationResult.Failure(\"Tenant not found\");\n            }\n\n            _context.Tenants.Remove(tenant);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Deleted tenant {TenantId}\", tenantId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error deleting tenant {TenantId}\", tenantId);\n            return OperationResult.Failure($\"Failed to delete tenant: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> SuspendTenantAsync(string tenantId, string reason, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tenant = await _context.Tenants.FindAsync(tenantId);\n            if (tenant == null)\n            {\n                return OperationResult.Failure(\"Tenant not found\");\n            }\n\n            tenant.Status = TenantStatus.Suspended;\n            tenant.UpdatedAt = DateTime.UtcNow;\n            \n            // Store suspension reason in settings\n            tenant.Settings[\"SuspensionReason\"] = reason;\n            tenant.Settings[\"SuspendedAt\"] = DateTime.UtcNow;\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Suspended tenant {TenantId} with reason: {Reason}\", tenantId, reason);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error suspending tenant {TenantId}\", tenantId);\n            return OperationResult.Failure($\"Failed to suspend tenant: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> ActivateTenantAsync(string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tenant = await _context.Tenants.FindAsync(tenantId);\n            if (tenant == null)\n            {\n                return OperationResult.Failure(\"Tenant not found\");\n            }\n\n            tenant.Status = TenantStatus.Active;\n            tenant.UpdatedAt = DateTime.UtcNow;\n            \n            // Remove suspension information\n            tenant.Settings.Remove(\"SuspensionReason\");\n            tenant.Settings.Remove(\"SuspendedAt\");\n            tenant.Settings[\"ActivatedAt\"] = DateTime.UtcNow;\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Activated tenant {TenantId}\", tenantId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error activating tenant {TenantId}\", tenantId);\n            return OperationResult.Failure($\"Failed to activate tenant: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> UpdateTenantUsageAsync(string tenantId, TenantUsage usage, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tenant = await _context.Tenants.FindAsync(tenantId);\n            if (tenant == null)\n            {\n                return OperationResult.Failure(\"Tenant not found\");\n            }\n\n            tenant.Usage = usage;\n            tenant.Usage.LastUpdated = DateTime.UtcNow;\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error updating tenant usage {TenantId}\", tenantId);\n            return OperationResult.Failure($\"Failed to update tenant usage: {ex.Message}\");\n        }\n    }\n\n    public async Task<bool> HasReachedLimitAsync(string tenantId, string limitType, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tenant = await _context.Tenants.FindAsync(tenantId);\n            if (tenant == null) return true;\n\n            return limitType switch\n            {\n                \"users\" => tenant.Usage.CurrentUsers >= tenant.Limits.MaxUsers,\n                \"messages\" => tenant.Usage.MessagesThisMonth >= tenant.Limits.MaxMessagesPerMonth,\n                \"plugins\" => tenant.Usage.ActivePlugins >= tenant.Limits.MaxPlugins,\n                \"templates\" => tenant.Usage.Templates >= tenant.Limits.MaxTemplates,\n                \"webhooks\" => tenant.Usage.Webhooks >= tenant.Limits.MaxWebhooks,\n                _ => false\n            };\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error checking tenant limit {TenantId} {LimitType}\", tenantId, limitType);\n            return true; // Err on the side of caution\n        }\n    }\n\n    public async Task<IReadOnlyList<TenantPlugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.TenantPlugins\n                .Where(tp => tp.TenantId == tenantId)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting tenant plugins {TenantId}\", tenantId);\n            return Array.Empty<TenantPlugin>();\n        }\n    }\n\n    public async Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tenantPlugin = await _context.TenantPlugins\n                .FirstOrDefaultAsync(tp => tp.TenantId == tenantId && tp.PluginName == pluginName, cancellationToken);\n\n            if (tenantPlugin == null)\n            {\n                tenantPlugin = new TenantPlugin\n                {\n                    Id = Guid.NewGuid().ToString(),\n                    TenantId = tenantId,\n                    PluginName = pluginName,\n                    IsEnabled = true,\n                    Configuration = configuration,\n                    ConfiguredAt = DateTime.UtcNow\n                };\n                _context.TenantPlugins.Add(tenantPlugin);\n            }\n            else\n            {\n                tenantPlugin.Configuration = configuration;\n                tenantPlugin.UpdatedAt = DateTime.UtcNow;\n            }\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Configured plugin {PluginName} for tenant {TenantId}\", pluginName, tenantId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error configuring tenant plugin {TenantId} {PluginName}\", tenantId, pluginName);\n            return OperationResult.Failure($\"Failed to configure plugin: {ex.Message}\");\n        }\n    }\n\n    private TenantLimits GetDefaultLimits(TenantPlan plan)\n    {\n        return plan switch\n        {\n            TenantPlan.Basic => new TenantLimits\n            {\n                MaxUsers = 5,\n                MaxMessagesPerMonth = 1000,\n                MaxPlugins = 2,\n                MaxTemplates = 10,\n                MaxWebhooks = 2,\n                CanUseCustomDomain = false,\n                CanUseAdvancedFeatures = false\n            },\n            TenantPlan.Professional => new TenantLimits\n            {\n                MaxUsers = 25,\n                MaxMessagesPerMonth = 10000,\n                MaxPlugins = 10,\n                MaxTemplates = 50,\n                MaxWebhooks = 10,\n                CanUseCustomDomain = true,\n                CanUseAdvancedFeatures = true\n            },\n            TenantPlan.Enterprise => new TenantLimits\n            {\n                MaxUsers = 100,\n                MaxMessagesPerMonth = 100000,\n                MaxPlugins = 50,\n                MaxTemplates = 200,\n                MaxWebhooks = 50,\n                CanUseCustomDomain = true,\n                CanUseAdvancedFeatures = true\n            },\n            _ => new TenantLimits()\n        };\n    }\n}\n"}]}