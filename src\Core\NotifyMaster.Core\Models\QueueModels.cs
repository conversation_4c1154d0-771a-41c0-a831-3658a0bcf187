namespace NotifyMaster.Core.Models;

/// <summary>
/// Represents a notification item in the queue
/// </summary>
public class NotificationQueueItem
{
    /// <summary>
    /// Unique queue identifier
    /// </summary>
    public string QueueId { get; set; } = string.Empty;
    
    /// <summary>
    /// Original message identifier
    /// </summary>
    public string MessageId { get; set; } = string.Empty;
    
    /// <summary>
    /// Type of notification (Email, SMS, Push, etc.)
    /// </summary>
    public NotificationType Type { get; set; }
    
    /// <summary>
    /// Recipient of the notification
    /// </summary>
    public string Recipient { get; set; } = string.Empty;
    
    /// <summary>
    /// Subject line for the notification
    /// </summary>
    public string Subject { get; set; } = string.Empty;
    
    /// <summary>
    /// Content/body of the notification
    /// </summary>
    public string Content { get; set; } = string.Empty;
    
    /// <summary>
    /// User ID associated with the notification
    /// </summary>
    public string? UserId { get; set; }
    
    /// <summary>
    /// Correlation ID for tracking related operations
    /// </summary>
    public string? CorrelationId { get; set; }
    
    /// <summary>
    /// Additional metadata for the notification
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
    
    /// <summary>
    /// When the notification was queued
    /// </summary>
    public DateTime QueuedAt { get; set; }
    
    /// <summary>
    /// When the notification was processed (if completed)
    /// </summary>
    public DateTime? ProcessedAt { get; set; }
    
    /// <summary>
    /// Number of retry attempts
    /// </summary>
    public int RetryCount { get; set; }
    
    /// <summary>
    /// Maximum number of retries allowed
    /// </summary>
    public int MaxRetries { get; set; } = 3;
    
    /// <summary>
    /// Error message if processing failed
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Priority level of the notification
    /// </summary>
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;
    
    /// <summary>
    /// Scheduled delivery time (if applicable)
    /// </summary>
    public DateTime? ScheduledFor { get; set; }
}

/// <summary>
/// Priority levels for notifications
/// </summary>
public enum NotificationPriority
{
    /// <summary>
    /// Low priority - can be delayed
    /// </summary>
    Low = 0,
    
    /// <summary>
    /// Normal priority - standard processing
    /// </summary>
    Normal = 1,
    
    /// <summary>
    /// High priority - expedited processing
    /// </summary>
    High = 2,
    
    /// <summary>
    /// Critical priority - immediate processing
    /// </summary>
    Critical = 3
}

/// <summary>
/// Represents a message that failed processing and was moved to dead letter queue
/// </summary>
public class DeadLetterMessage
{
    /// <summary>
    /// Unique identifier for the dead letter message
    /// </summary>
    public string Id { get; set; } = string.Empty;
    
    /// <summary>
    /// Original message ID that failed
    /// </summary>
    public string OriginalMessageId { get; set; } = string.Empty;
    
    /// <summary>
    /// Tenant ID associated with the message
    /// </summary>
    public string TenantId { get; set; } = string.Empty;
    
    /// <summary>
    /// Channel/type of the failed message
    /// </summary>
    public string Channel { get; set; } = string.Empty;
    
    /// <summary>
    /// Original message content
    /// </summary>
    public string OriginalMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// Error message describing the failure
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// Stack trace of the error (if available)
    /// </summary>
    public string? StackTrace { get; set; }
    
    /// <summary>
    /// Number of retry attempts made
    /// </summary>
    public int RetryCount { get; set; }
    
    /// <summary>
    /// When the message first failed
    /// </summary>
    public DateTime FirstFailedAt { get; set; }
    
    /// <summary>
    /// When the message last failed
    /// </summary>
    public DateTime LastFailedAt { get; set; }
    
    /// <summary>
    /// Current status of the dead letter message
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// Additional metadata about the failure
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Represents an outbound webhook job
/// </summary>
public class OutboundWebhookJob
{
    /// <summary>
    /// Unique identifier for the webhook job
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Tenant ID associated with the webhook
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Target URL for the webhook
    /// </summary>
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// HTTP method to use (GET, POST, PUT, etc.)
    /// </summary>
    public string HttpMethod { get; set; } = "POST";

    /// <summary>
    /// Headers to include in the webhook request
    /// </summary>
    public Dictionary<string, string>? Headers { get; set; }

    /// <summary>
    /// Payload/body of the webhook
    /// </summary>
    public string Payload { get; set; } = string.Empty;

    /// <summary>
    /// Content type of the payload
    /// </summary>
    public string ContentType { get; set; } = "application/json";

    /// <summary>
    /// Current status of the webhook job
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// When the webhook job was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When the webhook was last attempted
    /// </summary>
    public DateTime? LastAttemptAt { get; set; }

    /// <summary>
    /// When the webhook was completed successfully
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Number of retry attempts made
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// Maximum number of retries allowed
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Error message if webhook failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// HTTP response code from the last attempt
    /// </summary>
    public int? ResponseCode { get; set; }

    /// <summary>
    /// Response body from the last attempt
    /// </summary>
    public string? ResponseBody { get; set; }

    /// <summary>
    /// Timeout for the webhook request in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;
}

/// <summary>
/// Represents the result of a webhook retry attempt
/// </summary>
public class WebhookRetryResult
{
    /// <summary>
    /// When the retry attempt was made
    /// </summary>
    public DateTime AttemptedAt { get; set; }

    /// <summary>
    /// HTTP response code received
    /// </summary>
    public int? ResponseCode { get; set; }

    /// <summary>
    /// Response body received
    /// </summary>
    public string? ResponseBody { get; set; }

    /// <summary>
    /// Error message if the attempt failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether the attempt was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Duration of the request in milliseconds
    /// </summary>
    public long DurationMs { get; set; }
}
