// Using statements are handled by GlobalUsings.cs

namespace NotifyMasterApi.Features.Setup.TUI;

/// <summary>
/// ASCII Art Logo for BlackBee/NotifyMaster
/// </summary>
public static class AsciiLogo
{
    public static string GetLogo()
    {
        return @"
    ╔══════════════════════════════════════════════════════════════════════╗
    ║                                                                      ║
    ║    ██████╗ ██╗      █████╗  ██████╗██╗  ██╗██████╗ ███████╗███████╗  ║
    ║    ██╔══██╗██║     ██╔══██╗██╔════╝██║ ██╔╝██╔══██╗██╔════╝██╔════╝  ║
    ║    ██████╔╝██║     ███████║██║     █████╔╝ ██████╔╝█████╗  █████╗    ║
    ║    ██╔══██╗██║     ██╔══██║██║     ██╔═██╗ ██╔══██╗██╔══╝  ██╔══╝    ║
    ║    ██████╔╝███████╗██║  ██║╚██████╗██║  ██╗██████╔╝███████╗███████╗  ║
    ║    ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚═════╝ ╚══════╝╚══════╝  ║
    ║                                                                      ║
    ║                          🐝 NOTIFICATION SERVICE 🐝                  ║
    ║                                                                      ║
    ║                    ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░                  ║
    ║                  ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░                  ║
    ║                ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░                ║
    ║              ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░              ║
    ║            ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░            ║
    ║          ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░          ║
    ║        ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░        ║
    ║      ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░      ║
    ║    ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░    ║
    ║  ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░  ║
    ║░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░║
    ║██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██║
    ║██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██║
    ║██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██║
    ║██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██║
    ║██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██║
    ║██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██║
    ║██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██║
    ║██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██║
    ║░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░║
    ║  ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░  ║
    ║    ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░    ║
    ║      ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░      ║
    ║        ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░        ║
    ║          ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░          ║
    ║            ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░            ║
    ║              ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░              ║
    ║                ░░██░░░░░░░░░░░░░░░░░░░░░░██░░                ║
    ║                  ░░██░░░░░░░░░░░░░░░░██░░                  ║
    ║                    ░░░░░░░░░░░░░░░░░░░░                    ║
    ║                                                                      ║
    ║                        🍯 SWEET NOTIFICATIONS 🍯                     ║
    ║                                                                      ║
    ╚══════════════════════════════════════════════════════════════════════╝
";
    }

    public static string GetCompactLogo()
    {
        return @"
    ╔═══════════════════════════════════════════════════════════╗
    ║  ██████╗ ██╗      █████╗  ██████╗██╗  ██╗██████╗ ███████╗ ║
    ║  ██╔══██╗██║     ██╔══██╗██╔════╝██║ ██╔╝██╔══██╗██╔════╝ ║
    ║  ██████╔╝██║     ███████║██║     █████╔╝ ██████╔╝█████╗   ║
    ║  ██╔══██╗██║     ██╔══██║██║     ██╔═██╗ ██╔══██╗██╔══╝   ║
    ║  ██████╔╝███████╗██║  ██║╚██████╗██║  ██╗██████╔╝███████╗ ║
    ║  ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚═════╝ ╚══════╝ ║
    ║                                                           ║
    ║                🐝 NOTIFICATION SERVICE 🐝                 ║
    ╚═══════════════════════════════════════════════════════════╝
";
    }

    public static string GetMinimalLogo()
    {
        return @"
    ┌─────────────────────────────────────────────┐
    │  ██████╗ ██╗      █████╗  ██████╗██╗  ██╗   │
    │  ██╔══██╗██║     ██╔══██╗██╔════╝██║ ██╔╝   │
    │  ██████╔╝██║     ███████║██║     █████╔╝    │
    │  ██╔══██╗██║     ██╔══██║██║     ██╔═██╗    │
    │  ██████╔╝███████╗██║  ██║╚██████╗██║  ██╗   │
    │  ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝   │
    │                                             │
    │           🐝 NOTIFICATION SERVICE 🐝        │
    └─────────────────────────────────────────────┘
";
    }

    public static string GetBeeIcon()
    {
        return @"
                    ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░
                  ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
                ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
              ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
            ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
          ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
        ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
      ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
    ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
  ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██
██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██
██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██
██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██
██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██
██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██
██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██
██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██
░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
  ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
    ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
      ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
        ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
          ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
            ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
              ░░██░░░░░░░░░░░░░░░░░░░░░░░░░░░░██░░
                ░░██░░░░░░░░░░░░░░░░░░░░░░██░░
                  ░░██░░░░░░░░░░░░░░░░██░░
                    ░░░░░░░░░░░░░░░░░░░░
";
    }

    public static string GetDosStyleHeader()
    {
        return @"
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║  ██████╗ ██╗      █████╗  ██████╗██╗  ██╗██████╗ ███████╗███████╗           ║
║  ██╔══██╗██║     ██╔══██╗██╔════╝██║ ██╔╝██╔══██╗██╔════╝██╔════╝           ║
║  ██████╔╝██║     ███████║██║     █████╔╝ ██████╔╝█████╗  █████╗             ║
║  ██╔══██╗██║     ██╔══██║██║     ██╔═██╗ ██╔══██╗██╔══╝  ██╔══╝             ║
║  ██████╔╝███████╗██║  ██║╚██████╗██║  ██╗██████╔╝███████╗███████╗           ║
║  ╚═════╝ ╚══════╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚═════╝ ╚══════╝╚══════╝           ║
║                                                                              ║
║                    🐝 NOTIFICATION SERVICE SETUP WIZARD 🐝                   ║
║                                                                              ║
║                          Version 2.0.0 - First Run Setup                   ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
";
    }

    public static string GetDosStyleFooter()
    {
        return @"
╔══════════════════════════════════════════════════════════════════════════════╗
║  F1=Help  F2=Save  F3=Exit  F10=Menu  ESC=Cancel  ENTER=Continue            ║
╚══════════════════════════════════════════════════════════════════════════════╝
";
    }

    public static string GetProgressBar(int percentage, int width = 50)
    {
        var filled = (int)((percentage / 100.0) * width);
        var empty = width - filled;
        
        return $"[{'█'.ToString().PadLeft(filled, '█')}{'░'.ToString().PadLeft(empty, '░')}] {percentage}%";
    }

    public static string GetDosWindow(string title, string content, int width = 76, int height = 20)
    {
        var result = new StringBuilder();
        
        // Top border
        result.AppendLine($"╔{'═'.ToString().PadLeft(width - 2, '═')}╗");
        
        // Title bar
        var titlePadding = (width - title.Length - 4) / 2;
        result.AppendLine($"║{' '.ToString().PadLeft(titlePadding, ' ')}{title}{' '.ToString().PadLeft(width - title.Length - titlePadding - 2, ' ')}║");
        result.AppendLine($"╠{'═'.ToString().PadLeft(width - 2, '═')}╣");
        
        // Content area
        var lines = content.Split('\n');
        for (int i = 0; i < height - 4; i++)
        {
            var line = i < lines.Length ? lines[i] : "";
            if (line.Length > width - 4)
                line = line.Substring(0, width - 4);
            
            result.AppendLine($"║ {line.PadRight(width - 4)} ║");
        }
        
        // Bottom border
        result.AppendLine($"╚{'═'.ToString().PadLeft(width - 2, '═')}╝");
        
        return result.ToString();
    }
}
