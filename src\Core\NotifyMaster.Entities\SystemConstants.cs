// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Entities;

/// <summary>
/// System permissions constants and default permissions
/// </summary>
public static class SystemPermissions
{
    // Tenant Management
    public const string TenantView = "tenant:view";
    public const string TenantCreate = "tenant:create";
    public const string TenantEdit = "tenant:edit";
    public const string TenantDelete = "tenant:delete";
    
    // User Management
    public const string UserView = "user:view";
    public const string UserCreate = "user:create";
    public const string UserEdit = "user:edit";
    public const string UserDelete = "user:delete";
    
    // Role Management
    public const string RoleView = "role:view";
    public const string RoleCreate = "role:create";
    public const string RoleEdit = "role:edit";
    public const string RoleDelete = "role:delete";
    
    // Permission Management
    public const string PermissionView = "permission:view";
    public const string PermissionCreate = "permission:create";
    public const string PermissionEdit = "permission:edit";
    public const string PermissionDelete = "permission:delete";
    
    // Plugin Management
    public const string PluginView = "plugin:view";
    public const string PluginConfigure = "plugin:configure";
    public const string PluginEnable = "plugin:enable";
    public const string PluginDisable = "plugin:disable";
    
    // System Administration
    public const string SystemView = "system:view";
    public const string SystemConfigure = "system:configure";
    public const string SystemMonitor = "system:monitor";
    
    // Notification Management
    public const string NotificationSend = "notification:send";
    public const string NotificationView = "notification:view";
    public const string NotificationTemplate = "notification:template";
    
    // Dashboard and Reports
    public const string DashboardView = "dashboard:view";
    public const string ReportView = "report:view";
    public const string AuditView = "audit:view";

    /// <summary>
    /// Gets the default system permissions
    /// </summary>
    public static IEnumerable<PermissionSeed> GetDefaultPermissions()
    {
        return new[]
        {
            // Tenant Management
            new PermissionSeed { Name = "View Tenants", Resource = "tenant", Action = "view", Description = "View tenant information", IsSystemPermission = true },
            new PermissionSeed { Name = "Create Tenants", Resource = "tenant", Action = "create", Description = "Create new tenants", IsSystemPermission = true },
            new PermissionSeed { Name = "Edit Tenants", Resource = "tenant", Action = "edit", Description = "Modify tenant settings", IsSystemPermission = true },
            new PermissionSeed { Name = "Delete Tenants", Resource = "tenant", Action = "delete", Description = "Remove tenants", IsSystemPermission = true },
            
            // User Management
            new PermissionSeed { Name = "View Users", Resource = "user", Action = "view", Description = "View user information", IsSystemPermission = true },
            new PermissionSeed { Name = "Create Users", Resource = "user", Action = "create", Description = "Create new users", IsSystemPermission = true },
            new PermissionSeed { Name = "Edit Users", Resource = "user", Action = "edit", Description = "Modify user settings", IsSystemPermission = true },
            new PermissionSeed { Name = "Delete Users", Resource = "user", Action = "delete", Description = "Remove users", IsSystemPermission = true },
            
            // Role Management
            new PermissionSeed { Name = "View Roles", Resource = "role", Action = "view", Description = "View role information", IsSystemPermission = true },
            new PermissionSeed { Name = "Create Roles", Resource = "role", Action = "create", Description = "Create new roles", IsSystemPermission = true },
            new PermissionSeed { Name = "Edit Roles", Resource = "role", Action = "edit", Description = "Modify role settings", IsSystemPermission = true },
            new PermissionSeed { Name = "Delete Roles", Resource = "role", Action = "delete", Description = "Remove roles", IsSystemPermission = true },
            
            // Permission Management
            new PermissionSeed { Name = "View Permissions", Resource = "permission", Action = "view", Description = "View permission information", IsSystemPermission = true },
            new PermissionSeed { Name = "Create Permissions", Resource = "permission", Action = "create", Description = "Create new permissions", IsSystemPermission = true },
            new PermissionSeed { Name = "Edit Permissions", Resource = "permission", Action = "edit", Description = "Modify permission settings", IsSystemPermission = true },
            new PermissionSeed { Name = "Delete Permissions", Resource = "permission", Action = "delete", Description = "Remove permissions", IsSystemPermission = true },
            
            // Plugin Management
            new PermissionSeed { Name = "View Plugins", Resource = "plugin", Action = "view", Description = "View plugin information", IsSystemPermission = true },
            new PermissionSeed { Name = "Configure Plugins", Resource = "plugin", Action = "configure", Description = "Configure plugin settings", IsSystemPermission = true },
            new PermissionSeed { Name = "Enable Plugins", Resource = "plugin", Action = "enable", Description = "Enable plugins", IsSystemPermission = true },
            new PermissionSeed { Name = "Disable Plugins", Resource = "plugin", Action = "disable", Description = "Disable plugins", IsSystemPermission = true },
            
            // System Administration
            new PermissionSeed { Name = "View System", Resource = "system", Action = "view", Description = "View system information", IsSystemPermission = true },
            new PermissionSeed { Name = "Configure System", Resource = "system", Action = "configure", Description = "Configure system settings", IsSystemPermission = true },
            new PermissionSeed { Name = "Monitor System", Resource = "system", Action = "monitor", Description = "Monitor system health", IsSystemPermission = true },
            
            // Notification Management
            new PermissionSeed { Name = "Send Notifications", Resource = "notification", Action = "send", Description = "Send notifications", IsSystemPermission = true },
            new PermissionSeed { Name = "View Notifications", Resource = "notification", Action = "view", Description = "View notification history", IsSystemPermission = true },
            new PermissionSeed { Name = "Manage Templates", Resource = "notification", Action = "template", Description = "Manage notification templates", IsSystemPermission = true },
            
            // Dashboard and Reports
            new PermissionSeed { Name = "View Dashboard", Resource = "dashboard", Action = "view", Description = "View dashboard", IsSystemPermission = true },
            new PermissionSeed { Name = "View Reports", Resource = "report", Action = "view", Description = "View reports", IsSystemPermission = true },
            new PermissionSeed { Name = "View Audit Logs", Resource = "audit", Action = "view", Description = "View audit logs", IsSystemPermission = true }
        };
    }
}

/// <summary>
/// System roles constants and default roles
/// </summary>
public static class SystemRoles
{
    public const string SuperAdmin = "SuperAdmin";
    public const string SystemAdmin = "SystemAdmin";
    public const string TenantAdmin = "TenantAdmin";
    public const string User = "User";
    public const string Viewer = "Viewer";

    /// <summary>
    /// Gets the default system roles
    /// </summary>
    public static IEnumerable<RoleSeed> GetDefaultRoles()
    {
        return new[]
        {
            new RoleSeed
            {
                Name = SuperAdmin,
                Description = "Super administrator with full system access",
                Scope = 0, // System scope
                IsSystemRole = true
            },
            new RoleSeed
            {
                Name = SystemAdmin,
                Description = "System administrator with administrative access",
                Scope = 0, // System scope
                IsSystemRole = true
            },
            new RoleSeed
            {
                Name = TenantAdmin,
                Description = "Tenant administrator with tenant-level access",
                Scope = 1, // Tenant scope
                IsSystemRole = true
            },
            new RoleSeed
            {
                Name = User,
                Description = "Standard user with basic access",
                Scope = 1, // Tenant scope
                IsSystemRole = true
            },
            new RoleSeed
            {
                Name = Viewer,
                Description = "Read-only user with view access",
                Scope = 1, // Tenant scope
                IsSystemRole = true
            }
        };
    }
}

/// <summary>
/// Seed data structure for permissions
/// </summary>
public class PermissionSeed
{
    public string Name { get; set; } = string.Empty;
    public string Resource { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsSystemPermission { get; set; }
}

/// <summary>
/// Seed data structure for roles
/// </summary>
public class RoleSeed
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int Scope { get; set; }
    public bool IsSystemRole { get; set; }
}
