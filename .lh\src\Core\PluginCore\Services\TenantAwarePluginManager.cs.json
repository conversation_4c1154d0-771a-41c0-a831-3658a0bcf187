{"sourceFile": "src/Core/PluginCore/Services/TenantAwarePluginManager.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 21, "patches": [{"date": 1751217494604, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751223789396, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -389,9 +389,8 @@\n                 }\n             }\n         }\n     }\n-    }\n }\n \n /// <summary>\n /// Extension methods for tenant-aware plugin manager\n"}, {"date": 1751224518742, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,6 @@\n+using Microsoft.Extensions.DependencyInjection;\n+using Microsoft.Extensions.Logging;\n using PluginCore.Base;\n using PluginCore.Interfaces;\n using PluginCore.Models;\n \n"}, {"date": 1751224835614, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -391,8 +391,20 @@\n                 }\n             }\n         }\n     }\n+\n+    public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n+    {\n+        // Delegate to the base plugin manager\n+        return _pluginManager.GetPlugin<T>(pluginName);\n+    }\n+\n+    public async Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n+    {\n+        // Delegate to the base plugin manager\n+        return await _pluginManager.ValidatePluginAsync(pluginPath, cancellationToken);\n+    }\n }\n \n /// <summary>\n /// Extension methods for tenant-aware plugin manager\n"}, {"date": 1751225217239, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,5 @@\n-using Microsoft.Extensions.DependencyInjection;\n-using Microsoft.Extensions.Logging;\n-using PluginCore.Base;\n-using PluginCore.Interfaces;\n-using PluginCore.Models;\n+// Using statements are handled by GlobalUsings.cs\n \n namespace PluginCore.Services;\n \n /// <summary>\n"}, {"date": 1751226843988, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -388,9 +388,9 @@\n             }\n         }\n     }\n \n-    public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n+    public T? GetPlugin<T>(string pluginName) where T :  IPluginType\n     {\n         // Delegate to the base plugin manager\n         return _pluginManager.GetPlugin<T>(pluginName);\n     }\n"}, {"date": 1751226923753, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -388,9 +388,9 @@\n             }\n         }\n     }\n \n-    public T? GetPlugin<T>(string pluginName) where T :  IPluginType\n+    public T? GetPlugin<T>(string pluginName) where T : class, IPlugin\n     {\n         // Delegate to the base plugin manager\n         return _pluginManager.GetPlugin<T>(pluginName);\n     }\n"}, {"date": 1751227012349, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -86,9 +86,9 @@\n     public IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType\n         => _basePluginManager.GetPlugins<T>();\n \n     public Task<Plugin?> GetPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n-        => _basePluginManager.GetPluginAsync(pluginName, cancellationToken);\n+        => _basePluginManager.Get.GetPluginAsync(pluginName, cancellationToken);\n \n     public IReadOnlyList<PluginManifest> GetPluginManifests()\n         => _basePluginManager.GetPluginManifests();\n \n"}, {"date": 1751227052589, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -86,9 +86,9 @@\n     public IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType\n         => _basePluginManager.GetPlugins<T>();\n \n     public Task<Plugin?> GetPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n-        => _basePluginManager.Get.GetPluginAsync(pluginName, cancellationToken);\n+        => _basePluginManager.GetPluginAsync(pluginName, cancellationToken);\n \n     public IReadOnlyList<PluginManifest> GetPluginManifests()\n         => _basePluginManager.GetPluginManifests();\n \n"}, {"date": 1751227089422, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -389,11 +389,9 @@\n         }\n     }\n \n     public T? GetPlugin<T>(string pluginName) where T : class, IPlugin\n-    {\n-        // Delegate to the base plugin manager\n-        return _pluginManager.GetPlugin<T>(pluginName);\n+    => _pluginManager.GetPlugin<T>(pluginName);\n     }\n \n     public async Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n     {\n"}, {"date": 1751227098306, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -390,9 +390,10 @@\n     }\n \n     public T? GetPlugin<T>(string pluginName) where T : class, IPlugin\n     => _pluginManager.GetPlugin<T>(pluginName);\n-    }\n+    public IReadOnlyList<PluginManifest> GetPluginManifests()\n+    => _pluginManager.GetPluginManifests();\n \n     public async Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n     {\n         // Delegate to the base plugin manager\n"}, {"date": 1751227113907, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -389,9 +389,10 @@\n         }\n     }\n \n     public T? GetPlugin<T>(string pluginName) where T : class, IPlugin\n-    => _pluginManager.GetPlugin<T>(pluginName);\n+    // => _pluginManager.GetPlugin<T>(pluginName);\n+\n     public IReadOnlyList<PluginManifest> GetPluginManifests()\n     => _pluginManager.GetPluginManifests();\n \n     public async Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n"}, {"date": 1751227120665, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -389,9 +389,9 @@\n         }\n     }\n \n     public T? GetPlugin<T>(string pluginName) where T : class, IPlugin\n-    // => _pluginManager.GetPlugin<T>(pluginName);\n+     => _pluginManager.GetPlugin<T>(pluginName);\n \n     public IReadOnlyList<PluginManifest> GetPluginManifests()\n     => _pluginManager.GetPluginManifests();\n \n"}, {"date": 1751227175264, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -388,10 +388,10 @@\n             }\n         }\n     }\n \n-    public T? GetPlugin<T>(string pluginName) where T : class, IPlugin\n-     => _pluginManager.GetPlugin<T>(pluginName);\n+    public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n+        => _pluginManager.GetPlugin<T>(pluginName);\n \n     public IReadOnlyList<PluginManifest> GetPluginManifests()\n     => _pluginManager.GetPluginManifests();\n \n"}, {"date": 1751227197296, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,8 @@\n // Using statements are handled by GlobalUsings.cs\n \n+using PluginCore.Interfaces;\n+\n namespace PluginCore.Services;\n \n /// <summary>\n /// Interface for tenant-aware plugin management\n@@ -389,9 +391,9 @@\n         }\n     }\n \n     public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n-        => _pluginManager.GetPlugin<T>(pluginName);\n+        => IPluginManager.GetPlugin<T>(pluginName);\n \n     public IReadOnlyList<PluginManifest> GetPluginManifests()\n     => _pluginManager.GetPluginManifests();\n \n"}, {"date": 1751227230570, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -391,17 +391,14 @@\n         }\n     }\n \n     public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n-        => IPluginManager.GetPlugin<T>(pluginName);\n+        => _basePluginManager.GetPlugin<T>(pluginName);\n \n-    public IReadOnlyList<PluginManifest> GetPluginManifests()\n-    => _pluginManager.GetPluginManifests();\n-\n     public async Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n     {\n         // Delegate to the base plugin manager\n-        return await _pluginManager.ValidatePluginAsync(pluginPath, cancellationToken);\n+        return await _basePluginManager.ValidatePluginAsync(pluginPath, cancellationToken);\n     }\n }\n \n /// <summary>\n"}, {"date": 1751227250809, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -391,10 +391,13 @@\n         }\n     }\n \n     public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n-        => _basePluginManager.GetPlugin<T>(pluginName);\n+    {\n+        return _basePluginManager.GetPlugin<T>(pluginName);\n+    }\n \n+\n     public async Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n     {\n         // Delegate to the base plugin manager\n         return await _basePluginManager.ValidatePluginAsync(pluginPath, cancellationToken);\n"}, {"date": 1751227273769, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -390,16 +390,22 @@\n             }\n         }\n     }\n \n-    public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n+    public IPluginManager Get_basePluginManager<T>() where T : class, IPluginType\n     {\n-        return _basePluginManager.GetPlugin<T>(pluginName);\n+        return _basePluginManager;\n     }\n \n \n-    public async Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n+    public T? GetPlugin<T>(string pluginName, IPluginManager _basePluginManager) where T : class, IPluginType\n     {\n+        return _basePluginManager.GetPlugin<T>(pluginName, _basePluginManager.Get_basePluginManager());\n+    }\n+\n+\n+    public async Task<PluginCore.Models.ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n+    {\n         // Delegate to the base plugin manager\n         return await _basePluginManager.ValidatePluginAsync(pluginPath, cancellationToken);\n     }\n }\n"}, {"date": 1751227287872, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,8 @@\n // Using statements are handled by GlobalUsings.cs\n \n using PluginCore.Interfaces;\n+using PluginCore.Models;\n \n namespace PluginCore.Services;\n \n /// <summary>\n"}, {"date": 1751227333760, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -397,11 +397,11 @@\n         return _basePluginManager;\n     }\n \n \n-    public T? GetPlugin<T>(string pluginName, IPluginManager _basePluginManager) where T : class, IPluginType\n+    public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n     {\n-        return _basePluginManager.GetPlugin<T>(pluginName, _basePluginManager.Get_basePluginManager());\n+        return _basePluginManager.GetPlugin<T>(pluginName);\n     }\n \n \n     public async Task<PluginCore.Models.ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n"}, {"date": 1751227359459, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -391,17 +391,11 @@\n             }\n         }\n     }\n \n-    public IPluginManager Get_basePluginManager<T>() where T : class, IPluginType\n-    {\n-        return _basePluginManager;\n-    }\n-\n-\n     public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n     {\n-        return _basePluginManager.GetPlugin<T>(pluginName)??nm;\n+        return _basePluginManager.GetPlugin<T>(pluginName)??nu;\n     }\n \n \n     public async Task<PluginCore.Models.ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n"}, {"date": 1751227378096, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -391,9 +391,9 @@\n             }\n         }\n     }\n \n-    public T GetPlugin<T>(string pluginName) where T : class, IPluginType\n+    public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n     {\n         return _basePluginManager.GetPlugin<T>(pluginName);\n     }\n \n"}], "date": 1751217494604, "name": "Commit-0", "content": "using PluginCore.Base;\nusing PluginCore.Interfaces;\nusing PluginCore.Models;\n\nnamespace PluginCore.Services;\n\n/// <summary>\n/// Interface for tenant-aware plugin management\n/// </summary>\npublic interface ITenantAwarePluginManager : IPluginManager\n{\n    /// <summary>\n    /// Gets plugins available to a specific tenant\n    /// </summary>\n    Task<IReadOnlyList<Plugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets a plugin instance configured for a specific tenant\n    /// </summary>\n    Task<T?> GetTenantPluginAsync<T>(string tenantId, string pluginName, CancellationToken cancellationToken = default) where T : class, IPluginType;\n    \n    /// <summary>\n    /// Configures a plugin for a specific tenant\n    /// </summary>\n    Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Enables a plugin for a specific tenant\n    /// </summary>\n    Task<OperationResult> EnableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Disables a plugin for a specific tenant\n    /// </summary>\n    Task<OperationResult> DisableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets tenant-specific plugin configuration\n    /// </summary>\n    Task<Dictionary<string, object>?> GetTenantPluginConfigurationAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets plugin usage metrics for a tenant\n    /// </summary>\n    Task<PluginMetrics?> GetTenantPluginMetricsAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Checks if a tenant has reached plugin limits\n    /// </summary>\n    Task<bool> HasReachedPluginLimitAsync(string tenantId, CancellationToken cancellationToken = default);\n}\n\n/// <summary>\n/// Tenant-aware plugin manager implementation\n/// </summary>\npublic class TenantAwarePluginManager : ITenantAwarePluginManager\n{\n    private readonly ILogger<TenantAwarePluginManager> _logger;\n    private readonly IPluginManager _basePluginManager;\n    private readonly ITenantService _tenantService;\n    private readonly ITenantContext _tenantContext;\n    private readonly Dictionary<string, Dictionary<string, object>> _tenantPluginInstances;\n    private readonly object _lock = new();\n\n    public TenantAwarePluginManager(\n        ILogger<TenantAwarePluginManager> logger,\n        IPluginManager basePluginManager,\n        ITenantService tenantService,\n        ITenantContext tenantContext)\n    {\n        _logger = logger;\n        _basePluginManager = basePluginManager;\n        _tenantService = tenantService;\n        _tenantContext = tenantContext;\n        _tenantPluginInstances = new Dictionary<string, Dictionary<string, object>>();\n    }\n\n    // Delegate base plugin manager methods\n    public Task<OperationResult> LoadPluginsAsync(string pluginDirectory, CancellationToken cancellationToken = default)\n        => _basePluginManager.LoadPluginsAsync(pluginDirectory, cancellationToken);\n\n    public Task<OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n        => _basePluginManager.LoadPluginAsync(pluginPath, cancellationToken);\n\n    public Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default)\n        => _basePluginManager.LoadPluginByNameAsync(pluginName, pluginDirectory, cancellationToken);\n\n    public IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType\n        => _basePluginManager.GetPlugins<T>();\n\n    public Task<Plugin?> GetPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n        => _basePluginManager.GetPluginAsync(pluginName, cancellationToken);\n\n    public IReadOnlyList<PluginManifest> GetPluginManifests()\n        => _basePluginManager.GetPluginManifests();\n\n    public PluginManifest? GetPluginManifest(string pluginName)\n        => _basePluginManager.GetPluginManifest(pluginName);\n\n    public Task<OperationResult> UnloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n        => _basePluginManager.UnloadPluginAsync(pluginName, cancellationToken);\n\n    public Task<OperationResult> ReloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n        => _basePluginManager.ReloadPluginAsync(pluginName, cancellationToken);\n\n    public Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(CancellationToken cancellationToken = default)\n        => _basePluginManager.GetPluginStatusesAsync(cancellationToken);\n\n    public Task<Dictionary<string, object>?> GetPluginConfigurationAsync(string pluginName, CancellationToken cancellationToken = default)\n        => _basePluginManager.GetPluginConfigurationAsync(pluginName, cancellationToken);\n\n    public Task<OperationResult> ConfigurePluginAsync(string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default)\n        => _basePluginManager.ConfigurePluginAsync(pluginName, configuration, cancellationToken);\n\n    public Task<PluginMetrics?> GetPluginMetricsAsync(string pluginName, CancellationToken cancellationToken = default)\n        => _basePluginManager.GetPluginMetricsAsync(pluginName, cancellationToken);\n\n    public Task<object?> GetPluginHealthStatusAsync(string pluginName, CancellationToken cancellationToken = default)\n        => _basePluginManager.GetPluginHealthStatusAsync(pluginName, cancellationToken);\n\n    // Tenant-aware methods\n    public async Task<IReadOnlyList<Plugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Get tenant plugin configurations\n            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);\n            var enabledPluginNames = tenantPlugins.Where(tp => tp.IsEnabled).Select(tp => tp.PluginName).ToHashSet();\n\n            // Get all available plugins and filter by tenant configuration\n            var allPlugins = new List<Plugin>();\n            var manifests = GetPluginManifests();\n\n            foreach (var manifest in manifests)\n            {\n                if (enabledPluginNames.Contains(manifest.Name))\n                {\n                    var plugin = await GetPluginAsync(manifest.Name, cancellationToken);\n                    if (plugin != null)\n                    {\n                        allPlugins.Add(plugin);\n                    }\n                }\n            }\n\n            return allPlugins.AsReadOnly();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting plugins for tenant {TenantId}\", tenantId);\n            return Array.Empty<Plugin>();\n        }\n    }\n\n    public async Task<T?> GetTenantPluginAsync<T>(string tenantId, string pluginName, CancellationToken cancellationToken = default) where T : class, IPluginType\n    {\n        try\n        {\n            // Check if plugin is enabled for tenant\n            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);\n            var tenantPlugin = tenantPlugins.FirstOrDefault(tp => tp.PluginName == pluginName && tp.IsEnabled);\n            \n            if (tenantPlugin == null)\n            {\n                _logger.LogWarning(\"Plugin {PluginName} is not enabled for tenant {TenantId}\", pluginName, tenantId);\n                return null;\n            }\n\n            // Get the base plugin instance\n            var plugins = GetPlugins<T>();\n            var plugin = plugins.FirstOrDefault(p => p.GetType().Name.Contains(pluginName));\n            \n            if (plugin == null)\n            {\n                _logger.LogWarning(\"Plugin {PluginName} not found or not of type {PluginType}\", pluginName, typeof(T).Name);\n                return null;\n            }\n\n            // Apply tenant-specific configuration if needed\n            await ApplyTenantConfigurationAsync(plugin, tenantPlugin.Configuration);\n\n            return plugin;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting tenant plugin {PluginName} for tenant {TenantId}\", pluginName, tenantId);\n            return null;\n        }\n    }\n\n    public async Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Check if tenant has reached plugin limits\n            if (await HasReachedPluginLimitAsync(tenantId, cancellationToken))\n            {\n                return OperationResult.Failure(\"Tenant has reached plugin limit\");\n            }\n\n            // Configure the plugin for the tenant\n            await _tenantService.ConfigureTenantPluginAsync(tenantId, pluginName, configuration, cancellationToken);\n\n            _logger.LogInformation(\"Plugin {PluginName} configured for tenant {TenantId}\", pluginName, tenantId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error configuring plugin {PluginName} for tenant {TenantId}\", pluginName, tenantId);\n            return OperationResult.Failure($\"Failed to configure plugin: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> EnableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Check if tenant has reached plugin limits\n            if (await HasReachedPluginLimitAsync(tenantId, cancellationToken))\n            {\n                return OperationResult.Failure(\"Tenant has reached plugin limit\");\n            }\n\n            // Enable the plugin for the tenant\n            var configuration = new Dictionary<string, object> { [\"enabled\"] = true };\n            await _tenantService.ConfigureTenantPluginAsync(tenantId, pluginName, configuration, cancellationToken);\n\n            _logger.LogInformation(\"Plugin {PluginName} enabled for tenant {TenantId}\", pluginName, tenantId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error enabling plugin {PluginName} for tenant {TenantId}\", pluginName, tenantId);\n            return OperationResult.Failure($\"Failed to enable plugin: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> DisableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Disable the plugin for the tenant\n            var configuration = new Dictionary<string, object> { [\"enabled\"] = false };\n            await _tenantService.ConfigureTenantPluginAsync(tenantId, pluginName, configuration, cancellationToken);\n\n            _logger.LogInformation(\"Plugin {PluginName} disabled for tenant {TenantId}\", pluginName, tenantId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error disabling plugin {PluginName} for tenant {TenantId}\", pluginName, tenantId);\n            return OperationResult.Failure($\"Failed to disable plugin: {ex.Message}\");\n        }\n    }\n\n    public async Task<Dictionary<string, object>?> GetTenantPluginConfigurationAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);\n            var tenantPlugin = tenantPlugins.FirstOrDefault(tp => tp.PluginName == pluginName);\n            \n            return tenantPlugin?.Configuration;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting tenant plugin configuration for {PluginName} in tenant {TenantId}\", pluginName, tenantId);\n            return null;\n        }\n    }\n\n    public async Task<PluginMetrics?> GetTenantPluginMetricsAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Get base plugin metrics and filter by tenant if needed\n            var metrics = await GetPluginMetricsAsync(pluginName, cancellationToken);\n            \n            // In a real implementation, you would filter metrics by tenant\n            return metrics;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting tenant plugin metrics for {PluginName} in tenant {TenantId}\", pluginName, tenantId);\n            return null;\n        }\n    }\n\n    public async Task<bool> HasReachedPluginLimitAsync(string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tenant = await _tenantService.GetTenantAsync(tenantId, cancellationToken);\n            if (tenant == null) return true;\n\n            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);\n            var enabledPluginCount = tenantPlugins.Count(tp => tp.IsEnabled);\n\n            return enabledPluginCount >= tenant.Limits.MaxPlugins;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error checking plugin limit for tenant {TenantId}\", tenantId);\n            return true; // Err on the side of caution\n        }\n    }\n\n    private async Task ApplyTenantConfigurationAsync(object plugin, Dictionary<string, object> configuration)\n    {\n        // Apply tenant-specific configuration to the plugin instance\n        // This would depend on the plugin's configuration interface\n\n        if (plugin is IGatewayMessagePluginType gatewayPlugin)\n        {\n            // Apply configuration specific to gateway message plugins\n            await ApplyGatewayMessageConfiguration(gatewayPlugin, configuration);\n        }\n        else if (plugin is IGatewayAdminPluginType adminPlugin)\n        {\n            // Apply configuration specific to gateway admin plugins\n            await ApplyGatewayAdminConfiguration(adminPlugin, configuration);\n        }\n        else if (plugin is IGatewayMetricsPluginType metricsPlugin)\n        {\n            // Apply configuration specific to gateway metrics plugins\n            await ApplyGatewayMetricsConfiguration(metricsPlugin, configuration);\n        }\n    }\n\n    private async Task ApplyGatewayMessageConfiguration(IGatewayMessagePluginType plugin, Dictionary<string, object> configuration)\n    {\n        // Apply configuration specific to message gateway plugins\n        foreach (var config in configuration)\n        {\n            // Apply configuration using reflection or a configuration interface\n            var property = plugin.GetType().GetProperty(config.Key);\n            if (property != null && property.CanWrite)\n            {\n                try\n                {\n                    property.SetValue(plugin, config.Value);\n                }\n                catch (Exception ex)\n                {\n                    _logger.LogWarning(ex, \"Failed to set property {PropertyName} on message gateway plugin {PluginType}\",\n                        config.Key, plugin.GetType().Name);\n                }\n            }\n        }\n    }\n\n    private async Task ApplyGatewayAdminConfiguration(IGatewayAdminPluginType plugin, Dictionary<string, object> configuration)\n    {\n        // Apply configuration specific to admin gateway plugins\n        foreach (var config in configuration)\n        {\n            var property = plugin.GetType().GetProperty(config.Key);\n            if (property != null && property.CanWrite)\n            {\n                try\n                {\n                    property.SetValue(plugin, config.Value);\n                }\n                catch (Exception ex)\n                {\n                    _logger.LogWarning(ex, \"Failed to set property {PropertyName} on admin gateway plugin {PluginType}\",\n                        config.Key, plugin.GetType().Name);\n                }\n            }\n        }\n    }\n\n    private async Task ApplyGatewayMetricsConfiguration(IGatewayMetricsPluginType plugin, Dictionary<string, object> configuration)\n    {\n        // Apply configuration specific to metrics gateway plugins\n        foreach (var config in configuration)\n        {\n            var property = plugin.GetType().GetProperty(config.Key);\n            if (property != null && property.CanWrite)\n            {\n                try\n                {\n                    property.SetValue(plugin, config.Value);\n                }\n                catch (Exception ex)\n                {\n                    _logger.LogWarning(ex, \"Failed to set property {PropertyName} on metrics gateway plugin {PluginType}\",\n                        config.Key, plugin.GetType().Name);\n                }\n            }\n        }\n    }\n    }\n}\n\n/// <summary>\n/// Extension methods for tenant-aware plugin manager\n/// </summary>\npublic static class TenantAwarePluginManagerExtensions\n{\n    /// <summary>\n    /// Adds tenant-aware plugin manager to the service collection\n    /// </summary>\n    public static IServiceCollection AddTenantAwarePluginManager(this IServiceCollection services)\n    {\n        services.AddScoped<ITenantAwarePluginManager, TenantAwarePluginManager>();\n        return services;\n    }\n}\n"}]}