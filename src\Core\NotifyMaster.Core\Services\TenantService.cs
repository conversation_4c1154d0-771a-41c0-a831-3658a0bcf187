// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Services;

/// <summary>
/// Service for tenant management operations
/// </summary>
public class TenantService(
    ITenantRepository tenantRepository,
    ILogger<TenantService> logger) : ITenantService
{
    private readonly ITenantRepository _tenantRepository = tenantRepository;
    private readonly ILogger<TenantService> _logger = logger;

    public async Task<Tenant?> GetTenantAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant {TenantId}", tenantId);
            return null;
        }
    }

    public async Task<Tenant?> GetTenantByDomainAsync(string domain, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenants = await _tenantRepository.GetAllAsync(cancellationToken);
            return tenants.FirstOrDefault(t => t.Domain?.Equals(domain, StringComparison.OrdinalIgnoreCase) == true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant by domain {Domain}", domain);
            return null;
        }
    }

    public async Task<IReadOnlyList<Tenant>> GetTenantsAsync(int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            var allTenants = await _tenantRepository.GetAllAsync(cancellationToken);
            return allTenants.Skip(skip).Take(take).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenants with skip={Skip}, take={Take}", skip, take);
            return new List<Tenant>();
        }
    }

    public async Task<NotifyMaster.Core.Models.OperationResult<Tenant>> CreateTenantAsync(CreateTenantRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if tenant with same name already exists
            var existingTenant = await _tenantRepository.GetByNameAsync(request.Name, cancellationToken);
            if (existingTenant != null)
            {
                return NotifyMaster.Core.Models.OperationResult<Tenant>.Failure("A tenant with this name already exists");
            }

            var tenant = new Tenant
            {
                Id = Guid.NewGuid().ToString(),
                Name = request.Name,
                Description = request.Description,
                Domain = request.Domain,
                Plan = request.Plan,
                Status = TenantStatus.Active,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy,
                Settings = new Dictionary<string, object>()
            };

            var createdTenant = await _tenantRepository.AddAsync(tenant, cancellationToken);
            _logger.LogInformation("Created tenant {TenantId} with name {TenantName}", createdTenant.Id, createdTenant.Name);
            
            return NotifyMaster.Core.Models.OperationResult<Tenant>.Success(createdTenant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tenant {TenantName}", request.Name);
            return NotifyMaster.Core.Models.OperationResult<Tenant>.Failure($"Failed to create tenant: {ex.Message}");
        }
    }

    public async Task<NotifyMaster.Core.Models.OperationResult<Tenant>> UpdateTenantAsync(string tenantId, UpdateTenantRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
            if (tenant == null)
            {
                return NotifyMaster.Core.Models.OperationResult<Tenant>.Failure("Tenant not found");
            }

            // Update properties
            if (!string.IsNullOrEmpty(request.Name))
                tenant.Name = request.Name;
            if (!string.IsNullOrEmpty(request.Description))
                tenant.Description = request.Description;
            if (!string.IsNullOrEmpty(request.Domain))
                tenant.Domain = request.Domain;
            if (request.Plan.HasValue)
                tenant.Plan = request.Plan.Value;

            tenant.UpdatedAt = DateTime.UtcNow;

            var updatedTenant = await _tenantRepository.UpdateAsync(tenant, cancellationToken);
            _logger.LogInformation("Updated tenant {TenantId}", tenantId);
            
            return NotifyMaster.Core.Models.OperationResult<Tenant>.Success(updatedTenant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant {TenantId}", tenantId);
            return NotifyMaster.Core.Models.OperationResult<Tenant>.Failure($"Failed to update tenant: {ex.Message}");
        }
    }

    public async Task<NotifyMaster.Core.Models.OperationResult> DeleteTenantAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
            if (tenant == null)
            {
                return NotifyMaster.Core.Models.OperationResult.Failure("Tenant not found");
            }

            await _tenantRepository.DeleteAsync(tenantId, cancellationToken);
            _logger.LogInformation("Deleted tenant {TenantId}", tenantId);

            return NotifyMaster.Core.Models.OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting tenant {TenantId}", tenantId);
            return NotifyMaster.Core.Models.OperationResult.Failure($"Failed to delete tenant: {ex.Message}");
        }
    }

    public async Task<NotifyMaster.Core.Models.OperationResult> SuspendTenantAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
            if (tenant == null)
            {
                return CoreOperationResult.Failure("Tenant not found");
            }

            tenant.Status = TenantStatus.Suspended;
            tenant.UpdatedAt = DateTime.UtcNow;

            await _tenantRepository.UpdateAsync(tenant, cancellationToken);
            _logger.LogInformation("Suspended tenant {TenantId}", tenantId);

            return NotifyMaster.Core.Models.OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error suspending tenant {TenantId}", tenantId);
            return NotifyMaster.Core.Models.OperationResult.Failure($"Failed to suspend tenant: {ex.Message}");
        }
    }

    public async Task<NotifyMaster.Core.Models.OperationResult> ActivateTenantAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
            if (tenant == null)
            {
                return NotifyMaster.Core.Models.OperationResult.Failure("Tenant not found");
            }

            tenant.Status = TenantStatus.Active;
            tenant.UpdatedAt = DateTime.UtcNow;

            await _tenantRepository.UpdateAsync(tenant, cancellationToken);
            _logger.LogInformation("Activated tenant {TenantId}", tenantId);

            return NotifyMaster.Core.Models.OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating tenant {TenantId}", tenantId);
            return NotifyMaster.Core.Models.OperationResult.Failure($"Failed to activate tenant: {ex.Message}");
        }
    }

    public async Task<NotifyMaster.Core.Models.OperationResult> UpdateTenantUsageAsync(string tenantId, TenantUsage usage, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement tenant usage tracking
            _logger.LogInformation("Updated usage for tenant {TenantId}", tenantId);
            await Task.CompletedTask;
            return NotifyMaster.Core.Models.OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant usage {TenantId}", tenantId);
            return NotifyMaster.Core.Models.OperationResult.Failure($"Failed to update tenant usage: {ex.Message}");
        }
    }

    public async Task<bool> HasReachedLimitAsync(string tenantId, string limitType, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement limit checking logic
            await Task.CompletedTask;
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking limits for tenant {TenantId}", tenantId);
            return false;
        }
    }

    public async Task<IReadOnlyList<TenantPlugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement tenant plugin retrieval
            await Task.CompletedTask;
            return new List<TenantPlugin>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting plugins for tenant {TenantId}", tenantId);
            return new List<TenantPlugin>();
        }
    }

    public async Task<NotifyMaster.Core.Models.OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if tenant exists
            var tenant = await _tenantRepository.GetByIdAsync(tenantId, cancellationToken);
            if (tenant == null)
            {
                return NotifyMaster.Core.Models.OperationResult.Failure("Tenant not found");
            }

            // TODO: Implement tenant plugin configuration
            _logger.LogInformation("Configured plugin {PluginName} for tenant {TenantId}", pluginName, tenantId);
            await Task.CompletedTask;
            return NotifyMaster.Core.Models.OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring plugin {PluginName} for tenant {TenantId}", pluginName, tenantId);
            return NotifyMaster.Core.Models.OperationResult.Failure($"Failed to configure plugin: {ex.Message}");
        }
    }
}
