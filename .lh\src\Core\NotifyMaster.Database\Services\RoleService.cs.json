{"sourceFile": "src/Core/NotifyMaster.Database/Services/RoleService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751225396610, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751233478414, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -70,8 +70,26 @@\n             return Array.Empty<Role>();\n         }\n     }\n \n+    public async Task<IReadOnlyList<Role>> GetRolesByTenantAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)\n+    {\n+        try\n+        {\n+            return await _context.Roles\n+                .Where(r => r.TenantId == tenantId)\n+                .OrderBy(r => r.Name)\n+                .Skip(skip)\n+                .Take(take)\n+                .ToListAsync(cancellationToken);\n+        }\n+        catch (Exception ex)\n+        {\n+            _logger.LogError(ex, \"Error getting roles for tenant {TenantId}\", tenantId);\n+            return Array.Empty<Role>();\n+        }\n+    }\n+\n     public async Task<OperationResult<Role>> CreateRoleAsync(CreateRoleRequest request, CancellationToken cancellationToken = default)\n     {\n         try\n         {\n"}], "date": 1751225396610, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Database.Services;\n\n/// <summary>\n/// Implementation of role management service using Entity Framework\n/// </summary>\npublic class RoleService : IRoleService\n{\n    private readonly NotifyMasterDbContext _context;\n    private readonly ILogger<RoleService> _logger;\n\n    public RoleService(NotifyMasterDbContext context, ILogger<RoleService> logger)\n    {\n        _context = context;\n        _logger = logger;\n    }\n\n    public async Task<Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Roles\n                .Include(r => r.Permissions).ThenInclude(rp => rp.Permission)\n                .Include(r => r.Users).ThenInclude(ur => ur.User)\n                .FirstOrDefaultAsync(r => r.Id == roleId, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting role {RoleId}\", roleId);\n            return null;\n        }\n    }\n\n    public async Task<Role?> GetRoleByNameAsync(string roleName, RoleScope scope = RoleScope.Tenant, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Roles\n                .Include(r => r.Permissions).ThenInclude(rp => rp.Permission)\n                .FirstOrDefaultAsync(r => r.Name == roleName && r.Scope == scope, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting role by name {RoleName}\", roleName);\n            return null;\n        }\n    }\n\n    public async Task<IReadOnlyList<Role>> GetRolesAsync(RoleScope? scope = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var query = _context.Roles.AsQueryable();\n\n            if (scope.HasValue)\n            {\n                query = query.Where(r => r.Scope == scope.Value);\n            }\n\n            return await query\n                .OrderBy(r => r.Name)\n                .Skip(skip)\n                .Take(take)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting roles\");\n            return Array.Empty<Role>();\n        }\n    }\n\n    public async Task<OperationResult<Role>> CreateRoleAsync(CreateRoleRequest request, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Check if role already exists\n            var existingRole = await _context.Roles\n                .FirstOrDefaultAsync(r => r.Name == request.Name && r.Scope == request.Scope, cancellationToken);\n\n            if (existingRole != null)\n            {\n                return OperationResult<Role>.Failure(\"A role with this name already exists in the specified scope\");\n            }\n\n            var role = new Role\n            {\n                Id = Guid.NewGuid().ToString(),\n                Name = request.Name,\n                Description = request.Description,\n                Scope = request.Scope,\n                IsSystemRole = request.IsSystemRole,\n                CreatedAt = DateTime.UtcNow\n            };\n\n            _context.Roles.Add(role);\n\n            // Assign permissions if provided\n            if (request.PermissionIds.Any())\n            {\n                var permissions = await _context.Permissions\n                    .Where(p => request.PermissionIds.Contains(p.Id))\n                    .ToListAsync(cancellationToken);\n\n                foreach (var permission in permissions)\n                {\n                    _context.RolePermissions.Add(new RolePermission\n                    {\n                        RoleId = role.Id,\n                        PermissionId = permission.Id,\n                        AssignedAt = DateTime.UtcNow\n                    });\n                }\n            }\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Created role {RoleId} - {RoleName}\", role.Id, role.Name);\n            return OperationResult<Role>.Success(role);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error creating role {RoleName}\", request.Name);\n            return OperationResult<Role>.Failure($\"Failed to create role: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult<Role>> UpdateRoleAsync(string roleId, UpdateRoleRequest request, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var role = await _context.Roles.FindAsync(roleId);\n            if (role == null)\n            {\n                return OperationResult<Role>.Failure(\"Role not found\");\n            }\n\n            // Update properties if provided\n            if (!string.IsNullOrEmpty(request.Name))\n            {\n                // Check if new name already exists\n                var existingRole = await _context.Roles\n                    .FirstOrDefaultAsync(r => r.Name == request.Name && r.Scope == role.Scope && r.Id != roleId, cancellationToken);\n\n                if (existingRole != null)\n                {\n                    return OperationResult<Role>.Failure(\"A role with this name already exists\");\n                }\n\n                role.Name = request.Name;\n            }\n\n            if (!string.IsNullOrEmpty(request.Description))\n                role.Description = request.Description;\n\n            if (request.Scope.HasValue)\n                role.Scope = request.Scope.Value;\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Updated role {RoleId}\", roleId);\n            return OperationResult<Role>.Success(role);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error updating role {RoleId}\", roleId);\n            return OperationResult<Role>.Failure($\"Failed to update role: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> DeleteRoleAsync(string roleId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var role = await _context.Roles.FindAsync(roleId);\n            if (role == null)\n            {\n                return OperationResult.Failure(\"Role not found\");\n            }\n\n            if (role.IsSystemRole)\n            {\n                return OperationResult.Failure(\"Cannot delete system roles\");\n            }\n\n            _context.Roles.Remove(role);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Deleted role {RoleId}\", roleId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error deleting role {RoleId}\", roleId);\n            return OperationResult.Failure($\"Failed to delete role: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> AssignPermissionAsync(string roleId, string permissionId, string? assignedBy = null, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var role = await _context.Roles.FindAsync(roleId);\n            if (role == null)\n            {\n                return OperationResult.Failure(\"Role not found\");\n            }\n\n            var permission = await _context.Permissions.FindAsync(permissionId);\n            if (permission == null)\n            {\n                return OperationResult.Failure(\"Permission not found\");\n            }\n\n            var existingRolePermission = await _context.RolePermissions\n                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId, cancellationToken);\n\n            if (existingRolePermission != null)\n            {\n                return OperationResult.Failure(\"Role already has this permission\");\n            }\n\n            _context.RolePermissions.Add(new RolePermission\n            {\n                RoleId = roleId,\n                PermissionId = permissionId,\n                AssignedAt = DateTime.UtcNow,\n                AssignedBy = assignedBy\n            });\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Assigned permission {PermissionId} to role {RoleId}\", permissionId, roleId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error assigning permission {PermissionId} to role {RoleId}\", permissionId, roleId);\n            return OperationResult.Failure($\"Failed to assign permission: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> RemovePermissionAsync(string roleId, string permissionId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var rolePermission = await _context.RolePermissions\n                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId, cancellationToken);\n\n            if (rolePermission == null)\n            {\n                return OperationResult.Failure(\"Role does not have this permission\");\n            }\n\n            _context.RolePermissions.Remove(rolePermission);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Removed permission {PermissionId} from role {RoleId}\", permissionId, roleId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error removing permission {PermissionId} from role {RoleId}\", permissionId, roleId);\n            return OperationResult.Failure($\"Failed to remove permission: {ex.Message}\");\n        }\n    }\n\n    public async Task<IReadOnlyList<Permission>> GetRolePermissionsAsync(string roleId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.RolePermissions\n                .Include(rp => rp.Permission)\n                .Where(rp => rp.RoleId == roleId)\n                .Select(rp => rp.Permission)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting permissions for role {RoleId}\", roleId);\n            return Array.Empty<Permission>();\n        }\n    }\n\n    public async Task<IReadOnlyList<User>> GetRoleUsersAsync(string roleId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.UserRoles\n                .Include(ur => ur.User)\n                .Where(ur => ur.RoleId == roleId)\n                .Select(ur => ur.User)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting users for role {RoleId}\", roleId);\n            return Array.Empty<User>();\n        }\n    }\n}\n"}]}