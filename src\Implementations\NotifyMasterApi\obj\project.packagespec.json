﻿"restore":{"projectUniqueName":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\NotifyMasterApi.csproj","projectName":"NotifyMasterApi","projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\NotifyMasterApi.csproj","outputPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj":{"projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj"},"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj":{"projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj"},"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj":{"projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"10.0.100"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"AWSSDK.S3":{"target":"Package","version":"[4.0.3, )"},"Azure.Storage.Blobs":{"target":"Package","version":"[12.24.1, )"},"FastEndpoints":{"target":"Package","version":"[6.2.0, )"},"Hangfire.AspNetCore":{"target":"Package","version":"[1.8.20, )"},"Hangfire.Core":{"target":"Package","version":"[1.8.20, )"},"Hangfire.InMemory":{"target":"Package","version":"[1.0.0, )"},"Hangfire.PostgreSql":{"target":"Package","version":"[1.20.12, )"},"Hangfire.SqlServer":{"target":"Package","version":"[1.8.20, )"},"MediatR":{"target":"Package","version":"[12.5.0, )"},"Microsoft.AspNetCore.Authentication.JwtBearer":{"target":"Package","version":"[9.0.6, )"},"Microsoft.AspNetCore.OpenApi":{"target":"Package","version":"[9.0.6, )"},"Microsoft.AspNetCore.SignalR":{"target":"Package","version":"[1.2.0, )"},"Microsoft.EntityFrameworkCore":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.InMemory":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.SqlServer":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.Sqlite":{"target":"Package","version":"[9.0.6, )"},"Microsoft.Extensions.Caching.Memory":{"target":"Package","version":"[9.0.6, )"},"Microsoft.Extensions.Caching.StackExchangeRedis":{"target":"Package","version":"[9.0.6, )"},"Npgsql.EntityFrameworkCore.PostgreSQL":{"target":"Package","version":"[9.0.4, )"},"Pomelo.EntityFrameworkCore.MySql":{"target":"Package","version":"[9.0.0-preview.1, )"},"Scalar.AspNetCore":{"target":"Package","version":"[2.5.3, )"},"Serilog.AspNetCore":{"target":"Package","version":"[9.0.0, )"},"Serilog.Sinks.Console":{"target":"Package","version":"[6.0.0, )"},"Serilog.Sinks.Debug":{"target":"Package","version":"[3.0.0, )"},"System.Text.Json":{"target":"Package","version":"[9.0.6, )"},"Terminal.Gui":{"target":"Package","version":"[1.15.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.NETCore.App.Host.win-x64","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.NETCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[9.0.5, 9.0.5]"}],"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json"}}