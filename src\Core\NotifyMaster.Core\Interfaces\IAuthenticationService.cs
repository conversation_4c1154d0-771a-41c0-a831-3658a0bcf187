// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Interface for authentication service
/// </summary>
public interface IAuthenticationService
{
    /// <summary>
    /// Authenticates a user with email and password
    /// </summary>
    Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Refreshes an authentication token
    /// </summary>
    Task<OperationResult<AuthenticationResult>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Validates a JWT token
    /// </summary>
    Task<OperationResult<ClaimsPrincipal>> ValidateTokenAsync(string token, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Revokes a refresh token
    /// </summary>
    Task<OperationResult> RevokeTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Generates a JWT token for a user
    /// </summary>
    Task<string> GenerateTokenAsync(User user, IReadOnlyList<Role> roles, IR<PERSON>OnlyList<Permission> permissions);
    
    /// <summary>
    /// Generates a refresh token
    /// </summary>
    string GenerateRefreshToken();
    
    /// <summary>
    /// Hashes a password
    /// </summary>
    string HashPassword(string password);
    
    /// <summary>
    /// Verifies a password against a hash
    /// </summary>
    bool VerifyPassword(string password, string hash);
}
