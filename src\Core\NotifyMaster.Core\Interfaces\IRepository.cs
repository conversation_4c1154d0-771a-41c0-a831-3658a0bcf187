// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Generic repository interface for data access
/// </summary>
public interface IRepository<T> where T : class
{
    Task<T?> GetByIdAsync(string id, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<T> AddAsync(T entity, CancellationToken cancellationToken = default);
    Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default);
    Task DeleteAsync(string id, CancellationToken cancellationToken = default);
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for User entities
/// </summary>
public interface IUserRepository : IRepository<User>
{
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<User?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default);
    Task<IEnumerable<User>> GetByTenantAsync(string tenantId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for Role entities
/// </summary>
public interface IRoleRepository : IRepository<Role>
{
    Task<IEnumerable<Role>> GetByTenantAsync(string tenantId, CancellationToken cancellationToken = default);
    Task<Role?> GetByNameAsync(string name, string tenantId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for Tenant entities
/// </summary>
public interface ITenantRepository : IRepository<Tenant>
{
    Task<Tenant?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
}

/// <summary>
/// Repository interface for Permission entities
/// </summary>
public interface IPermissionRepository : IRepository<Permission>
{
    Task<IEnumerable<Permission>> GetByResourceAsync(string resource, CancellationToken cancellationToken = default);
}
