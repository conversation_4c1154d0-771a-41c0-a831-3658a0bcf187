// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Services;

/// <summary>
/// Implementation of tenant context service
/// </summary>
public class TenantContext : ITenantContext
{
    private readonly ILogger<TenantContext> _logger;
    private string? _tenantId;
    private Tenant? _currentTenant;
    private string? _userId;
    private User? _currentUser;

    public TenantContext(ILogger<TenantContext> logger)
    {
        _logger = logger;
    }

    public string? TenantId => _tenantId;
    public Tenant? CurrentTenant => _currentTenant;
    public string? UserId => _userId;
    public User? CurrentUser => _currentUser;

    public void SetTenant(string tenantId, Tenant? tenant = null)
    {
        _tenantId = tenantId;
        _currentTenant = tenant;
        
        _logger.LogDebug("Tenant context set to: {TenantId}", tenantId);
    }

    public void SetUser(string userId, User? user = null)
    {
        _userId = userId;
        _currentUser = user;
        
        _logger.LogDebug("User context set to: {UserId}", userId);
    }

    public void Clear()
    {
        _tenantId = null;
        _currentTenant = null;
        _userId = null;
        _currentUser = null;
        
        _logger.LogDebug("Tenant context cleared");
    }
}

/// <summary>
/// Middleware for setting tenant context from HTTP requests
/// </summary>
public class TenantContextMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TenantContextMiddleware> _logger;

    public TenantContextMiddleware(RequestDelegate next, ILogger<TenantContextMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ITenantContext tenantContext, ITenantService tenantService)
    {
        try
        {
            // Extract tenant information from request
            var tenantId = ExtractTenantId(context);
            var userId = ExtractUserId(context);

            if (!string.IsNullOrEmpty(tenantId))
            {
                // Load tenant information
                var tenant = await tenantService.GetTenantAsync(tenantId);
                tenantContext.SetTenant(tenantId, tenant);
            }

            if (!string.IsNullOrEmpty(userId))
            {
                tenantContext.SetUser(userId);
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in tenant context middleware");
            throw;
        }
        finally
        {
            // Clear context after request
            tenantContext.Clear();
        }
    }

    private string? ExtractTenantId(HttpContext context)
    {
        // Try header first
        if (context.Request.Headers.TryGetValue("X-Tenant-Id", out var headerValue))
        {
            return headerValue.FirstOrDefault();
        }

        // Try query parameter
        if (context.Request.Query.TryGetValue("tenantId", out var queryValue))
        {
            return queryValue.FirstOrDefault();
        }

        // Try JWT claims
        var tenantClaim = context.User?.FindFirst("tenant_id");
        if (tenantClaim != null)
        {
            return tenantClaim.Value;
        }

        // Try subdomain
        var host = context.Request.Host.Host;
        if (host.Contains('.'))
        {
            var subdomain = host.Split('.')[0];
            if (!string.IsNullOrEmpty(subdomain) && subdomain != "www" && subdomain != "api")
            {
                return subdomain;
            }
        }

        return null;
    }

    private string? ExtractUserId(HttpContext context)
    {
        // Try JWT claims
        var userClaim = context.User?.FindFirst("user_id") ?? context.User?.FindFirst(ClaimTypes.NameIdentifier);
        return userClaim?.Value;
    }
}
