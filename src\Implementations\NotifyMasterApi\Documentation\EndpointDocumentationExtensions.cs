namespace NotifyMasterApi.Documentation;

/// <summary>
/// Extension methods for enhanced FastEndpoints documentation
/// </summary>
public static class EndpointDocumentationExtensions
{
    /// <summary>
    /// Configure comprehensive documentation for messaging endpoints
    /// Note: This is a placeholder for documentation configuration.
    /// Actual endpoint configuration should be done in the endpoint's Configure() method.
    /// </summary>
    public static void ConfigureMessagingEndpoint<TRequest, TResponse>(
        this Endpoint<TRequest, TResponse> endpoint,
        string method,
        string route,
        string summary,
        string description,
        string category,
        string[] tags,
        bool requiresAuth = false,
        string? exampleRequest = null,
        string? exampleResponse = null)
        where TRequest : notnull, new()
        where TResponse : notnull, new()
    {
        // Note: This extension method is for documentation purposes only.
        // Actual endpoint configuration (routes, auth, summaries, tags) should be done
        // in the endpoint's Configure() method using FastEndpoints' built-in methods.
    }

    /// <summary>
    /// Configure documentation for admin endpoints
    /// </summary>
    public static void ConfigureAdminEndpoint<TRequest, TResponse>(
        this Endpoint<TRequest, TResponse> endpoint,
        string method,
        string route,
        string summary,
        string description,
        string[]? additionalTags = null)
        where TRequest : notnull, new()
        where TResponse : notnull, new()
    {
        // Note: This is a placeholder for admin endpoint documentation
    }

    /// <summary>
    /// Configure documentation for plugin endpoints
    /// </summary>
    public static void ConfigurePluginEndpoint<TRequest, TResponse>(
        this Endpoint<TRequest, TResponse> endpoint,
        string method,
        string route,
        string summary,
        string description,
        string[]? additionalTags = null)
        where TRequest : notnull, new()
        where TResponse : notnull, new()
    {
        // Note: This is a placeholder for plugin endpoint documentation
    }

    /// <summary>
    /// Configure documentation for health and monitoring endpoints
    /// </summary>
    public static void ConfigureHealthEndpoint<TRequest, TResponse>(
        this Endpoint<TRequest, TResponse> endpoint,
        string method,
        string route,
        string summary,
        string description,
        string[]? additionalTags = null)
        where TRequest : notnull, new()
        where TResponse : notnull, new()
    {
        // Note: This is a placeholder for health endpoint documentation
    }

    /// <summary>
    /// Configure documentation for messaging endpoints (Email, SMS, Push)
    /// </summary>
    public static void ConfigureNotificationEndpoint<TRequest, TResponse>(
        this Endpoint<TRequest, TResponse> endpoint,
        string method,
        string route,
        string summary,
        string description,
        string notificationType,
        string[]? additionalTags = null)
        where TRequest : notnull, new()
        where TResponse : notnull, new()
    {
        // Note: This is a placeholder for notification endpoint documentation
    }
}

/// <summary>
/// Documentation categories for endpoint organization
/// </summary>
public static class DocumentationCategories
{
    public const string Email = "📧 Email Services";
    public const string SMS = "📱 SMS Services";
    public const string Push = "🔔 Push Notifications";
    public const string Messaging = "💬 Messaging Services";
    public const string PluginManagement = "🔌 Plugin Management";
    public const string SystemAdmin = "🔧 System Administration";
    public const string HealthMonitoring = "💚 Health & Monitoring";
    public const string TenantManagement = "🏢 Tenant Management";
    public const string Webhooks = "🔗 Webhooks & Events";
    public const string Templates = "📄 Templates";
    public const string Advanced = "⚡ Advanced Features";
    public const string Setup = "🚀 Setup & Configuration";
}

/// <summary>
/// Common example requests and responses for documentation
/// </summary>
public static class DocumentationExamples
{
    public static class Email
    {
        public const string SendRequest = """
            {
              "to": "<EMAIL>",
              "from": "<EMAIL>",
              "subject": "Welcome to NotificationService",
              "htmlBody": "<h1>Welcome!</h1><p>Thank you for joining us.</p>",
              "plainTextBody": "Welcome! Thank you for joining us.",
              "category": "welcome"
            }
            """;

        public const string SendResponse = """
            {
              "success": true,
              "messageId": "msg_abc123def456",
              "timestamp": "2024-01-15T10:30:00Z"
            }
            """;
    }

    public static class SMS
    {
        public const string SendRequest = """
            {
              "phoneNumber": "+1234567890",
              "message": "Your verification code is: 123456",
              "from": "YourService"
            }
            """;

        public const string SendResponse = """
            {
              "success": true,
              "messageId": "sms_xyz789abc123",
              "timestamp": "2024-01-15T10:30:00Z"
            }
            """;
    }

    public static class Push
    {
        public const string SendRequest = """
            {
              "deviceToken": "device_token_here",
              "title": "New Message",
              "message": "You have a new notification",
              "imageUrl": "https://example.com/image.png",
              "data": {
                "action": "open_chat",
                "chatId": "chat_123"
              }
            }
            """;

        public const string SendResponse = """
            {
              "success": true,
              "messageId": "push_def456ghi789",
              "timestamp": "2024-01-15T10:30:00Z"
            }
            """;
    }
}
