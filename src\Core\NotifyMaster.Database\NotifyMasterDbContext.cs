// Using statements are handled by GlobalUsings.cs
using NotifyMaster.Entities;

namespace NotifyMaster.Database;

/// <summary>
/// Main database context for NotifyMaster with multi-tenancy support
/// </summary>
public class NotifyMasterDbContext : DbContext, INotifyMasterDbContext
{
    public NotifyMasterDbContext(DbContextOptions<NotifyMasterDbContext> options) : base(options)
    {
    }
    // Tenant and User Management
    public DbSet<TenantEntity> Tenants { get; set; }
    public DbSet<UserEntity> Users { get; set; }
    public DbSet<RoleEntity> Roles { get; set; }
    public DbSet<PermissionEntity> Permissions { get; set; }
    public DbSet<UserRoleEntity> UserRoles { get; set; }
    public DbSet<UserPermissionEntity> UserPermissions { get; set; }
    public DbSet<RolePermissionEntity> RolePermissions { get; set; }
    public DbSet<TenantPluginEntity> TenantPlugins { get; set; }



    // Audit and Queue Management
    public DbSet<RequestAuditLog> RequestAuditLogs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure tenant entity
        ConfigureTenant(modelBuilder);
        
        // Configure user management entities
        ConfigureUserManagement(modelBuilder);
        
        // Configure notification entities
        ConfigureNotificationEntities(modelBuilder);

        // Configure audit entities
        ConfigureAuditEntities(modelBuilder);

        // Seed default data
        SeedDefaultData(modelBuilder);
    }

    private static void ConfigureTenant(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<TenantEntity>(entity =>
        {
            entity.HasKey(t => t.Id);
            entity.Property(t => t.Name).IsRequired().HasMaxLength(100);
            entity.Property(t => t.Domain).IsRequired().HasMaxLength(100);
            entity.HasIndex(t => t.Domain).IsUnique();
            entity.Property(t => t.Description).HasMaxLength(500);
            entity.Property(t => t.Status).IsRequired();
            entity.Property(t => t.Plan).IsRequired();
            entity.Property(t => t.CreatedAt).IsRequired();
            entity.Property(t => t.CreatedBy).HasMaxLength(36);

            // Configure JSON columns
            entity.Property(t => t.Settings).HasColumnType("jsonb");
            entity.Property(t => t.CustomLimits).HasColumnType("jsonb");
            entity.Property(t => t.CustomUsage).HasColumnType("jsonb");

            // Configure limits
            entity.Property(t => t.MaxUsers).IsRequired();
            entity.Property(t => t.MaxPlugins).IsRequired();
            entity.Property(t => t.MaxMessagesPerMonth).IsRequired();
            entity.Property(t => t.MaxStorageGB).IsRequired();
            entity.Property(t => t.MaxApiCallsPerDay).IsRequired();

            // Configure usage tracking
            entity.Property(t => t.CurrentUsers).IsRequired();
            entity.Property(t => t.CurrentPlugins).IsRequired();
            entity.Property(t => t.MessagesThisMonth).IsRequired();
            entity.Property(t => t.StorageUsedGB).IsRequired();
            entity.Property(t => t.ApiCallsToday).IsRequired();
            entity.Property(t => t.UsageLastUpdated).IsRequired();
        });

        modelBuilder.Entity<TenantPluginEntity>(entity =>
        {
            entity.HasKey(tp => tp.Id);
            entity.Property(tp => tp.TenantId).IsRequired().HasMaxLength(36);
            entity.Property(tp => tp.PluginName).IsRequired().HasMaxLength(100);
            entity.Property(tp => tp.Configuration).HasColumnType("jsonb");
            entity.Property(tp => tp.IsEnabled).IsRequired();
            entity.Property(tp => tp.ConfiguredAt).IsRequired();
            entity.Property(tp => tp.ConfiguredBy).HasMaxLength(36);

            entity.HasOne(tp => tp.Tenant)
                  .WithMany(t => t.Plugins)
                  .HasForeignKey(tp => tp.TenantId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(tp => new { tp.TenantId, tp.PluginName }).IsUnique();
        });
    }

    private static void ConfigureUserManagement(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<UserEntity>(entity =>
        {
            entity.HasKey(u => u.Id);
            entity.Property(u => u.TenantId).IsRequired();
            entity.Property(u => u.Username).IsRequired().HasMaxLength(100);
            entity.Property(u => u.Email).IsRequired().HasMaxLength(255);
            entity.Property(u => u.PasswordHash).IsRequired();
            entity.Property(u => u.FirstName).HasMaxLength(100);
            entity.Property(u => u.LastName).HasMaxLength(100);

            entity.HasOne(u => u.Tenant)
                  .WithMany(t => t.Users)
                  .HasForeignKey(u => u.TenantId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(u => new { u.TenantId, u.Email }).IsUnique();
            entity.HasIndex(u => new { u.TenantId, u.Username }).IsUnique();

            entity.Property(u => u.Profile).HasColumnType("jsonb");
        });

        modelBuilder.Entity<RoleEntity>(entity =>
        {
            entity.HasKey(r => r.Id);
            entity.Property(r => r.Name).IsRequired().HasMaxLength(100);
            entity.Property(r => r.Description).HasMaxLength(500);
            entity.HasIndex(r => new { r.Name, r.Scope }).IsUnique();
        });

        modelBuilder.Entity<PermissionEntity>(entity =>
        {
            entity.HasKey(p => p.Id);
            entity.Property(p => p.Resource).IsRequired().HasMaxLength(100);
            entity.Property(p => p.Action).IsRequired().HasMaxLength(100);
            entity.Property(p => p.Description).HasMaxLength(500);
            entity.HasIndex(p => new { p.Resource, p.Action }).IsUnique();
        });

        // Configure many-to-many relationships
        modelBuilder.Entity<UserRoleEntity>(entity =>
        {
            entity.HasKey(ur => new { ur.UserId, ur.RoleId });

            entity.HasOne(ur => ur.User)
                  .WithMany(u => u.Roles)
                  .HasForeignKey(ur => ur.UserId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(ur => ur.Role)
                  .WithMany(r => r.Users)
                  .HasForeignKey(ur => ur.RoleId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<UserPermissionEntity>(entity =>
        {
            entity.HasKey(up => new { up.UserId, up.PermissionId });

            entity.HasOne(up => up.User)
                  .WithMany(u => u.Permissions)
                  .HasForeignKey(up => up.UserId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(up => up.Permission)
                  .WithMany(p => p.Users)
                  .HasForeignKey(up => up.PermissionId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<RolePermissionEntity>(entity =>
        {
            entity.HasKey(rp => new { rp.RoleId, rp.PermissionId });

            entity.HasOne(rp => rp.Role)
                  .WithMany(r => r.Permissions)
                  .HasForeignKey(rp => rp.RoleId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(rp => rp.Permission)
                  .WithMany(p => p.Roles)
                  .HasForeignKey(rp => rp.PermissionId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private static void ConfigureNotificationEntities(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<PluginCore.Models.NotificationLog>(entity =>
        {
            entity.HasKey(n => n.Id);
            entity.Property(n => n.TenantId).IsRequired();
            entity.Property(n => n.MessageId).IsRequired().HasMaxLength(100);
            entity.Property(n => n.Channel).IsRequired().HasMaxLength(50);
            entity.Property(n => n.Recipient).IsRequired().HasMaxLength(255);
            entity.Property(n => n.Subject).HasMaxLength(500);
            entity.Property(n => n.Provider).HasMaxLength(100);
            entity.Property(n => n.CorrelationId).HasMaxLength(50);
            entity.Property(n => n.UserId).HasMaxLength(100);

            entity.HasIndex(n => n.TenantId);
            entity.HasIndex(n => n.MessageId);
            entity.HasIndex(n => new { n.TenantId, n.Status });

            entity.OwnsOne(n => n.Metadata, metadata =>
            {
                metadata.ToJson();
            });
        });

        // Add other notification entities as needed
        modelBuilder.Entity<PluginCore.Models.MessageTemplate>(entity =>
        {
            entity.HasKey(mt => mt.Id);
            entity.Property(mt => mt.TenantId).IsRequired();
            entity.Property(mt => mt.Name).IsRequired().HasMaxLength(100);
            entity.Property(mt => mt.Subject).HasMaxLength(500);
            entity.Property(mt => mt.Content).IsRequired();
            
            entity.HasIndex(mt => new { mt.TenantId, mt.Name }).IsUnique();
        });

        modelBuilder.Entity<PluginCore.Models.ScheduledMessage>(entity =>
        {
            entity.HasKey(sm => sm.Id);
            entity.Property(sm => sm.TenantId).IsRequired();
            entity.Property(sm => sm.MessageId).IsRequired().HasMaxLength(100);
            entity.Property(sm => sm.Channel).IsRequired().HasMaxLength(50);
            entity.Property(sm => sm.Recipient).IsRequired().HasMaxLength(255);

            entity.HasIndex(sm => sm.TenantId);
            entity.HasIndex(sm => new { sm.TenantId, sm.ScheduledTime });
        });

        modelBuilder.Entity<PluginCore.Models.WebhookEndpoint>(entity =>
        {
            entity.HasKey(we => we.Id);
            entity.Property(we => we.TenantId).IsRequired();
            entity.Property(we => we.Name).IsRequired().HasMaxLength(100);
            entity.Property(we => we.Url).IsRequired().HasMaxLength(500);
            entity.Property(we => we.Secret).HasMaxLength(100);

            entity.HasIndex(we => new { we.TenantId, we.Name }).IsUnique();
        });
    }

    private static void ConfigureAuditEntities(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<RequestAuditLog>(entity =>
        {
            entity.HasKey(r => r.Id);
            entity.Property(r => r.QueueId).IsRequired().HasMaxLength(50);
            entity.Property(r => r.RequestId).IsRequired().HasMaxLength(50);
            entity.Property(r => r.CorrelationId).HasMaxLength(50);
            entity.Property(r => r.TenantId).HasMaxLength(50);
            entity.Property(r => r.UserId).HasMaxLength(50);
            entity.Property(r => r.Method).IsRequired().HasMaxLength(10);
            entity.Property(r => r.Path).IsRequired().HasMaxLength(500);
            entity.Property(r => r.IpAddress).HasMaxLength(45);
            entity.Property(r => r.ErrorMessage).HasMaxLength(1000);

            // Indexes for performance
            entity.HasIndex(r => r.QueueId).IsUnique();
            entity.HasIndex(r => r.RequestId);
            entity.HasIndex(r => r.TenantId);
            entity.HasIndex(r => new { r.TenantId, r.Status });
            entity.HasIndex(r => r.QueuedAt);
            entity.HasIndex(r => r.CreatedAt);

            // Foreign key relationships
            entity.HasOne(r => r.Tenant)
                  .WithMany()
                  .HasForeignKey(r => r.TenantId)
                  .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(r => r.User)
                  .WithMany()
                  .HasForeignKey(r => r.UserId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
    }

    private static void SeedDefaultData(ModelBuilder modelBuilder)
    {
        // Seed default permissions
        var permissions = SystemPermissions.GetDefaultPermissions();
        var permissionEntities = permissions.Select((p, index) => new PermissionEntity
        {
            Id = Guid.NewGuid().ToString(),
            Name = p.Name,
            Description = p.Description,
            Resource = p.Resource,
            Action = p.Action,
            IsSystemPermission = p.IsSystemPermission,
            CreatedAt = DateTime.UtcNow
        }).ToArray();

        modelBuilder.Entity<PermissionEntity>().HasData(permissionEntities.Cast<object>().ToArray());

        // Seed default roles
        var roles = SystemRoles.GetDefaultRoles();
        var roleEntities = roles.Select(r => new RoleEntity
        {
            Id = Guid.NewGuid().ToString(),
            Name = r.Name,
            Description = r.Description,
            Scope = r.Scope,
            IsSystemRole = r.IsSystemRole,
            CreatedAt = DateTime.UtcNow
        }).ToArray();

        modelBuilder.Entity<RoleEntity>().HasData(roleEntities.Cast<object>().ToArray());

        // Assign all permissions to SuperAdmin role
        var superAdminRole = roleEntities.First(r => r.Name == SystemRoles.SuperAdmin);
        var superAdminPermissions = permissionEntities.Select(p => new RolePermissionEntity
        {
            RoleId = superAdminRole.Id,
            PermissionId = p.Id,
            AssignedAt = DateTime.UtcNow
        }).ToArray();

        modelBuilder.Entity<RolePermissionEntity>().HasData(superAdminPermissions.Cast<object>().ToArray());
    }

    /// <summary>
    /// Begin a database transaction
    /// </summary>
    public async Task<Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        return await Database.BeginTransactionAsync(cancellationToken);
    }
}


