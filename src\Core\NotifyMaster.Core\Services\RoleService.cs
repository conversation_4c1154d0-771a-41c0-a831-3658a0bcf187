// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Services;

/// <summary>
/// Implementation of role management service using repositories
/// </summary>
public class RoleService : IRoleService
{
    private readonly IRoleRepository _roleRepository;
    private readonly ILogger<RoleService> _logger;

    public RoleService(IRoleRepository roleRepository, ILogger<RoleService> logger)
    {
        _roleRepository = roleRepository;
        _logger = logger;
    }

    public async Task<Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Roles
                .Include(r => r.Permissions).ThenInclude(rp => rp.Permission)
                .Include(r => r.Users).ThenInclude(ur => ur.User)
                .FirstOrDefaultAsync(r => r.Id == roleId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role {RoleId}", roleId);
            return null;
        }
    }

    public async Task<Role?> GetRoleByNameAsync(string roleName, RoleScope scope = RoleScope.Tenant, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Roles
                .Include(r => r.Permissions).ThenInclude(rp => rp.Permission)
                .FirstOrDefaultAsync(r => r.Name == roleName && r.Scope == scope, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role by name {RoleName}", roleName);
            return null;
        }
    }

    public async Task<IReadOnlyList<Role>> GetRolesAsync(RoleScope? scope = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.Roles.AsQueryable();

            if (scope.HasValue)
            {
                query = query.Where(r => r.Scope == scope.Value);
            }

            return await query
                .OrderBy(r => r.Name)
                .Skip(skip)
                .Take(take)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles");
            return Array.Empty<Role>();
        }
    }

    public async Task<IReadOnlyList<Role>> GetRolesByTenantAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Roles
                .Where(r => r.TenantId == tenantId)
                .OrderBy(r => r.Name)
                .Skip(skip)
                .Take(take)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for tenant {TenantId}", tenantId);
            return Array.Empty<Role>();
        }
    }

    public async Task<OperationResult<Role>> CreateRoleAsync(CreateRoleRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if role already exists
            var existingRole = await _context.Roles
                .FirstOrDefaultAsync(r => r.Name == request.Name && r.Scope == request.Scope, cancellationToken);

            if (existingRole != null)
            {
                return OperationResult<Role>.Failure("A role with this name already exists");
            }

            var role = new Role
            {
                Id = Guid.NewGuid().ToString(),
                Name = request.Name,
                Description = request.Description,
                Scope = request.Scope,
                TenantId = request.TenantId,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy
            };

            _context.Roles.Add(role);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created role {RoleId} ({Name}) with scope {Scope}", role.Id, role.Name, role.Scope);
            return OperationResult<Role>.Success(role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating role {Name}", request.Name);
            return OperationResult<Role>.Failure($"Failed to create role: {ex.Message}");
        }
    }

    public async Task<OperationResult<Role>> UpdateRoleAsync(string roleId, UpdateRoleRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _context.Roles.FindAsync(roleId);
            if (role == null)
            {
                return OperationResult<Role>.Failure("Role not found");
            }

            // Update fields if provided
            if (!string.IsNullOrEmpty(request.Name))
            {
                // Check if name is already taken
                var existingRole = await _context.Roles
                    .FirstOrDefaultAsync(r => r.Id != roleId && r.Name == request.Name && r.Scope == role.Scope, cancellationToken);
                if (existingRole != null)
                {
                    return OperationResult<Role>.Failure("Role name is already taken");
                }
                role.Name = request.Name;
            }

            if (!string.IsNullOrEmpty(request.Description))
                role.Description = request.Description;

            if (request.IsActive.HasValue)
                role.IsActive = request.IsActive.Value;

            role.UpdatedAt = DateTime.UtcNow;
            role.UpdatedBy = request.UpdatedBy;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated role {RoleId}", roleId);
            return OperationResult<Role>.Success(role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating role {RoleId}", roleId);
            return OperationResult<Role>.Failure($"Failed to update role: {ex.Message}");
        }
    }

    public async Task<OperationResult> DeleteRoleAsync(string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _context.Roles.FindAsync(roleId);
            if (role == null)
            {
                return OperationResult.Failure("Role not found");
            }

            // Check if role is in use
            var isInUse = await _context.UserRoles.AnyAsync(ur => ur.RoleId == roleId, cancellationToken);
            if (isInUse)
            {
                return OperationResult.Failure("Cannot delete role that is assigned to users");
            }

            _context.Roles.Remove(role);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted role {RoleId}", roleId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting role {RoleId}", roleId);
            return OperationResult.Failure($"Failed to delete role: {ex.Message}");
        }
    }

    public async Task<OperationResult> AssignPermissionAsync(string roleId, string permissionId, string? assignedBy = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _context.Roles.FindAsync(roleId);
            if (role == null)
            {
                return OperationResult.Failure("Role not found");
            }

            var permission = await _context.Permissions.FindAsync(permissionId);
            if (permission == null)
            {
                return OperationResult.Failure("Permission not found");
            }

            var existingRolePermission = await _context.RolePermissions
                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId, cancellationToken);

            if (existingRolePermission != null)
            {
                return OperationResult.Failure("Role already has this permission");
            }

            _context.RolePermissions.Add(new RolePermission
            {
                RoleId = roleId,
                PermissionId = permissionId,
                TenantId = role.TenantId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = assignedBy
            });

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Assigned permission {PermissionId} to role {RoleId}", permissionId, roleId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permission {PermissionId} to role {RoleId}", permissionId, roleId);
            return OperationResult.Failure($"Failed to assign permission: {ex.Message}");
        }
    }

    public async Task<OperationResult> RemovePermissionAsync(string roleId, string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var rolePermission = await _context.RolePermissions
                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId, cancellationToken);

            if (rolePermission == null)
            {
                return OperationResult.Failure("Role does not have this permission");
            }

            _context.RolePermissions.Remove(rolePermission);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Removed permission {PermissionId} from role {RoleId}", permissionId, roleId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing permission {PermissionId} from role {RoleId}", permissionId, roleId);
            return OperationResult.Failure($"Failed to remove permission: {ex.Message}");
        }
    }

    public async Task<IReadOnlyList<Permission>> GetRolePermissionsAsync(string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.RolePermissions
                .Include(rp => rp.Permission)
                .Where(rp => rp.RoleId == roleId)
                .Select(rp => rp.Permission)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions for role {RoleId}", roleId);
            return Array.Empty<Permission>();
        }
    }
}
