// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Services;

/// <summary>
/// Implementation of role management service using repositories
/// </summary>
public class RoleService : IRoleService
{
    private readonly IRoleRepository _roleRepository;
    private readonly ILogger<RoleService> _logger;

    public RoleService(IRoleRepository roleRepository, ILogger<RoleService> logger)
    {
        _roleRepository = roleRepository;
        _logger = logger;
    }

    public async Task<Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _roleRepository.GetByIdAsync(roleId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role {RoleId}", roleId);
            return null;
        }
    }

    public async Task<Role?> GetRoleByNameAsync(string roleName, RoleScope scope = RoleScope.Tenant, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement GetByNameAsync in repository or use GetAllAsync and filter
            var roles = await _roleRepository.GetAllAsync(cancellationToken);
            return roles.FirstOrDefault(r => r.Name == roleName && r.Scope == scope);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role by name {RoleName}", roleName);
            return null;
        }
    }

    public async Task<IReadOnlyList<Role>> GetRolesAsync(RoleScope? scope = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            var roles = await _roleRepository.GetAllAsync(cancellationToken);

            var filteredRoles = roles.AsEnumerable();
            if (scope.HasValue)
            {
                filteredRoles = filteredRoles.Where(r => r.Scope == scope.Value);
            }

            return filteredRoles
                .OrderBy(r => r.Name)
                .Skip(skip)
                .Take(take)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles");
            return Array.Empty<Role>();
        }
    }

    public async Task<IReadOnlyList<Role>> GetRolesByTenantAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            var roles = await _roleRepository.GetAllAsync(cancellationToken);
            return roles
                .Where(r => r.TenantId == tenantId)
                .OrderBy(r => r.Name)
                .Skip(skip)
                .Take(take)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for tenant {TenantId}", tenantId);
            return Array.Empty<Role>();
        }
    }

    public async Task<OperationResult<Role>> CreateRoleAsync(CreateRoleRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if role already exists
            var existingRole = await GetRoleByNameAsync(request.Name, request.Scope, cancellationToken);

            if (existingRole != null)
            {
                return OperationResult<Role>.Failure("A role with this name already exists");
            }

            var role = new Role
            {
                Id = Guid.NewGuid().ToString(),
                Name = request.Name,
                Description = request.Description,
                Scope = request.Scope,
                TenantId = request.TenantId,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy
            };

            await _roleRepository.AddAsync(role, cancellationToken);
            await _roleRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created role {RoleId} ({Name}) with scope {Scope}", role.Id, role.Name, role.Scope);
            return OperationResult<Role>.Success(role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating role {Name}", request.Name);
            return OperationResult<Role>.Failure($"Failed to create role: {ex.Message}");
        }
    }

    public async Task<OperationResult<Role>> UpdateRoleAsync(string roleId, UpdateRoleRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return OperationResult<Role>.Failure("Role not found");
            }

            // Update fields if provided
            if (!string.IsNullOrEmpty(request.Name))
            {
                // Check if name is already taken
                var existingRole = await GetRoleByNameAsync(request.Name, role.Scope, cancellationToken);
                if (existingRole != null && existingRole.Id != roleId)
                {
                    return OperationResult<Role>.Failure("Role name is already taken");
                }
                role.Name = request.Name;
            }

            if (!string.IsNullOrEmpty(request.Description))
                role.Description = request.Description;

            role.UpdatedAt = DateTime.UtcNow;
            role.UpdatedBy = request.UpdatedBy;

            await _roleRepository.UpdateAsync(role, cancellationToken);
            await _roleRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated role {RoleId}", roleId);
            return OperationResult<Role>.Success(role);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating role {RoleId}", roleId);
            return OperationResult<Role>.Failure($"Failed to update role: {ex.Message}");
        }
    }

    public async Task<OperationResult> DeleteRoleAsync(string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return OperationResult.Failure("Role not found");
            }

            // TODO: Check if role is in use with UserRole repository
            // For now, just proceed with deletion

            await _roleRepository.DeleteAsync(roleId, cancellationToken);
            await _roleRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted role {RoleId}", roleId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting role {RoleId}", roleId);
            return OperationResult.Failure($"Failed to delete role: {ex.Message}");
        }
    }

    public async Task<OperationResult> AssignPermissionAsync(string roleId, string permissionId, string? assignedBy = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return OperationResult.Failure("Role not found");
            }

            // TODO: Check if permission exists and if role already has permission
            // For now, just add the permission to the role's permissions collection
            var rolePermission = new RolePermission
            {
                RoleId = roleId,
                PermissionId = permissionId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = assignedBy
            };

            role.Permissions.Add(rolePermission);
            await _roleRepository.UpdateAsync(role, cancellationToken);
            await _roleRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Assigned permission {PermissionId} to role {RoleId}", permissionId, roleId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permission {PermissionId} to role {RoleId}", permissionId, roleId);
            return OperationResult.Failure($"Failed to assign permission: {ex.Message}");
        }
    }

    public async Task<OperationResult> RemovePermissionAsync(string roleId, string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return OperationResult.Failure("Role not found");
            }

            var rolePermission = role.Permissions.FirstOrDefault(rp => rp.PermissionId == permissionId);
            if (rolePermission == null)
            {
                return OperationResult.Failure("Role does not have this permission");
            }

            role.Permissions.Remove(rolePermission);
            await _roleRepository.UpdateAsync(role, cancellationToken);
            await _roleRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Removed permission {PermissionId} from role {RoleId}", permissionId, roleId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing permission {PermissionId} from role {RoleId}", permissionId, roleId);
            return OperationResult.Failure($"Failed to remove permission: {ex.Message}");
        }
    }

    public async Task<IReadOnlyList<Permission>> GetRolePermissionsAsync(string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return Array.Empty<Permission>();
            }

            // TODO: Load permissions from role permissions collection
            return role.Permissions.Select(rp => rp.Permission).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions for role {RoleId}", roleId);
            return Array.Empty<Permission>();
        }
    }
}
