namespace NotifyMasterApi.ApplicationCore.Services;

public sealed class NotificationService(IQueueService queueService) : INotificationService
{
    public async Task SendAsync(SendNotificationRequest request)
    {
        var tasks = new List<Task>();

        // Process each channel specified in the request
        foreach (var channel in request.Channels)
        {
            switch (channel.ToLowerInvariant())
            {
                case "email":
                    if (request.Recipients.Any(r => !string.IsNullOrEmpty(r.Email)))
                    {
                        tasks.Add(ProcessEmailNotifications(request));
                    }
                    break;

                case "sms":
                    if (request.Recipients.Any(r => !string.IsNullOrEmpty(r.PhoneNumber)))
                    {
                        tasks.Add(ProcessSmsNotifications(request));
                    }
                    break;

                case "push":
                    if (request.Recipients.Any(r => !string.IsNullOrEmpty(r.DeviceToken)))
                    {
                        tasks.Add(ProcessPushNotifications(request));
                    }
                    break;

                case "webapp":
                    if (request.Recipients.Any(r => !string.IsNullOrEmpty(r.User<PERSON>d)))
                    {
                        tasks.Add(ProcessWebAppNotifications(request));
                    }
                    break;
            }
        }

        await Task.WhenAll(tasks);
    }

    private async Task ProcessEmailNotifications(SendNotificationRequest request)
    {
        // Create email-specific queue messages for each recipient
        foreach (var recipient in request.Recipients.Where(r => !string.IsNullOrEmpty(r.Email)))
        {
            var emailMessage = new
            {
                Id = Guid.NewGuid().ToString(),
                request.TenantId,
                request.UserId,
                request.CorrelationId,
                Recipient = recipient.Email,
                request.Message.Subject,
                request.Message.Content,
                request.Message.From,
                Headers = request.Message.Headers ?? new Dictionary<string, string>(),
                request.Priority,
                ScheduledFor = request.ScheduledAt,
                request.Metadata
            };

            await queueService.PublishAsync("email-queue", emailMessage);
        }
    }

    private async Task ProcessSmsNotifications(SendNotificationRequest request)
    {
        // Create SMS-specific queue messages for each recipient
        foreach (var recipient in request.Recipients.Where(r => !string.IsNullOrEmpty(r.PhoneNumber)))
        {
            var smsMessage = new
            {
                Id = Guid.NewGuid().ToString(),
                request.TenantId,
                request.UserId,
                request.CorrelationId,
                Recipient = recipient.PhoneNumber,
                request.Message.Content,
                request.Message.From,
                request.Priority,
                ScheduledFor = request.ScheduledAt,
                request.Metadata
            };

            await queueService.PublishAsync("sms-queue", smsMessage);
        }
    }

    private async Task ProcessPushNotifications(SendNotificationRequest request)
    {
        // Create push notification queue messages for each recipient
        foreach (var recipient in request.Recipients.Where(r => !string.IsNullOrEmpty(r.DeviceToken)))
        {
            var pushMessage = new
            {
                Id = Guid.NewGuid().ToString(),
                request.TenantId,
                request.UserId,
                request.CorrelationId,
                recipient.DeviceToken,
                Title = request.Message.Subject,
                request.Message.Content,
                Data = request.Message.PushNotificationData,
                request.Priority,
                ScheduledFor = request.ScheduledAt,
                request.Metadata
            };

            await queueService.PublishAsync("push-queue", pushMessage);
        }
    }

    private async Task ProcessWebAppNotifications(SendNotificationRequest request)
    {
        // Create web app notification queue messages for each recipient
        foreach (var recipient in request.Recipients.Where(r => !string.IsNullOrEmpty(r.UserId)))
        {
            var webAppMessage = new
            {
                Id = Guid.NewGuid().ToString(),
                request.TenantId,
                UserId = recipient.UserId,
                request.CorrelationId,
                Title = request.Message.Subject,
                request.Message.Content,
                Data = request.Message.WebAppNotificationData,
                request.Priority,
                ScheduledFor = request.ScheduledAt,
                request.Metadata
            };

            await queueService.PublishAsync("webapp-queue", webAppMessage);
        }
    }
}