// Using statements are handled by GlobalUsings.cs
using NotifyMasterApi.Services.Setup;
using SetupStatus = NotifyMasterApi.Services.Setup.SetupStatus;
using InitializeSystemRequest = NotifyMasterApi.Services.Setup.InitializeSystemRequest;

namespace NotifyMasterApi.Features.Setup;



public class GetSetupStatusEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly ISetupService _setupService;

    public GetSetupStatusEndpoint(ISetupService setupService)
    {
        _setupService = setupService;
    }

    public override void Configure()
    {
        Get("/health/setup-status");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get setup status";
            s.Description = "Check if the system has been initialized";
            s.Responses[200] = "Setup status retrieved successfully";
        });
        Tags("Setup");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        var status = await _setupService.GetSetupStatusAsync();
        await SendOkAsync(status, ct);
    }
}



public class InitializeSystemEndpoint : Endpoint<InitializeSystemRequest, object>
{
    private readonly ISetupService _setupService;
    private readonly ILogger<InitializeSystemEndpoint> _logger;

    public InitializeSystemEndpoint(ISetupService setupService, ILogger<InitializeSystemEndpoint> logger)
    {
        _setupService = setupService;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/setup/initialize");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Initialize system";
            s.Description = "Initialize the notification service system";
            s.Responses[200] = "System initialized successfully";
            s.Responses[400] = "Invalid request or system already initialized";
            s.Responses[500] = "Initialization failed";
        });
        Tags("Setup");
    }

    public override async Task HandleAsync(InitializeSystemRequest req, CancellationToken ct)
    {
        try
        {
            if (await _setupService.IsSystemInitializedAsync())
            {
                await SendAsync(new { Error = "System is already initialized" }, 400, ct);
                return;
            }

            var success = await _setupService.InitializeSystemAsync(req);
            
            if (success)
            {
                await SendOkAsync(new
                {
                    Success = true,
                    Message = "System initialized successfully",
                    RootTenantName = req.RootTenantName,
                    Timestamp = DateTime.UtcNow
                }, ct);
            }
            else
            {
                await SendAsync(new { Error = "Failed to initialize system" }, 500, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during system initialization");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetSystemUsageStatsEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly ILogger<GetSystemUsageStatsEndpoint> _logger;

    public GetSystemUsageStatsEndpoint(ILogger<GetSystemUsageStatsEndpoint> logger)
    {
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/system/usage-stats");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get system usage statistics";
            s.Description = "Get live system metrics including messages per second, plugin usage, and queue depth";
            s.Responses[200] = "Usage statistics retrieved successfully";
        });
        Tags("System");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            // Simulate real-time metrics
            var stats = new
            {
                MessagesPerSecond = new Random().NextDouble() * 100,
                QueueDepth = new Random().Next(0, 1000),
                PluginUsage = new
                {
                    Email = new
                    {
                        CpuUsage = new Random().NextDouble() * 100,
                        MemoryUsageMB = new Random().Next(50, 500),
                        RequestsPerSecond = new Random().NextDouble() * 50
                    },
                    SMS = new
                    {
                        CpuUsage = new Random().NextDouble() * 100,
                        MemoryUsageMB = new Random().Next(30, 300),
                        RequestsPerSecond = new Random().NextDouble() * 30
                    },
                    Push = new
                    {
                        CpuUsage = new Random().NextDouble() * 100,
                        MemoryUsageMB = new Random().Next(40, 400),
                        RequestsPerSecond = new Random().NextDouble() * 40
                    }
                },
                SystemResources = new
                {
                    CpuUsage = new Random().NextDouble() * 100,
                    MemoryUsageMB = new Random().Next(500, 2000),
                    DiskUsageGB = new Random().Next(10, 100),
                    NetworkBytesPerSecond = new Random().Next(1000, 100000)
                },
                Timestamp = DateTime.UtcNow
            };

            await SendOkAsync(stats, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system usage statistics");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetChannelUsageRequest
{
    public string? TenantId { get; set; }
    public string? Period { get; set; } = "day"; // hour, day, week, month
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

public class GetChannelUsageEndpoint : Endpoint<GetChannelUsageRequest, object>
{
    private readonly ILogger<GetChannelUsageEndpoint> _logger;

    public GetChannelUsageEndpoint(ILogger<GetChannelUsageEndpoint> logger)
    {
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/system/channel-usage");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get channel usage breakdown";
            s.Description = "Get per-channel usage statistics with breakdown by period, user, or campaign";
            s.Responses[200] = "Channel usage retrieved successfully";
        });
        Tags("System");
    }

    public override async Task HandleAsync(GetChannelUsageRequest req, CancellationToken ct)
    {
        try
        {
            // Simulate channel usage data
            var usage = new
            {
                TenantId = req.TenantId ?? "all",
                Period = req.Period,
                StartDate = req.StartDate ?? DateTime.UtcNow.AddDays(-7),
                EndDate = req.EndDate ?? DateTime.UtcNow,
                Channels = new
                {
                    Email = new
                    {
                        TotalMessages = new Random().Next(1000, 10000),
                        SuccessfulMessages = new Random().Next(900, 9500),
                        FailedMessages = new Random().Next(10, 500),
                        AverageLatencyMs = new Random().Next(100, 2000),
                        Cost = Math.Round(new Random().NextDouble() * 100, 2),
                        TopProviders = new[]
                        {
                            new { Provider = "SendGrid", Count = new Random().Next(500, 5000) },
                            new { Provider = "Mailgun", Count = new Random().Next(200, 2000) }
                        }
                    },
                    SMS = new
                    {
                        TotalMessages = new Random().Next(500, 5000),
                        SuccessfulMessages = new Random().Next(450, 4800),
                        FailedMessages = new Random().Next(5, 200),
                        AverageLatencyMs = new Random().Next(500, 3000),
                        Cost = Math.Round(new Random().NextDouble() * 200, 2),
                        TopProviders = new[]
                        {
                            new { Provider = "Twilio", Count = new Random().Next(300, 3000) },
                            new { Provider = "BulkSMS", Count = new Random().Next(100, 1000) }
                        }
                    },
                    Push = new
                    {
                        TotalMessages = new Random().Next(2000, 20000),
                        SuccessfulMessages = new Random().Next(1800, 19000),
                        FailedMessages = new Random().Next(50, 1000),
                        AverageLatencyMs = new Random().Next(50, 1000),
                        Cost = Math.Round(new Random().NextDouble() * 50, 2),
                        TopProviders = new[]
                        {
                            new { Provider = "Firebase FCM", Count = new Random().Next(1500, 15000) }
                        }
                    }
                },
                Timestamp = DateTime.UtcNow
            };

            await SendOkAsync(usage, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting channel usage");
            await SendErrorsAsync(500, ct);
        }
    }
}
