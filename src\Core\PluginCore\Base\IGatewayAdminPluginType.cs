namespace PluginCore.Base;

/// <summary>
/// Defines administrative and configuration-related functions for a gateway.
/// </summary>
/// <remarks>
/// This interface defines administrative and configuration-related functions for a gateway.
/// </remarks>
public interface IGatewayAdminPluginType : IPluginType
{
    /// <summary>
    /// Retrieves the current set of configurations for the plugin.
    /// Sensitive values should be masked by the implementation.
    /// </summary>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <param name="queData">Data to be sent to the que</param>
    /// <returns>A list of GatewayConfiguration objects representing the current configuration.</returns>
    Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync( CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates one or more configuration values. The plugin should validate
    /// settings before applying them and return a detailed result.
    /// </summary>
    /// <param name="settingsToUpdate">The list of configuration settings to update.</param>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>An OperationResult indicating the success of the update.</returns>
    Task<OperationResult> UpdateConfigurationAsync(IEnumerable<GatewayConfiguration> settingsToUpdate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs a diagnostic test on the gateway's configuration, such as testing
    /// API key validity and connectivity to the provider endpoint.
    /// </summary>
    /// <param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>An OperationResult indicating the success or failure with detailed error information.</returns>
    Task<OperationResult> TestConfigurationAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Returns the manifest describing the plugin’s metadata and capabilities.
    /// </summary>
    /// <Param name="cancellationToken">A token to cancel the operation.</param>
    /// <returns>A PluginManifest object representing the plugin's metadata and capabilities.</returns>
    Task<PluginManifest> GetManifestAsync(CancellationToken cancellationToken = default);
}
