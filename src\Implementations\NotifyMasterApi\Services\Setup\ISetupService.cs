namespace NotifyMasterApi.Services.Setup;

/// <summary>
/// Core setup service interface for system initialization
/// </summary>
public interface ISetupService
{
    Task<SetupStatus> GetSetupStatusAsync();
    Task<bool> InitializeSystemAsync(InitializeSystemRequest request);
    Task<bool> IsSystemInitializedAsync();
    Task LockSystemAsync();
    Task<ValidationResult> ValidateConfigurationAsync(SetupConfiguration configuration);
}

/// <summary>
/// Setup status information
/// </summary>
public class SetupStatus
{
    public bool IsInitialized { get; set; }
    public bool DatabaseConfigured { get; set; }
    public bool HasRootTenant { get; set; }
    public bool HasAdminUser { get; set; }
    public DateTime? InitializedAt { get; set; }
    public string? Version { get; set; }
    public List<string> RequiredSteps { get; set; } = new();
    public List<string> CompletedSteps { get; set; } = new();
}

/// <summary>
/// System initialization request
/// </summary>
public class InitializeSystemRequest
{
    public string TenantName { get; set; } = string.Empty;
    public string TenantDescription { get; set; } = string.Empty;
    public string TenantDomain { get; set; } = string.Empty;
    public string TenantPlan { get; set; } = "Standard";
    public string AdminEmail { get; set; } = string.Empty;
    public string AdminPassword { get; set; } = string.Empty;
    public string AdminFirstName { get; set; } = string.Empty;
    public string AdminLastName { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
}

/// <summary>
/// Setup configuration for validation
/// </summary>
public class SetupConfiguration
{
    public DatabaseConfiguration Database { get; set; } = new();
    public RedisConfiguration Redis { get; set; } = new();
    public TenantConfiguration Tenant { get; set; } = new();
    public AdminUserConfiguration AdminUser { get; set; } = new();
    public Dictionary<string, object> AdditionalSettings { get; set; } = new();
}

/// <summary>
/// Database configuration
/// </summary>
public class DatabaseConfiguration
{
    public string Provider { get; set; } = "SqlServer"; // SqlServer, PostgreSQL, MySQL, SQLite
    public string ConnectionString { get; set; } = string.Empty;
    public bool TestConnection { get; set; } = true;
    public bool CreateDatabase { get; set; } = true;
    public bool RunMigrations { get; set; } = true;
}

/// <summary>
/// Redis configuration
/// </summary>
public class RedisConfiguration
{
    public string ConnectionString { get; set; } = string.Empty;
    public bool Enabled { get; set; } = true;
    public bool TestConnection { get; set; } = true;
    public int Database { get; set; } = 0;
}

/// <summary>
/// Tenant configuration
/// </summary>
public class TenantConfiguration
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Domain { get; set; } = string.Empty;
    public string Plan { get; set; } = "Standard";
}

/// <summary>
/// Admin user configuration
/// </summary>
public class AdminUserConfiguration
{
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
}

/// <summary>
/// Validation result for setup configuration
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public Dictionary<string, object> ValidationData { get; set; } = new();
}
