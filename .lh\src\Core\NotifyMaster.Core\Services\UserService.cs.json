{"sourceFile": "src/Core/NotifyMaster.Core/Services/UserService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1751233770511, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751233791808, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -470,5 +470,79 @@\n             _logger.LogError(ex, \"Error checking role {RoleName} for user {UserId}\", roleName, userId);\n             return false;\n         }\n     }\n+\n+    public async Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, string? tenantId = null, CancellationToken cancellationToken = default)\n+    {\n+        try\n+        {\n+            var permissions = new List<Permission>();\n+\n+            // Get direct user permissions\n+            var directPermissions = await _context.UserPermissions\n+                .Include(up => up.Permission)\n+                .Where(up => up.UserId == userId)\n+                .Select(up => up.Permission)\n+                .ToListAsync(cancellationToken);\n+\n+            permissions.AddRange(directPermissions);\n+\n+            // Get role-based permissions\n+            var rolePermissions = await _context.UserRoles\n+                .Include(ur => ur.Role)\n+                .ThenInclude(r => r.Permissions)\n+                .ThenInclude(rp => rp.Permission)\n+                .Where(ur => ur.UserId == userId)\n+                .SelectMany(ur => ur.Role.Permissions.Select(rp => rp.Permission))\n+                .ToListAsync(cancellationToken);\n+\n+            permissions.AddRange(rolePermissions);\n+\n+            // Remove duplicates\n+            return permissions.DistinctBy(p => p.Id).ToList();\n+        }\n+        catch (Exception ex)\n+        {\n+            _logger.LogError(ex, \"Error getting permissions for user {UserId}\", userId);\n+            return Array.Empty<Permission>();\n+        }\n+    }\n+\n+    public async Task<IReadOnlyList<Role>> GetUserRolesAsync(string userId, string? tenantId = null, CancellationToken cancellationToken = default)\n+    {\n+        try\n+        {\n+            return await _context.UserRoles\n+                .Include(ur => ur.Role)\n+                .Where(ur => ur.UserId == userId)\n+                .Select(ur => ur.Role)\n+                .ToListAsync(cancellationToken);\n+        }\n+        catch (Exception ex)\n+        {\n+            _logger.LogError(ex, \"Error getting roles for user {UserId}\", userId);\n+            return Array.Empty<Role>();\n+        }\n+    }\n+\n+    // Interface-compliant overloads with tenantId parameter\n+    public async Task<OperationResult> AssignRoleAsync(string userId, string roleId, string? tenantId = null, string? assignedBy = null, CancellationToken cancellationToken = default)\n+    {\n+        return await AssignRoleAsync(userId, roleId, assignedBy, cancellationToken);\n+    }\n+\n+    public async Task<OperationResult> RemoveRoleAsync(string userId, string roleId, string? tenantId = null, CancellationToken cancellationToken = default)\n+    {\n+        return await RemoveRoleAsync(userId, roleId, cancellationToken);\n+    }\n+\n+    public async Task<OperationResult> GrantPermissionAsync(string userId, string permissionId, string? tenantId = null, string? grantedBy = null, CancellationToken cancellationToken = default)\n+    {\n+        return await GrantPermissionAsync(userId, permissionId, grantedBy, cancellationToken);\n+    }\n+\n+    public async Task<OperationResult> RevokePermissionAsync(string userId, string permissionId, string? tenantId = null, CancellationToken cancellationToken = default)\n+    {\n+        return await RevokePermissionAsync(userId, permissionId, cancellationToken);\n+    }\n }\n"}, {"date": 1751235653340, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -524,8 +524,61 @@\n             return Array.Empty<Role>();\n         }\n     }\n \n+    public async Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default)\n+    {\n+        try\n+        {\n+            // Get user by email and tenant\n+            var user = await _context.Users\n+                .Include(u => u.Tenant)\n+                .Include(u => u.Roles).ThenInclude(ur => ur.Role)\n+                .FirstOrDefaultAsync(u => u.Email == email && u.TenantId == tenantId, cancellationToken);\n+\n+            if (user == null)\n+            {\n+                return OperationResult<AuthenticationResult>.Failure(\"Invalid email or password\");\n+            }\n+\n+            if (!user.IsActive)\n+            {\n+                return OperationResult<AuthenticationResult>.Failure(\"User account is disabled\");\n+            }\n+\n+            // Verify password using authentication service\n+            var passwordValid = await _authService.VerifyPasswordAsync(user.Id, password, cancellationToken);\n+            if (!passwordValid)\n+            {\n+                return OperationResult<AuthenticationResult>.Failure(\"Invalid email or password\");\n+            }\n+\n+            // Update last login\n+            user.LastLoginAt = DateTime.UtcNow;\n+            await _context.SaveChangesAsync(cancellationToken);\n+\n+            // Create authentication result\n+            var result = new AuthenticationResult\n+            {\n+                UserId = user.Id,\n+                Email = user.Email,\n+                FirstName = user.FirstName,\n+                LastName = user.LastName,\n+                TenantId = user.TenantId,\n+                Roles = user.Roles.Select(ur => ur.Role.Name).ToList(),\n+                IsAuthenticated = true\n+            };\n+\n+            _logger.LogInformation(\"User {UserId} authenticated successfully\", user.Id);\n+            return OperationResult<AuthenticationResult>.Success(result);\n+        }\n+        catch (Exception ex)\n+        {\n+            _logger.LogError(ex, \"Error authenticating user {Email} for tenant {TenantId}\", email, tenantId);\n+            return OperationResult<AuthenticationResult>.Failure(\"Authentication failed\");\n+        }\n+    }\n+\n     // Interface-compliant overloads with tenantId parameter\n     public async Task<OperationResult> AssignRoleAsync(string userId, string roleId, string? tenantId = null, string? assignedBy = null, CancellationToken cancellationToken = default)\n     {\n         return await AssignRoleAsync(userId, roleId, assignedBy, cancellationToken);\n"}, {"date": 1751235690671, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -555,18 +555,21 @@\n             // Update last login\n             user.LastLoginAt = DateTime.UtcNow;\n             await _context.SaveChangesAsync(cancellationToken);\n \n+            // Get user roles and permissions\n+            var roles = user.Roles.Select(ur => ur.Role).ToList();\n+            var permissions = await GetUserPermissionsAsync(user.Id, tenantId, cancellationToken);\n+\n             // Create authentication result\n             var result = new AuthenticationResult\n             {\n-                UserId = user.Id,\n-                Email = user.Email,\n-                FirstName = user.FirstName,\n-                LastName = user.LastName,\n-                TenantId = user.TenantId,\n-                Roles = user.Roles.Select(ur => ur.Role.Name).ToList(),\n-                IsAuthenticated = true\n+                User = user,\n+                Token = string.Empty, // Token generation should be handled by AuthenticationService\n+                ExpiresAt = DateTime.UtcNow.AddHours(24), // Default expiration\n+                RefreshToken = string.Empty, // Refresh token generation should be handled by AuthenticationService\n+                Roles = roles,\n+                Permissions = permissions\n             };\n \n             _logger.LogInformation(\"User {UserId} authenticated successfully\", user.Id);\n             return OperationResult<AuthenticationResult>.Success(result);\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -545,9 +545,9 @@\n                 return OperationResult<AuthenticationResult>.Failure(\"User account is disabled\");\n             }\n \n             // Verify password using authentication service\n-            var passwordValid = await _authService.VerifyPasswordAsync(user.Id, password, cancellationToken);\n+            var passwordValid = _authService.VerifyPassword(password, user.PasswordHash);\n             if (!passwordValid)\n             {\n                 return OperationResult<AuthenticationResult>.Failure(\"Invalid email or password\");\n             }\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,14 +6,14 @@\n /// Implementation of user management service using Entity Framework\n /// </summary>\n public class UserService : IUserService\n {\n-    private readonly NotifyMasterDbContext _context;\n+    private readonly INotifyMasterDbContext _context;\n     private readonly IAuthenticationService _authService;\n     private readonly ILogger<UserService> _logger;\n \n     public UserService(\n-        NotifyMasterDbContext context, \n+        INotifyMasterDbContext context,\n         IAuthenticationService authService,\n         ILogger<UserService> logger)\n     {\n         _context = context;\n"}, {"date": 1751238757430, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,22 +2,22 @@\n \n namespace NotifyMaster.Core.Services;\n \n /// <summary>\n-/// Implementation of user management service using Entity Framework\n+/// Implementation of user management service using repositories\n /// </summary>\n public class UserService : IUserService\n {\n-    private readonly INotifyMasterDbContext _context;\n+    private readonly IUserRepository _userRepository;\n     private readonly IAuthenticationService _authService;\n     private readonly ILogger<UserService> _logger;\n \n     public UserService(\n-        INotifyMasterDbContext context,\n+        IUserRepository userRepository,\n         IAuthenticationService authService,\n         ILogger<UserService> logger)\n     {\n-        _context = context;\n+        _userRepository = userRepository;\n         _authService = authService;\n         _logger = logger;\n     }\n \n"}], "date": 1751233770511, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Core.Services;\n\n/// <summary>\n/// Implementation of user management service using Entity Framework\n/// </summary>\npublic class UserService : IUserService\n{\n    private readonly NotifyMasterDbContext _context;\n    private readonly IAuthenticationService _authService;\n    private readonly ILogger<UserService> _logger;\n\n    public UserService(\n        NotifyMasterDbContext context, \n        IAuthenticationService authService,\n        ILogger<UserService> logger)\n    {\n        _context = context;\n        _authService = authService;\n        _logger = logger;\n    }\n\n    public async Task<User?> GetUserAsync(string userId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Users\n                .Include(u => u.Tenant)\n                .Include(u => u.Roles).ThenInclude(ur => ur.Role)\n                .Include(u => u.Permissions).ThenInclude(up => up.Permission)\n                .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting user {UserId}\", userId);\n            return null;\n        }\n    }\n\n    public async Task<User?> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Users\n                .Include(u => u.Tenant)\n                .Include(u => u.Roles).ThenInclude(ur => ur.Role)\n                .Include(u => u.Permissions).ThenInclude(up => up.Permission)\n                .FirstOrDefaultAsync(u => u.Email == email, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting user by email {Email}\", email);\n            return null;\n        }\n    }\n\n    public async Task<User?> GetUserByEmailAsync(string tenantId, string email, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Users\n                .Include(u => u.Tenant)\n                .Include(u => u.Roles).ThenInclude(ur => ur.Role)\n                .Include(u => u.Permissions).ThenInclude(up => up.Permission)\n                .FirstOrDefaultAsync(u => u.TenantId == tenantId && u.Email == email, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting user by email {Email} in tenant {TenantId}\", email, tenantId);\n            return null;\n        }\n    }\n\n    public async Task<User?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Users\n                .Include(u => u.Tenant)\n                .Include(u => u.Roles).ThenInclude(ur => ur.Role)\n                .Include(u => u.Permissions).ThenInclude(up => up.Permission)\n                .FirstOrDefaultAsync(u => u.Username == username, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting user by username {Username}\", username);\n            return null;\n        }\n    }\n\n    public async Task<IReadOnlyList<User>> GetUsersByTenantAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Users\n                .Where(u => u.TenantId == tenantId)\n                .Include(u => u.Roles).ThenInclude(ur => ur.Role)\n                .OrderBy(u => u.Username)\n                .Skip(skip)\n                .Take(take)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting users for tenant {TenantId}\", tenantId);\n            return Array.Empty<User>();\n        }\n    }\n\n    public async Task<IReadOnlyList<User>> GetTenantUsersAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Users\n                .Where(u => u.TenantId == tenantId)\n                .Include(u => u.Roles).ThenInclude(ur => ur.Role)\n                .OrderBy(u => u.Username)\n                .Skip(skip)\n                .Take(take)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting users for tenant {TenantId}\", tenantId);\n            return Array.Empty<User>();\n        }\n    }\n\n    public async Task<OperationResult<User>> CreateUserAsync(CreateUserRequest request, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Check if user already exists\n            var existingUser = await _context.Users\n                .FirstOrDefaultAsync(u => u.TenantId == request.TenantId && \n                                         (u.Email == request.Email || u.Username == request.Username), cancellationToken);\n\n            if (existingUser != null)\n            {\n                return OperationResult<User>.Failure(\"A user with this email or username already exists\");\n            }\n\n            // Hash password\n            var passwordHash = await _authService.HashPasswordAsync(request.Password);\n\n            var user = new User\n            {\n                Id = Guid.NewGuid().ToString(),\n                TenantId = request.TenantId,\n                Username = request.Username,\n                Email = request.Email,\n                FirstName = request.FirstName,\n                LastName = request.LastName,\n                PasswordHash = passwordHash,\n                IsActive = true,\n                CreatedAt = DateTime.UtcNow,\n                CreatedBy = request.CreatedBy\n            };\n\n            _context.Users.Add(user);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Created user {UserId} ({Email}) in tenant {TenantId}\", user.Id, user.Email, user.TenantId);\n            return OperationResult<User>.Success(user);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error creating user {Email} in tenant {TenantId}\", request.Email, request.TenantId);\n            return OperationResult<User>.Failure($\"Failed to create user: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult<User>> UpdateUserAsync(string userId, UpdateUserRequest request, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var user = await _context.Users.FindAsync(userId);\n            if (user == null)\n            {\n                return OperationResult<User>.Failure(\"User not found\");\n            }\n\n            // Update fields if provided\n            if (!string.IsNullOrEmpty(request.Username))\n            {\n                // Check if username is already taken\n                var existingUser = await _context.Users\n                    .FirstOrDefaultAsync(u => u.Id != userId && u.TenantId == user.TenantId && u.Username == request.Username, cancellationToken);\n                if (existingUser != null)\n                {\n                    return OperationResult<User>.Failure(\"Username is already taken\");\n                }\n                user.Username = request.Username;\n            }\n\n            if (!string.IsNullOrEmpty(request.Email))\n            {\n                // Check if email is already taken\n                var existingUser = await _context.Users\n                    .FirstOrDefaultAsync(u => u.Id != userId && u.TenantId == user.TenantId && u.Email == request.Email, cancellationToken);\n                if (existingUser != null)\n                {\n                    return OperationResult<User>.Failure(\"Email is already taken\");\n                }\n                user.Email = request.Email;\n            }\n\n            if (!string.IsNullOrEmpty(request.FirstName))\n                user.FirstName = request.FirstName;\n\n            if (!string.IsNullOrEmpty(request.LastName))\n                user.LastName = request.LastName;\n\n            if (request.IsActive.HasValue)\n                user.IsActive = request.IsActive.Value;\n\n            user.UpdatedAt = DateTime.UtcNow;\n            user.UpdatedBy = request.UpdatedBy;\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Updated user {UserId}\", userId);\n            return OperationResult<User>.Success(user);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error updating user {UserId}\", userId);\n            return OperationResult<User>.Failure($\"Failed to update user: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> DeleteUserAsync(string userId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var user = await _context.Users.FindAsync(userId);\n            if (user == null)\n            {\n                return OperationResult.Failure(\"User not found\");\n            }\n\n            _context.Users.Remove(user);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Deleted user {UserId}\", userId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error deleting user {UserId}\", userId);\n            return OperationResult.Failure($\"Failed to delete user: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> ChangePasswordAsync(string userId, string currentPassword, string newPassword, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var user = await _context.Users.FindAsync(userId);\n            if (user == null)\n            {\n                return OperationResult.Failure(\"User not found\");\n            }\n\n            // Verify current password\n            var isCurrentPasswordValid = await _authService.VerifyPasswordAsync(currentPassword, user.PasswordHash);\n            if (!isCurrentPasswordValid)\n            {\n                return OperationResult.Failure(\"Current password is incorrect\");\n            }\n\n            // Hash new password\n            user.PasswordHash = await _authService.HashPasswordAsync(newPassword);\n            user.UpdatedAt = DateTime.UtcNow;\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Changed password for user {UserId}\", userId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error changing password for user {UserId}\", userId);\n            return OperationResult.Failure($\"Failed to change password: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> AssignRoleAsync(string userId, string roleId, string? assignedBy = null, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var user = await _context.Users.FindAsync(userId);\n            if (user == null)\n            {\n                return OperationResult.Failure(\"User not found\");\n            }\n\n            var role = await _context.Roles.FindAsync(roleId);\n            if (role == null)\n            {\n                return OperationResult.Failure(\"Role not found\");\n            }\n\n            var existingUserRole = await _context.UserRoles\n                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId, cancellationToken);\n\n            if (existingUserRole != null)\n            {\n                return OperationResult.Failure(\"User already has this role\");\n            }\n\n            _context.UserRoles.Add(new UserRole\n            {\n                UserId = userId,\n                RoleId = roleId,\n                TenantId = user.TenantId,\n                AssignedAt = DateTime.UtcNow,\n                AssignedBy = assignedBy\n            });\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Assigned role {RoleId} to user {UserId}\", roleId, userId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error assigning role {RoleId} to user {UserId}\", roleId, userId);\n            return OperationResult.Failure($\"Failed to assign role: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> RemoveRoleAsync(string userId, string roleId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var userRole = await _context.UserRoles\n                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId, cancellationToken);\n\n            if (userRole == null)\n            {\n                return OperationResult.Failure(\"User does not have this role\");\n            }\n\n            _context.UserRoles.Remove(userRole);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Removed role {RoleId} from user {UserId}\", roleId, userId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error removing role {RoleId} from user {UserId}\", roleId, userId);\n            return OperationResult.Failure($\"Failed to remove role: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> GrantPermissionAsync(string userId, string permissionId, string? grantedBy = null, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var user = await _context.Users.FindAsync(userId);\n            if (user == null)\n            {\n                return OperationResult.Failure(\"User not found\");\n            }\n\n            var permission = await _context.Permissions.FindAsync(permissionId);\n            if (permission == null)\n            {\n                return OperationResult.Failure(\"Permission not found\");\n            }\n\n            var existingUserPermission = await _context.UserPermissions\n                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId, cancellationToken);\n\n            if (existingUserPermission != null)\n            {\n                return OperationResult.Failure(\"User already has this permission\");\n            }\n\n            _context.UserPermissions.Add(new UserPermission\n            {\n                UserId = userId,\n                PermissionId = permissionId,\n                TenantId = user.TenantId,\n                GrantedAt = DateTime.UtcNow,\n                GrantedBy = grantedBy\n            });\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Granted permission {PermissionId} to user {UserId}\", permissionId, userId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error granting permission {PermissionId} to user {UserId}\", permissionId, userId);\n            return OperationResult.Failure($\"Failed to grant permission: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> RevokePermissionAsync(string userId, string permissionId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var userPermission = await _context.UserPermissions\n                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId, cancellationToken);\n\n            if (userPermission == null)\n            {\n                return OperationResult.Failure(\"User does not have this permission\");\n            }\n\n            _context.UserPermissions.Remove(userPermission);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Revoked permission {PermissionId} from user {UserId}\", permissionId, userId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error revoking permission {PermissionId} from user {UserId}\", permissionId, userId);\n            return OperationResult.Failure($\"Failed to revoke permission: {ex.Message}\");\n        }\n    }\n\n    public async Task<bool> HasPermissionAsync(string userId, string resource, string action, string? tenantId = null, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Check direct user permissions\n            var hasDirectPermission = await _context.UserPermissions\n                .Include(up => up.Permission)\n                .AnyAsync(up => up.UserId == userId &&\n                               up.Permission.Resource == resource &&\n                               up.Permission.Action == action, cancellationToken);\n\n            if (hasDirectPermission) return true;\n\n            // Check role-based permissions\n            var hasRolePermission = await _context.UserRoles\n                .Include(ur => ur.Role)\n                .ThenInclude(r => r.Permissions)\n                .ThenInclude(rp => rp.Permission)\n                .AnyAsync(ur => ur.UserId == userId &&\n                               ur.Role.Permissions.Any(rp => rp.Permission.Resource == resource &&\n                                                             rp.Permission.Action == action), cancellationToken);\n\n            return hasRolePermission;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error checking permission {Resource}:{Action} for user {UserId}\", resource, action, userId);\n            return false;\n        }\n    }\n\n    public async Task<bool> HasRoleAsync(string userId, string roleName, string? tenantId = null, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.UserRoles\n                .Include(ur => ur.Role)\n                .AnyAsync(ur => ur.UserId == userId && ur.Role.Name == roleName, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error checking role {RoleName} for user {UserId}\", roleName, userId);\n            return false;\n        }\n    }\n}\n"}]}