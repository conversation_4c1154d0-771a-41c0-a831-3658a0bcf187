{"sourceFile": "src/Core/PluginCore/Services/AuthenticationService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1751224502996, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751225231695, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,15 +1,5 @@\n-using Microsoft.Extensions.Configuration;\n-using Microsoft.Extensions.DependencyInjection;\n-using Microsoft.Extensions.Logging;\n-using Microsoft.Extensions.Options;\n-using Microsoft.IdentityModel.Tokens;\n-using PluginCore.Interfaces;\n-using PluginCore.Models;\n-using System.IdentityModel.Tokens.Jwt;\n-using System.Security.Claims;\n-using System.Security.Cryptography;\n-using System.Text;\n+// Using statements are handled by GlobalUsings.cs\n \n namespace PluginCore.Services;\n \n /// <summary>\n"}, {"date": 1751229152498, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -279,9 +279,9 @@\n     /// </summary>\n     public static IServiceCollection AddAuthenticationServices(this IServiceCollection services, IConfiguration configuration)\n     {\n         // Configure JWT options\n-        services.Configure<JwtOptions>(configuration.GetSection(\"Jwt\"));\n+        services.Configure<JwtOptions>(options => configuration.GetSection(\"Jwt\").Bind(options));\n         \n         // Add authentication service\n         services.AddScoped<IAuthenticationService, AuthenticationService>();\n         \n"}], "date": 1751224502996, "name": "Commit-0", "content": "using Microsoft.Extensions.Configuration;\nusing Microsoft.Extensions.DependencyInjection;\nusing Microsoft.Extensions.Logging;\nusing Microsoft.Extensions.Options;\nusing Microsoft.IdentityModel.Tokens;\nusing PluginCore.Interfaces;\nusing PluginCore.Models;\nusing System.IdentityModel.Tokens.Jwt;\nusing System.Security.Claims;\nusing System.Security.Cryptography;\nusing System.Text;\n\nnamespace PluginCore.Services;\n\n/// <summary>\n/// JWT configuration options\n/// </summary>\npublic class JwtOptions\n{\n    public string SecretKey { get; set; } = string.Empty;\n    public string Issuer { get; set; } = \"NotifyMaster\";\n    public string Audience { get; set; } = \"NotifyMaster\";\n    public int ExpirationMinutes { get; set; } = 60;\n    public int RefreshTokenExpirationDays { get; set; } = 7;\n}\n\n/// <summary>\n/// Interface for authentication service\n/// </summary>\npublic interface IAuthenticationService\n{\n    /// <summary>\n    /// Authenticates a user and returns JWT token\n    /// </summary>\n    Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Refreshes an authentication token\n    /// </summary>\n    Task<OperationResult<AuthenticationResult>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Validates a JWT token\n    /// </summary>\n    Task<OperationResult<ClaimsPrincipal>> ValidateTokenAsync(string token, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Revokes a refresh token\n    /// </summary>\n    Task<OperationResult> RevokeTokenAsync(string refreshToken, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Generates a JWT token for a user\n    /// </summary>\n    Task<string> GenerateTokenAsync(User user, IReadOnlyList<Role> roles, IReadOnlyList<Permission> permissions);\n    \n    /// <summary>\n    /// Generates a refresh token\n    /// </summary>\n    string GenerateRefreshToken();\n    \n    /// <summary>\n    /// Hashes a password\n    /// </summary>\n    string HashPassword(string password);\n    \n    /// <summary>\n    /// Verifies a password against a hash\n    /// </summary>\n    bool VerifyPassword(string password, string hash);\n}\n\n/// <summary>\n/// Implementation of authentication service\n/// </summary>\npublic class AuthenticationService : IAuthenticationService\n{\n    private readonly ILogger<AuthenticationService> _logger;\n    private readonly IUserService _userService;\n    private readonly IRoleService _roleService;\n    private readonly IOptions<JwtOptions> _jwtOptions;\n    private readonly TokenValidationParameters _tokenValidationParameters;\n\n    public AuthenticationService(\n        ILogger<AuthenticationService> logger,\n        IUserService userService,\n        IRoleService roleService,\n        IOptions<JwtOptions> jwtOptions)\n    {\n        _logger = logger;\n        _userService = userService;\n        _roleService = roleService;\n        _jwtOptions = jwtOptions;\n        \n        _tokenValidationParameters = new TokenValidationParameters\n        {\n            ValidateIssuerSigningKey = true,\n            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtOptions.Value.SecretKey)),\n            ValidateIssuer = true,\n            ValidIssuer = _jwtOptions.Value.Issuer,\n            ValidateAudience = true,\n            ValidAudience = _jwtOptions.Value.Audience,\n            ValidateLifetime = true,\n            ClockSkew = TimeSpan.Zero\n        };\n    }\n\n    public async Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Get user by email within tenant\n            var user = await _userService.GetUserByEmailAsync(tenantId, email, cancellationToken);\n            if (user == null)\n            {\n                _logger.LogWarning(\"Authentication failed: User not found for email {Email} in tenant {TenantId}\", email, tenantId);\n                return OperationResult<AuthenticationResult>.Failure(\"Invalid email or password\");\n            }\n\n            // Check if user is active\n            if (user.Status != UserStatus.Active)\n            {\n                _logger.LogWarning(\"Authentication failed: User {UserId} is not active (Status: {Status})\", user.Id, user.Status);\n                return OperationResult<AuthenticationResult>.Failure(\"User account is not active\");\n            }\n\n            // Verify password\n            if (!VerifyPassword(password, user.PasswordHash))\n            {\n                _logger.LogWarning(\"Authentication failed: Invalid password for user {UserId}\", user.Id);\n                return OperationResult<AuthenticationResult>.Failure(\"Invalid email or password\");\n            }\n\n            // Get user roles and permissions\n            var roles = await _userService.GetUserRolesAsync(user.Id, tenantId, cancellationToken);\n            var permissions = await _userService.GetUserPermissionsAsync(user.Id, tenantId, cancellationToken);\n\n            // Generate tokens\n            var token = await GenerateTokenAsync(user, roles, permissions);\n            var refreshToken = GenerateRefreshToken();\n\n            // Update last login time\n            await _userService.UpdateUserAsync(user.Id, new UpdateUserRequest(), cancellationToken);\n\n            var result = new AuthenticationResult\n            {\n                User = user,\n                Token = token,\n                ExpiresAt = DateTime.UtcNow.AddMinutes(_jwtOptions.Value.ExpirationMinutes),\n                RefreshToken = refreshToken,\n                Roles = roles,\n                Permissions = permissions\n            };\n\n            _logger.LogInformation(\"User {UserId} authenticated successfully\", user.Id);\n            return OperationResult<AuthenticationResult>.Success(result);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error during authentication for email {Email} in tenant {TenantId}\", email, tenantId);\n            return OperationResult<AuthenticationResult>.Failure(\"Authentication failed\");\n        }\n    }\n\n    public async Task<OperationResult<AuthenticationResult>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // In a real implementation, you would validate the refresh token against a database\n            // For now, we'll just return a failure\n            _logger.LogWarning(\"Refresh token validation not implemented\");\n            return OperationResult<AuthenticationResult>.Failure(\"Refresh token is invalid or expired\");\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error during token refresh\");\n            return OperationResult<AuthenticationResult>.Failure(\"Token refresh failed\");\n        }\n    }\n\n    public async Task<OperationResult<ClaimsPrincipal>> ValidateTokenAsync(string token, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tokenHandler = new JwtSecurityTokenHandler();\n            var principal = tokenHandler.ValidateToken(token, _tokenValidationParameters, out var validatedToken);\n            \n            return OperationResult<ClaimsPrincipal>.Success(principal);\n        }\n        catch (SecurityTokenException ex)\n        {\n            _logger.LogWarning(ex, \"Token validation failed\");\n            return OperationResult<ClaimsPrincipal>.Failure(\"Invalid token\");\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error during token validation\");\n            return OperationResult<ClaimsPrincipal>.Failure(\"Token validation failed\");\n        }\n    }\n\n    public async Task<OperationResult> RevokeTokenAsync(string refreshToken, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // In a real implementation, you would mark the refresh token as revoked in the database\n            _logger.LogInformation(\"Refresh token revoked\");\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error during token revocation\");\n            return OperationResult.Failure(\"Token revocation failed\");\n        }\n    }\n\n    public async Task<string> GenerateTokenAsync(User user, IReadOnlyList<Role> roles, IReadOnlyList<Permission> permissions)\n    {\n        var claims = new List<Claim>\n        {\n            new(ClaimTypes.NameIdentifier, user.Id),\n            new(ClaimTypes.Name, user.Username),\n            new(ClaimTypes.Email, user.Email),\n            new(\"tenant_id\", user.TenantId),\n            new(\"user_id\", user.Id),\n            new(\"first_name\", user.FirstName ?? string.Empty),\n            new(\"last_name\", user.LastName ?? string.Empty)\n        };\n\n        // Add role claims\n        foreach (var role in roles)\n        {\n            claims.Add(new Claim(ClaimTypes.Role, role.Name));\n        }\n\n        // Add permission claims\n        foreach (var permission in permissions)\n        {\n            claims.Add(new Claim(\"permission\", $\"{permission.Resource}:{permission.Action}\"));\n        }\n\n        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtOptions.Value.SecretKey));\n        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);\n\n        var token = new JwtSecurityToken(\n            issuer: _jwtOptions.Value.Issuer,\n            audience: _jwtOptions.Value.Audience,\n            claims: claims,\n            expires: DateTime.UtcNow.AddMinutes(_jwtOptions.Value.ExpirationMinutes),\n            signingCredentials: credentials\n        );\n\n        return new JwtSecurityTokenHandler().WriteToken(token);\n    }\n\n    public string GenerateRefreshToken()\n    {\n        var randomBytes = new byte[64];\n        using var rng = RandomNumberGenerator.Create();\n        rng.GetBytes(randomBytes);\n        return Convert.ToBase64String(randomBytes);\n    }\n\n    public string HashPassword(string password)\n    {\n        return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt());\n    }\n\n    public bool VerifyPassword(string password, string hash)\n    {\n        try\n        {\n            return BCrypt.Net.BCrypt.Verify(password, hash);\n        }\n        catch\n        {\n            return false;\n        }\n    }\n}\n\n/// <summary>\n/// Extension methods for authentication services\n/// </summary>\npublic static class AuthenticationServiceExtensions\n{\n    /// <summary>\n    /// Adds authentication services to the service collection\n    /// </summary>\n    public static IServiceCollection AddAuthenticationServices(this IServiceCollection services, IConfiguration configuration)\n    {\n        // Configure JWT options\n        services.Configure<JwtOptions>(configuration.GetSection(\"Jwt\"));\n        \n        // Add authentication service\n        services.AddScoped<IAuthenticationService, AuthenticationService>();\n        \n        return services;\n    }\n}\n"}]}