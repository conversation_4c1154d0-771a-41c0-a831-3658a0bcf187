// Using statements are handled by GlobalUsings.cs
using Terminal.Gui;

namespace NotifyMasterApi.Features.Setup.TUI;

/// <summary>
/// Setup step implementations for the TUI wizard
/// </summary>
public static class SetupSteps
{
    public static void ShowWelcomeStep(FrameView frame, SetupWizardData data)
    {
        var welcomeText = new TextView()
        {
            X = 1,
            Y = 1,
            Width = Dim.Fill() - 2,
            Height = Dim.Fill() - 2,
            ReadOnly = true,
            Text = @"
🚀 Welcome to NotifyMaster Setup Wizard!

This wizard will help you configure your NotifyMaster instance for first use.

We'll guide you through:

📊 Database Configuration
   Configure your primary database connection (SQL Server, PostgreSQL, etc.)

🔄 Redis Configuration  
   Set up Redis for caching and queue management

🏢 First Tenant Setup
   Create your organization's tenant

👤 Admin User Creation
   Set up your administrator account

The setup process typically takes 2-3 minutes to complete.

Click 'Next' to begin the configuration process.

💡 Tip: You can cancel at any time and restart the wizard later.
"
        };

        frame.Add(welcomeText);
    }

    public static void ShowDatabaseStep(FrameView frame, SetupWizardData data)
    {
        var label = new Label("Configure your database connection:")
        {
            X = 1,
            Y = 1
        };

        var providerLabel = new Label("Database Provider:")
        {
            X = 1,
            Y = 3
        };

        var providerRadio = new RadioGroup(new[] { "SQL Server", "PostgreSQL", "SQLite" })
        {
            X = 1,
            Y = 4,
            SelectedItem = 0
        };

        var serverLabel = new Label("Server:")
        {
            X = 1,
            Y = 8
        };

        var serverField = new TextField("localhost")
        {
            X = 15,
            Y = 8,
            Width = 30
        };

        var databaseLabel = new Label("Database:")
        {
            X = 1,
            Y = 10
        };

        var databaseField = new TextField("NotifyMaster")
        {
            X = 15,
            Y = 10,
            Width = 30
        };

        var userLabel = new Label("Username:")
        {
            X = 1,
            Y = 12
        };

        var userField = new TextField("")
        {
            X = 15,
            Y = 12,
            Width = 30
        };

        var passwordLabel = new Label("Password:")
        {
            X = 1,
            Y = 14
        };

        var passwordField = new TextField("")
        {
            X = 15,
            Y = 14,
            Width = 30,
            Secret = true
        };

        var testButton = new Button("Test Connection")
        {
            X = 1,
            Y = 16
        };

        var statusLabel = new Label("")
        {
            X = 20,
            Y = 16,
            Width = 40
        };

        testButton.Clicked += () =>
        {
            statusLabel.Text = "🔄 Testing connection...";
            Application.Refresh();
            
            // Simulate connection test
            Task.Run(async () =>
            {
                await Task.Delay(1000);
                Application.MainLoop.Invoke(() =>
                {
                    statusLabel.Text = "✅ Connection successful!";
                    statusLabel.ColorScheme = Colors.Dialog;
                    
                    // Build connection string
                    var provider = providerRadio.SelectedItem;
                    var server = serverField.Text.ToString();
                    var database = databaseField.Text.ToString();
                    var username = userField.Text.ToString();
                    var password = passwordField.Text.ToString();
                    
                    data.DatabaseConnectionString = provider switch
                    {
                        0 => $"Server={server};Database={database};User Id={username};Password={password};TrustServerCertificate=true;",
                        1 => $"Host={server};Database={database};Username={username};Password={password};",
                        2 => $"Data Source={database}.db;",
                        _ => ""
                    };
                });
            });
        };

        frame.Add(label, providerLabel, providerRadio, serverLabel, serverField, 
                 databaseLabel, databaseField, userLabel, userField, 
                 passwordLabel, passwordField, testButton, statusLabel);
    }

    public static void ShowRedisStep(FrameView frame, SetupWizardData data)
    {
        var label = new Label("Configure Redis for caching and queues:")
        {
            X = 1,
            Y = 1
        };

        var hostLabel = new Label("Redis Host:")
        {
            X = 1,
            Y = 3
        };

        var hostField = new TextField("localhost")
        {
            X = 15,
            Y = 3,
            Width = 30
        };

        var portLabel = new Label("Port:")
        {
            X = 1,
            Y = 5
        };

        var portField = new TextField("6379")
        {
            X = 15,
            Y = 5,
            Width = 10
        };

        var passwordLabel = new Label("Password:")
        {
            X = 1,
            Y = 7
        };

        var passwordField = new TextField("")
        {
            X = 15,
            Y = 7,
            Width = 30,
            Secret = true
        };

        var databaseLabel = new Label("Database:")
        {
            X = 1,
            Y = 9
        };

        var databaseField = new TextField("0")
        {
            X = 15,
            Y = 9,
            Width = 5
        };

        var testButton = new Button("Test Connection")
        {
            X = 1,
            Y = 11
        };

        var statusLabel = new Label("")
        {
            X = 20,
            Y = 11,
            Width = 40
        };

        testButton.Clicked += () =>
        {
            statusLabel.Text = "🔄 Testing Redis connection...";
            Application.Refresh();
            
            Task.Run(async () =>
            {
                await Task.Delay(1000);
                Application.MainLoop.Invoke(() =>
                {
                    statusLabel.Text = "✅ Redis connection successful!";
                    statusLabel.ColorScheme = Colors.Dialog;
                    
                    // Build Redis connection string
                    var host = hostField.Text.ToString();
                    var port = portField.Text.ToString();
                    var password = passwordField.Text.ToString();
                    var database = databaseField.Text.ToString();
                    
                    var connectionString = $"{host}:{port}";
                    if (!string.IsNullOrEmpty(password))
                        connectionString += $",password={password}";
                    if (database != "0")
                        connectionString += $",defaultDatabase={database}";
                    
                    data.RedisConnectionString = connectionString;
                });
            });
        };

        var infoText = new TextView()
        {
            X = 1,
            Y = 13,
            Width = Dim.Fill() - 2,
            Height = 5,
            ReadOnly = true,
            Text = @"
💡 Redis is used for:
• Message queuing and processing
• Caching frequently accessed data
• Real-time notifications and events
• Session management

If you don't have Redis installed, you can use Docker:
docker run -d -p 6379:6379 redis:alpine
"
        };

        frame.Add(label, hostLabel, hostField, portLabel, portField, 
                 passwordLabel, passwordField, databaseLabel, databaseField, 
                 testButton, statusLabel, infoText);
    }

    public static void ShowTenantStep(FrameView frame, SetupWizardData data)
    {
        var label = new Label("Create your organization's tenant:")
        {
            X = 1,
            Y = 1
        };

        var nameLabel = new Label("Organization Name:")
        {
            X = 1,
            Y = 3
        };

        var nameField = new TextField("My Organization")
        {
            X = 20,
            Y = 3,
            Width = 40
        };

        var domainLabel = new Label("Domain:")
        {
            X = 1,
            Y = 5
        };

        var domainField = new TextField("myorg")
        {
            X = 20,
            Y = 5,
            Width = 20
        };

        var descLabel = new Label("Description:")
        {
            X = 1,
            Y = 7
        };

        var descField = new TextField("Default organization for NotifyMaster")
        {
            X = 20,
            Y = 7,
            Width = 40
        };

        var infoText = new TextView()
        {
            X = 1,
            Y = 9,
            Width = Dim.Fill() - 2,
            Height = 8,
            ReadOnly = true,
            Text = @"
🏢 About Tenants:

A tenant represents your organization in NotifyMaster. It provides:
• Isolated data and configuration
• User and role management
• Billing and usage tracking
• Custom branding and settings

The domain will be used for:
• API endpoints: /api/tenants/{domain}/...
• User login scoping
• Multi-tenant isolation

Choose a short, memorable domain name (letters, numbers, hyphens only).
"
        };

        // Update data when fields change
        nameField.TextChanged += (args) => data.TenantName = args.NewText.ToString() ?? "";
        domainField.TextChanged += (args) => data.TenantDomain = args.NewText.ToString() ?? "";
        descField.TextChanged += (args) => data.TenantDescription = args.NewText.ToString() ?? "";

        frame.Add(label, nameLabel, nameField, domainLabel, domainField, 
                 descLabel, descField, infoText);
    }

    public static void ShowAdminStep(FrameView frame, SetupWizardData data)
    {
        var label = new Label("Create your administrator account:")
        {
            X = 1,
            Y = 1
        };

        var firstNameLabel = new Label("First Name:")
        {
            X = 1,
            Y = 3
        };

        var firstNameField = new TextField("")
        {
            X = 15,
            Y = 3,
            Width = 25
        };

        var lastNameLabel = new Label("Last Name:")
        {
            X = 1,
            Y = 5
        };

        var lastNameField = new TextField("")
        {
            X = 15,
            Y = 5,
            Width = 25
        };

        var emailLabel = new Label("Email:")
        {
            X = 1,
            Y = 7
        };

        var emailField = new TextField("")
        {
            X = 15,
            Y = 7,
            Width = 40
        };

        var passwordLabel = new Label("Password:")
        {
            X = 1,
            Y = 9
        };

        var passwordField = new TextField("")
        {
            X = 15,
            Y = 9,
            Width = 30,
            Secret = true
        };

        var confirmLabel = new Label("Confirm:")
        {
            X = 1,
            Y = 11
        };

        var confirmField = new TextField("")
        {
            X = 15,
            Y = 11,
            Width = 30,
            Secret = true
        };

        var infoText = new TextView()
        {
            X = 1,
            Y = 13,
            Width = Dim.Fill() - 2,
            Height = 6,
            ReadOnly = true,
            Text = @"
👤 Administrator Account:

This account will have full system access including:
• System configuration and settings
• User and tenant management  
• Plugin installation and configuration
• System monitoring and logs

Password Requirements:
• At least 8 characters
• Mix of letters, numbers, and symbols recommended
"
        };

        // Update data when fields change
        firstNameField.TextChanged += (args) => data.AdminFirstName = args.NewText.ToString() ?? "";
        lastNameField.TextChanged += (args) => data.AdminLastName = args.NewText.ToString() ?? "";
        emailField.TextChanged += (args) => data.AdminEmail = args.NewText.ToString() ?? "";
        passwordField.TextChanged += (args) => data.AdminPassword = args.NewText.ToString() ?? "";

        frame.Add(label, firstNameLabel, firstNameField, lastNameLabel, lastNameField,
                 emailLabel, emailField, passwordLabel, passwordField, 
                 confirmLabel, confirmField, infoText);
    }

    public static void ShowCompleteStep(FrameView frame, SetupWizardData data)
    {
        var summaryText = new TextView()
        {
            X = 1,
            Y = 1,
            Width = Dim.Fill() - 2,
            Height = Dim.Fill() - 2,
            ReadOnly = true,
            Text = $@"
🎉 Setup Configuration Summary

Your NotifyMaster instance is ready to be configured with the following settings:

📊 Database: {(string.IsNullOrEmpty(data.DatabaseConnectionString) ? "Not configured" : "✅ Configured")}

🔄 Redis: {(string.IsNullOrEmpty(data.RedisConnectionString) ? "Not configured" : "✅ Configured")}

🏢 Organization: {data.TenantName}
   Domain: {data.TenantDomain}
   Description: {data.TenantDescription}

👤 Administrator: {data.AdminFirstName} {data.AdminLastName}
   Email: {data.AdminEmail}

Click 'Next' to complete the setup process.

⚠️  This will:
• Update configuration files
• Initialize the database
• Create your tenant and admin user
• Start all system services

The process may take a few moments to complete.
"
        };

        frame.Add(summaryText);
    }
}
