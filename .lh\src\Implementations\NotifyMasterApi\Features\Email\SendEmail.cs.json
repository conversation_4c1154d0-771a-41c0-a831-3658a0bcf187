{"sourceFile": "src/Implementations/NotifyMasterApi/Features/Email/SendEmail.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 7, "patches": [{"date": 1751230838047, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751230856762, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -85,36 +85,36 @@\n /// </summary>\r\n public class SendEmailResponse\r\n {\r\n     /// <summary>\r\n-    /// Indicates whether the email was sent successfully\r\n+    /// Indicates whether the email was queued successfully\r\n     /// </summary>\r\n     /// <example>true</example>\r\n     public bool Success { get; set; }\r\n \r\n     /// <summary>\r\n-    /// Unique identifier for the sent message (available when successful)\r\n+    /// Correlation ID for tracking the request\r\n     /// </summary>\r\n-    /// <example>msg_abc123def456</example>\r\n-    public string? MessageId { get; set; }\r\n+    /// <example>corr_abc123def456</example>\r\n+    public string? CorrelationId { get; set; }\r\n \r\n     /// <summary>\r\n     /// Error message if the operation failed\r\n     /// </summary>\r\n     /// <example>Invalid email address format</example>\r\n     public string? Error { get; set; }\r\n \r\n     /// <summary>\r\n-    /// Timestamp when the operation was completed\r\n+    /// Timestamp when the request was queued\r\n     /// </summary>\r\n     /// <example>2024-01-15T10:30:00Z</example>\r\n     public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n \r\n     /// <summary>\r\n-    /// Email provider used for sending\r\n+    /// Status of the request\r\n     /// </summary>\r\n-    /// <example>SendGrid, Mailgun, SMTP</example>\r\n-    public string? Provider { get; set; }\r\n+    /// <example>Queued, Processing, Completed, Failed</example>\r\n+    public string Status { get; set; } = \"Queued\";\r\n \r\n     /// <summary>\r\n     /// Additional metadata about the operation\r\n     /// </summary>\r\n"}, {"date": 1751230869646, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -122,14 +122,16 @@\n }\r\n \r\n public class SendEmailEndpoint : Endpoint<SendEmailRequest, SendEmailResponse>\r\n {\r\n-    private readonly IEmailGateway _emailGateway;\r\n+    private readonly IQueueService _queueService;\r\n+    private readonly ITenantContext _tenantContext;\r\n     private readonly ILogger<SendEmailEndpoint> _logger;\r\n \r\n-    public SendEmailEndpoint(IEmailGateway emailGateway, ILogger<SendEmailEndpoint> logger)\r\n+    public SendEmailEndpoint(IQueueService queueService, ITenantContext tenantContext, ILogger<SendEmailEndpoint> logger)\r\n     {\r\n-        _emailGateway = emailGateway;\r\n+        _queueService = queueService;\r\n+        _tenantContext = tenantContext;\r\n         _logger = logger;\r\n     }\r\n \r\n     public override void Configure()\r\n"}, {"date": 1751230892257, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -166,50 +166,67 @@\n     public override async Task HandleAsync(SendEmailRequest req, CancellationToken ct)\r\n     {\r\n         try\r\n         {\r\n-            _logger.LogInformation(\"Sending email to {To}\", req.To);\r\n+            _logger.LogInformation(\"Queueing email to {To} for processing\", req.To);\r\n \r\n-            var emailRequest = new EmailMessageRequest\r\n+            // Create the message for the queue\r\n+            var emailMessage = new SendNotificationMessage\r\n             {\r\n-                To = req.To,\r\n+                TenantId = _tenantContext.TenantId ?? \"default\",\r\n+                UserId = HttpContext.User?.Identity?.Name ?? \"anonymous\",\r\n+                PluginName = \"email\",\r\n+                Recipient = req.To,\r\n+                Content = req.PlainTextBody ?? req.HtmlBody ?? \"\",\r\n+                Subject = req.Subject,\r\n                 From = req.From,\r\n-                Subject = req.Subject,\r\n-                Body = req.PlainTextBody ?? req.HtmlBody ?? \"\",\r\n-                HtmlBody = req.HtmlBody,\r\n-                PlainTextBody = req.PlainTextBody,\r\n-                Cc = string.IsNullOrEmpty(req.Cc) ? null : new List<string> { req.Cc },\r\n-                Bcc = string.IsNullOrEmpty(req.Bcc) ? null : new List<string> { req.Bcc },\r\n-                Headers = req.Headers,\r\n-                Category = req.Category\r\n+                Headers = req.Headers ?? new Dictionary<string, string>(),\r\n+                Metadata = new Dictionary<string, object>\r\n+                {\r\n+                    [\"HtmlBody\"] = req.HtmlBody ?? \"\",\r\n+                    [\"PlainTextBody\"] = req.PlainTextBody ?? \"\",\r\n+                    [\"Cc\"] = req.Cc ?? \"\",\r\n+                    [\"Bcc\"] = req.Bcc ?? \"\",\r\n+                    [\"Category\"] = req.Category ?? \"\"\r\n+                },\r\n+                RequestedAt = DateTime.UtcNow\r\n             };\r\n \r\n-            var result = await _emailGateway.SendAsync(emailRequest);\r\n+            // Enqueue the message\r\n+            var result = await _queueService.EnqueueAsync(QueueNames.EmailProcessing, emailMessage, ct);\r\n \r\n             if (result.IsSuccess)\r\n             {\r\n                 await SendOkAsync(new SendEmailResponse\r\n                 {\r\n                     Success = true,\r\n-                    MessageId = result.MessageId\r\n+                    CorrelationId = emailMessage.CorrelationId,\r\n+                    Status = \"Queued\",\r\n+                    Metadata = new Dictionary<string, object>\r\n+                    {\r\n+                        [\"QueueName\"] = QueueNames.EmailProcessing,\r\n+                        [\"JobId\"] = result.Data ?? \"unknown\"\r\n+                    }\r\n                 }, ct);\r\n             }\r\n             else\r\n             {\r\n                 await SendAsync(new SendEmailResponse\r\n                 {\r\n                     Success = false,\r\n-                    Error = result.ErrorMessage\r\n+                    Error = result.Message,\r\n+                    Status = \"Failed\"\r\n                 }, 400, ct);\r\n             }\r\n         }\r\n         catch (Exception ex)\r\n         {\r\n-            _logger.LogError(ex, \"Error sending email\");\r\n+            _logger.LogError(ex, \"Error queueing email\");\r\n             await SendAsync(new SendEmailResponse\r\n             {\r\n                 Success = false,\r\n-                Error = \"Internal server error\"\r\n+                Error = \"Internal server error\",\r\n+                Status = \"Failed\"\r\n             }, 500, ct);\r\n         }\r\n     }\r\n }\r\n"}, {"date": 1751231220237, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,9 +3,9 @@\n using NotificationContract.Models;\r\n using NotifyMasterApi.Documentation;\r\n using System.ComponentModel.DataAnnotations;\r\n using NotifyMaster.Core.Interfaces;\r\n-using NotifyMaster.Core.Interfaces.Messages;\r\n+using static NotifyMaster.Core.Interfaces.Messages;\r\n \r\n namespace NotifyMasterApi.Features.Email;\r\n \r\n /// <summary>\r\n"}, {"date": 1751234590387, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -120,22 +120,21 @@\n     /// </summary>\r\n     public Dictionary<string, object>? Metadata { get; set; }\r\n }\r\n \r\n-public class SendEmailEndpoint : Endpoint<SendEmailRequest, SendEmailResponse>\r\n+public class SendEmailEndpoint : QueueFirstEndpointBase<SendEmailRequest, SendEmailResponse>\r\n {\r\n     private readonly IQueueService _queueService;\r\n     private readonly ITenantContext _tenantContext;\r\n-    private readonly ILogger<SendEmailEndpoint> _logger;\r\n \r\n     public SendEmailEndpoint(IQueueService queueService, ITenantContext tenantContext, ILogger<SendEmailEndpoint> logger)\r\n+        : base(logger)\r\n     {\r\n         _queueService = queueService;\r\n         _tenantContext = tenantContext;\r\n-        _logger = logger;\r\n     }\r\n \r\n-    public override void Configure()\r\n+    protected override void ConfigureEndpoint()\r\n     {\r\n         this.ConfigureNotificationEndpoint(\r\n             \"POST\",\r\n             \"/api/email/send\",\r\n@@ -157,10 +156,9 @@\n             \"- Input sanitization for XSS prevention\\n\" +\r\n             \"- Email address validation\\n\" +\r\n             \"- Content filtering for spam prevention\\n\" +\r\n             \"- Rate limiting and abuse protection\",\r\n-            \"Email\",\r\n-            new[] { \"Core Messaging\", \"Communication\" }\r\n+            new[] { \"Email\" }\r\n         );\r\n     }\r\n \r\n     public override async Task HandleAsync(SendEmailRequest req, CancellationToken ct)\r\n"}, {"date": 1751234612360, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -193,9 +193,9 @@\n             var result = await _queueService.EnqueueAsync(QueueNames.EmailProcessing, emailMessage, ct);\r\n \r\n             if (result.IsSuccess)\r\n             {\r\n-                await SendOkAsync(new SendEmailResponse\r\n+                var responseData = new SendEmailResponse\r\n                 {\r\n                     Success = true,\r\n                     CorrelationId = emailMessage.CorrelationId,\r\n                     Status = \"Queued\",\r\n@@ -203,29 +203,21 @@\n                     {\r\n                         [\"QueueName\"] = QueueNames.EmailProcessing,\r\n                         [\"JobId\"] = result.Data ?? \"unknown\"\r\n                     }\r\n-                }, ct);\r\n+                };\r\n+\r\n+                await SendOkAsync(CreateQueuedResponse(responseData, \"Email queued successfully\"), ct);\r\n             }\r\n             else\r\n             {\r\n-                await SendAsync(new SendEmailResponse\r\n-                {\r\n-                    Success = false,\r\n-                    Error = result.Message,\r\n-                    Status = \"Failed\"\r\n-                }, 400, ct);\r\n+                await SendAsync(CreateErrorResponse(\"Failed to queue email\", result.Message), 400, ct);\r\n             }\r\n         }\r\n         catch (Exception ex)\r\n         {\r\n-            _logger.LogError(ex, \"Error queueing email\");\r\n-            await SendAsync(new SendEmailResponse\r\n-            {\r\n-                Success = false,\r\n-                Error = \"Internal server error\",\r\n-                Status = \"Failed\"\r\n-            }, 500, ct);\r\n+            Logger.LogError(ex, \"Error queueing email\");\r\n+            await SendAsync(CreateErrorResponse(\"Failed to queue email\", ex.Message), 500, ct);\r\n         }\r\n     }\r\n }\r\n \r\n"}, {"date": 1751234629907, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -220,31 +220,27 @@\n         }\r\n     }\r\n }\r\n \r\n-public class GetEmailProvidersEndpoint : Endpoint<EmptyRequest, object>\r\n+[SkipQueueProcessing] // Read-only operation doesn't need queuing\r\n+public class GetEmailProvidersEndpoint : StandardEndpointBase<EmptyRequest, object>\r\n {\r\n     private readonly IEmailGateway _emailGateway;\r\n-    private readonly ILogger<GetEmailProvidersEndpoint> _logger;\r\n \r\n     public GetEmailProvidersEndpoint(IEmailGateway emailGateway, ILogger<GetEmailProvidersEndpoint> logger)\r\n+        : base(logger)\r\n     {\r\n         _emailGateway = emailGateway;\r\n-        _logger = logger;\r\n     }\r\n \r\n-    public override void Configure()\r\n+    protected override void ConfigureEndpoint()\r\n     {\r\n-        Get(\"/api/email/providers\");\r\n-        AllowAnonymous();\r\n-        Summary(s =>\r\n-        {\r\n-            s.Summary = \"Get email providers\";\r\n-            s.Description = \"Get list of available email providers\";\r\n-            s.Responses[200] = \"Providers retrieved successfully\";\r\n-            s.Responses[500] = \"Internal server error\";\r\n-        });\r\n-        Tags(\"Email\");\r\n+        this.ConfigureNotificationEndpoint(\r\n+            \"GET\",\r\n+            \"/api/email/providers\",\r\n+            \"Get Email Providers\",\r\n+            \"Get list of available email providers and their status\",\r\n+            new[] { \"Email\" });\r\n     }\r\n \r\n     public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n     {\r\n"}], "date": 1751230838047, "name": "Commit-0", "content": "using FastEndpoints;\r\nusing NotifyMasterApi.Gateways;\r\nusing NotificationContract.Models;\r\nusing NotifyMasterApi.Documentation;\r\nusing System.ComponentModel.DataAnnotations;\r\nusing NotifyMaster.Core.Interfaces;\r\nusing NotifyMaster.Core.Interfaces.Messages;\r\n\r\nnamespace NotifyMasterApi.Features.Email;\r\n\r\n/// <summary>\r\n/// Request model for sending an email message\r\n/// </summary>\r\npublic class SendEmailRequest\r\n{\r\n    /// <summary>\r\n    /// Recipient email address (required)\r\n    /// </summary>\r\n    /// <example><EMAIL></example>\r\n    [Required(ErrorMessage = \"Recipient email address is required\")]\r\n    [EmailAddress(ErrorMessage = \"Invalid email address format\")]\r\n    public string To { get; set; } = string.Empty;\r\n\r\n    /// <summary>\r\n    /// Sender email address (optional - uses default if not specified)\r\n    /// </summary>\r\n    /// <example><EMAIL></example>\r\n    [EmailAddress(ErrorMessage = \"Invalid sender email address format\")]\r\n    public string From { get; set; } = string.Empty;\r\n\r\n    /// <summary>\r\n    /// Email subject line (required)\r\n    /// </summary>\r\n    /// <example>Welcome to NotificationService</example>\r\n    [Required(ErrorMessage = \"Email subject is required\")]\r\n    [StringLength(200, ErrorMessage = \"Subject cannot exceed 200 characters\")]\r\n    public string Subject { get; set; } = string.Empty;\r\n\r\n    /// <summary>\r\n    /// Email body content (deprecated - use HtmlBody or PlainTextBody)\r\n    /// </summary>\r\n    [Obsolete(\"Use HtmlBody or PlainTextBody instead\")]\r\n    public string? Body { get; set; }\r\n\r\n    /// <summary>\r\n    /// HTML formatted email body\r\n    /// </summary>\r\n    /// <example>&lt;h1&gt;Welcome!&lt;/h1&gt;&lt;p&gt;Thank you for joining us.&lt;/p&gt;</example>\r\n    public string? HtmlBody { get; set; }\r\n\r\n    /// <summary>\r\n    /// Plain text email body (fallback for HTML)\r\n    /// </summary>\r\n    /// <example>Welcome! Thank you for joining us.</example>\r\n    public string? PlainTextBody { get; set; }\r\n\r\n    /// <summary>\r\n    /// Carbon copy recipients (comma-separated)\r\n    /// </summary>\r\n    /// <example><EMAIL>,<EMAIL></example>\r\n    public string? Cc { get; set; }\r\n\r\n    /// <summary>\r\n    /// Blind carbon copy recipients (comma-separated)\r\n    /// </summary>\r\n    /// <example><EMAIL>,<EMAIL></example>\r\n    public string? Bcc { get; set; }\r\n\r\n    /// <summary>\r\n    /// Custom email headers\r\n    /// </summary>\r\n    /// <example>{\"X-Priority\": \"1\", \"X-Custom-Header\": \"value\"}</example>\r\n    public Dictionary<string, string>? Headers { get; set; }\r\n\r\n    /// <summary>\r\n    /// Email category for tracking and analytics\r\n    /// </summary>\r\n    /// <example>welcome, notification, marketing</example>\r\n    [StringLength(50, ErrorMessage = \"Category cannot exceed 50 characters\")]\r\n    public string? Category { get; set; }\r\n}\r\n\r\n/// <summary>\r\n/// Response model for email sending operation\r\n/// </summary>\r\npublic class SendEmailResponse\r\n{\r\n    /// <summary>\r\n    /// Indicates whether the email was sent successfully\r\n    /// </summary>\r\n    /// <example>true</example>\r\n    public bool Success { get; set; }\r\n\r\n    /// <summary>\r\n    /// Unique identifier for the sent message (available when successful)\r\n    /// </summary>\r\n    /// <example>msg_abc123def456</example>\r\n    public string? MessageId { get; set; }\r\n\r\n    /// <summary>\r\n    /// Error message if the operation failed\r\n    /// </summary>\r\n    /// <example>Invalid email address format</example>\r\n    public string? Error { get; set; }\r\n\r\n    /// <summary>\r\n    /// Timestamp when the operation was completed\r\n    /// </summary>\r\n    /// <example>2024-01-15T10:30:00Z</example>\r\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n\r\n    /// <summary>\r\n    /// Email provider used for sending\r\n    /// </summary>\r\n    /// <example>SendGrid, Mailgun, SMTP</example>\r\n    public string? Provider { get; set; }\r\n\r\n    /// <summary>\r\n    /// Additional metadata about the operation\r\n    /// </summary>\r\n    public Dictionary<string, object>? Metadata { get; set; }\r\n}\r\n\r\npublic class SendEmailEndpoint : Endpoint<SendEmailRequest, SendEmailResponse>\r\n{\r\n    private readonly IEmailGateway _emailGateway;\r\n    private readonly ILogger<SendEmailEndpoint> _logger;\r\n\r\n    public SendEmailEndpoint(IEmailGateway emailGateway, ILogger<SendEmailEndpoint> logger)\r\n    {\r\n        _emailGateway = emailGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        this.ConfigureNotificationEndpoint(\r\n            \"POST\",\r\n            \"/api/email/send\",\r\n            \"Send Email Message\",\r\n            \"Send an email message through the configured email providers.\\n\\n\" +\r\n            \"## 🎯 Features\\n\" +\r\n            \"- **Multi-Provider Support**: Automatically routes through available email providers\\n\" +\r\n            \"- **HTML & Plain Text**: Support for both HTML and plain text content\\n\" +\r\n            \"- **Advanced Recipients**: CC, BCC, and custom headers support\\n\" +\r\n            \"- **Tracking & Analytics**: Message categorization and delivery tracking\\n\" +\r\n            \"- **Validation**: Comprehensive input validation and error handling\\n\\n\" +\r\n            \"## 📋 Provider Support\\n\" +\r\n            \"- SendGrid\\n- Mailgun\\n- Amazon SES\\n- SMTP (Generic)\\n- Custom Email Plugins\\n\\n\" +\r\n            \"## ⚡ Rate Limits\\n\" +\r\n            \"- **Default**: 100 requests/minute per API key\\n\" +\r\n            \"- **Burst**: Up to 1000 requests in 10 seconds\\n\" +\r\n            \"- **Daily**: 10,000 emails per day (configurable)\\n\\n\" +\r\n            \"## 🔒 Security\\n\" +\r\n            \"- Input sanitization for XSS prevention\\n\" +\r\n            \"- Email address validation\\n\" +\r\n            \"- Content filtering for spam prevention\\n\" +\r\n            \"- Rate limiting and abuse protection\",\r\n            \"Email\",\r\n            new[] { \"Core Messaging\", \"Communication\" }\r\n        );\r\n    }\r\n\r\n    public override async Task HandleAsync(SendEmailRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            _logger.LogInformation(\"Sending email to {To}\", req.To);\r\n\r\n            var emailRequest = new EmailMessageRequest\r\n            {\r\n                To = req.To,\r\n                From = req.From,\r\n                Subject = req.Subject,\r\n                Body = req.PlainTextBody ?? req.HtmlBody ?? \"\",\r\n                HtmlBody = req.HtmlBody,\r\n                PlainTextBody = req.PlainTextBody,\r\n                Cc = string.IsNullOrEmpty(req.Cc) ? null : new List<string> { req.Cc },\r\n                Bcc = string.IsNullOrEmpty(req.Bcc) ? null : new List<string> { req.Bcc },\r\n                Headers = req.Headers,\r\n                Category = req.Category\r\n            };\r\n\r\n            var result = await _emailGateway.SendAsync(emailRequest);\r\n\r\n            if (result.IsSuccess)\r\n            {\r\n                await SendOkAsync(new SendEmailResponse\r\n                {\r\n                    Success = true,\r\n                    MessageId = result.MessageId\r\n                }, ct);\r\n            }\r\n            else\r\n            {\r\n                await SendAsync(new SendEmailResponse\r\n                {\r\n                    Success = false,\r\n                    Error = result.ErrorMessage\r\n                }, 400, ct);\r\n            }\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error sending email\");\r\n            await SendAsync(new SendEmailResponse\r\n            {\r\n                Success = false,\r\n                Error = \"Internal server error\"\r\n            }, 500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetEmailProvidersEndpoint : Endpoint<EmptyRequest, object>\r\n{\r\n    private readonly IEmailGateway _emailGateway;\r\n    private readonly ILogger<GetEmailProvidersEndpoint> _logger;\r\n\r\n    public GetEmailProvidersEndpoint(IEmailGateway emailGateway, ILogger<GetEmailProvidersEndpoint> logger)\r\n    {\r\n        _emailGateway = emailGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/email/providers\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get email providers\";\r\n            s.Description = \"Get list of available email providers\";\r\n            s.Responses[200] = \"Providers retrieved successfully\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"Email\");\r\n    }\r\n\r\n    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var providers = await _emailGateway.GetProvidersAsync();\r\n            await SendOkAsync(providers, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting email providers\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class TestEmailProviderRequest\r\n{\r\n    public string Provider { get; set; } = string.Empty;\r\n    public string? TestEmail { get; set; }\r\n}\r\n\r\npublic class TestEmailProviderEndpoint : Endpoint<TestEmailProviderRequest, object>\r\n{\r\n    private readonly IEmailGateway _emailGateway;\r\n    private readonly ILogger<TestEmailProviderEndpoint> _logger;\r\n\r\n    public TestEmailProviderEndpoint(IEmailGateway emailGateway, ILogger<TestEmailProviderEndpoint> logger)\r\n    {\r\n        _emailGateway = emailGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Post(\"/api/email/providers/{provider}/test\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Test email provider\";\r\n            s.Description = \"Test a specific email provider\";\r\n            s.Responses[200] = \"Provider test completed\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"Email\");\r\n    }\r\n\r\n    public override async Task HandleAsync(TestEmailProviderRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var result = await _emailGateway.TestProviderAsync(req.Provider, req.TestEmail);\r\n            await SendOkAsync(result, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error testing email provider {Provider}\", req.Provider);\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n"}]}