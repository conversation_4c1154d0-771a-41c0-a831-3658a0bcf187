// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Models;

/// <summary>
/// Represents a tenant in the multi-tenant system
/// </summary>
public class Tenant
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required, MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required, MaxLength(100)]
    public string Domain { get; set; } = string.Empty;
    
    public TenantStatus Status { get; set; } = TenantStatus.Active;
    
    public TenantPlan Plan { get; set; } = TenantPlan.Basic;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? CreatedBy { get; set; }
    
    public Dictionary<string, object> Settings { get; set; } = new();
    
    public TenantLimits Limits { get; set; } = new();
    
    public TenantUsage Usage { get; set; } = new();
    
    // Navigation properties
    public List<User> Users { get; set; } = new();
    public List<TenantPlugin> Plugins { get; set; } = new();
}

/// <summary>
/// Tenant status enumeration
/// </summary>
public enum TenantStatus
{
    Active = 0,
    Suspended = 1,
    Inactive = 2,
    Deleted = 3
}

/// <summary>
/// Tenant plan enumeration
/// </summary>
public enum TenantPlan
{
    Basic = 0,
    Professional = 1,
    Enterprise = 2,
    Custom = 3
}

/// <summary>
/// Tenant limits configuration
/// </summary>
public class TenantLimits
{
    public int MaxUsers { get; set; } = 10;
    public int MaxPlugins { get; set; } = 5;
    public int MaxMessagesPerMonth { get; set; } = 1000;
    public int MaxStorageGB { get; set; } = 1;
    public int MaxApiCallsPerDay { get; set; } = 10000;
    public Dictionary<string, object> CustomLimits { get; set; } = new();
}

/// <summary>
/// Tenant usage tracking
/// </summary>
public class TenantUsage
{
    public int CurrentUsers { get; set; }
    public int CurrentPlugins { get; set; }
    public int MessagesThisMonth { get; set; }
    public double StorageUsedGB { get; set; }
    public int ApiCallsToday { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> CustomUsage { get; set; } = new();
}

/// <summary>
/// Represents tenant-specific plugin configuration
/// </summary>
public class TenantPlugin
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string TenantId { get; set; } = string.Empty;
    
    [Required, MaxLength(100)]
    public string PluginName { get; set; } = string.Empty;
    
    public bool IsEnabled { get; set; } = true;
    
    public Dictionary<string, object> Configuration { get; set; } = new();
    
    public DateTime ConfiguredAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? ConfiguredBy { get; set; }
    
    // Navigation properties
    public Tenant Tenant { get; set; } = null!;
}

/// <summary>
/// Request model for creating a tenant
/// </summary>
public class CreateTenantRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Domain { get; set; } = string.Empty;
    public TenantPlan Plan { get; set; } = TenantPlan.Basic;
    public Dictionary<string, object> Settings { get; set; } = new();
    public TenantLimits? CustomLimits { get; set; }
    public string? CreatedBy { get; set; }
}

/// <summary>
/// Request model for updating a tenant
/// </summary>
public class UpdateTenantRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Domain { get; set; }
    public TenantStatus? Status { get; set; }
    public TenantPlan? Plan { get; set; }
    public Dictionary<string, object>? Settings { get; set; }
    public TenantLimits? Limits { get; set; }
    public string? UpdatedBy { get; set; }
}
