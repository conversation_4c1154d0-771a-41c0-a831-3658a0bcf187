namespace NotifyMasterApi.Features.Database;

/// <summary>
/// Endpoint to get supported database providers
/// </summary>
public class GetSupportedProvidersEndpoint : EndpointWithoutRequest<GetSupportedProvidersResponse>
{
    public override void Configure()
    {
        Get("/api/database/providers");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get supported database providers";
            s.Description = "Returns a list of all supported database providers that can be configured";
            s.Responses[200] = "List of supported database providers";
        });
        
        Tags("🔧 System Administration");
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var providers = DatabaseProviderService.GetSupportedProviders();
        
        var response = new GetSupportedProvidersResponse
        {
            Providers = providers.Select(p => new DatabaseProviderInfo
            {
                Name = p,
                DisplayName = GetDisplayName(p),
                Description = GetDescription(p),
                DefaultPort = GetDefaultPort(p),
                RequiresCredentials = RequiresCredentials(p),
                SupportsIntegratedSecurity = SupportsIntegratedSecurity(p)
            }).ToList()
        };

        await SendOkAsync(response, ct);
    }

    private static string GetDisplayName(string provider) => provider switch
    {
        "SqlServer" => "Microsoft SQL Server",
        "MySQL" => "MySQL",
        "PostgreSQL" => "PostgreSQL",
        "SQLite" => "SQLite",
        "InMemory" => "In-Memory Database",
        _ => provider
    };

    private static string GetDescription(string provider) => provider switch
    {
        "SqlServer" => "Microsoft SQL Server database engine",
        "MySQL" => "MySQL open-source relational database",
        "PostgreSQL" => "PostgreSQL open-source object-relational database",
        "SQLite" => "SQLite embedded database (file-based)",
        "InMemory" => "In-memory database for development and testing",
        _ => $"{provider} database provider"
    };

    private static int GetDefaultPort(string provider) => provider switch
    {
        "SqlServer" => 1433,
        "MySQL" => 3306,
        "PostgreSQL" => 5432,
        "SQLite" => 0,
        "InMemory" => 0,
        _ => 0
    };

    private static bool RequiresCredentials(string provider) => provider switch
    {
        "SQLite" => false,
        "InMemory" => false,
        _ => true
    };

    private static bool SupportsIntegratedSecurity(string provider) => provider switch
    {
        "SqlServer" => true,
        _ => false
    };
}

/// <summary>
/// Response model for supported database providers
/// </summary>
public class GetSupportedProvidersResponse
{
    public List<DatabaseProviderInfo> Providers { get; set; } = new();
}

/// <summary>
/// Information about a database provider
/// </summary>
public class DatabaseProviderInfo
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int DefaultPort { get; set; }
    public bool RequiresCredentials { get; set; }
    public bool SupportsIntegratedSecurity { get; set; }
}
