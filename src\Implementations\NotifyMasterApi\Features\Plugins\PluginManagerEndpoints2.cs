namespace NotifyMasterApi.Features.Plugins;

// ============================================================================
// Get Plugin Statuses
// ============================================================================

public class GetPluginStatusesEndpoint : EndpointWithoutRequest<List<PluginStatus>>
{
    private readonly IPluginManager _pluginManager;

    public GetPluginStatusesEndpoint(IPluginManager pluginManager)
    {
        _pluginManager = pluginManager;
    }

    public override void Configure()
    {
        Get("/api/plugins/status");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get plugin statuses";
            s.Description = "Get the status of all loaded plugins including health information";
            s.Response<List<PluginStatus>>(200, "Plugin statuses retrieved successfully");
        });
        Tags("🔌 Plugin Management");
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var statuses = await _pluginManager.GetPluginStatusesAsync(ct);
        await SendOkAsync(statuses.ToList(), ct);
    }
}

// ============================================================================
// Validate Plugin
// ============================================================================

public class ValidatePluginRequest
{
    [Required]
    public string PluginPath { get; set; } = string.Empty;
}

public class ValidatePluginEndpoint : Endpoint<ValidatePluginRequest, ValidationResult>
{
    private readonly IPluginManager _pluginManager;

    public ValidatePluginEndpoint(IPluginManager pluginManager)
    {
        _pluginManager = pluginManager;
    }

    public override void Configure()
    {
        Post("/api/plugins/validate");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Validate plugin";
            s.Description = "Validate a plugin before loading to check for compatibility and errors";
            s.Response<ValidationResult>(200, "Plugin validation completed");
            s.Response(400, "Invalid plugin path");
        });
        Tags("🔌 Plugin Management");
    }

    public override async Task HandleAsync(ValidatePluginRequest req, CancellationToken ct)
    {
        var result = await _pluginManager.ValidatePluginAsync(req.PluginPath, ct);
        await SendOkAsync(result, ct);
    }
}

// ============================================================================
// Get Plugin Metrics
// ============================================================================

public class GetPluginMetricsRequest
{
    [Required]
    public string PluginName { get; set; } = string.Empty;
}

public class GetPluginMetricsEndpoint : Endpoint<GetPluginMetricsRequest, PluginMetrics?>
{
    private readonly IPluginManager _pluginManager;

    public GetPluginMetricsEndpoint(IPluginManager pluginManager)
    {
        _pluginManager = pluginManager;
    }

    public override void Configure()
    {
        Get("/api/plugins/{pluginName}/metrics");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get plugin metrics";
            s.Description = "Get performance and usage metrics for a specific plugin";
            s.Response<PluginMetrics>(200, "Plugin metrics retrieved successfully");
            s.Response(404, "Plugin not found");
        });
        Tags("🔌 Plugin Management");
    }

    public override async Task HandleAsync(GetPluginMetricsRequest req, CancellationToken ct)
    {
        var metrics = await _pluginManager.GetPluginMetricsAsync(req.PluginName, ct);
        
        if (metrics == null)
        {
            await SendNotFoundAsync(ct);
            return;
        }
        
        await SendOkAsync(metrics, ct);
    }
}

// ============================================================================
// Template Rendering Endpoints (Additional Methods)
// ============================================================================

public class RenderTemplateDirectRequest
{
    [Required]
    public string TemplateContent { get; set; } = string.Empty;
    
    [Required]
    public Dictionary<string, object> Variables { get; set; } = new();
}

public class RenderTemplateDirectEndpoint : Endpoint<RenderTemplateDirectRequest, string>
{
    private readonly ITemplateRenderingService _templateService;

    public RenderTemplateDirectEndpoint(ITemplateRenderingService templateService)
    {
        _templateService = templateService;
    }

    public override void Configure()
    {
        Post("/api/templates/render-direct");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Render template directly";
            s.Description = "Render template content directly with variables (simple version)";
            s.Response<string>(200, "Template rendered successfully");
            s.Response(400, "Invalid template or variables");
        });
        Tags("📝 Templates");
    }

    public override async Task HandleAsync(RenderTemplateDirectRequest req, CancellationToken ct)
    {
        var result = await _templateService.RenderTemplateAsync(req.TemplateContent, req.Variables);
        await SendOkAsync(result, ct);
    }
}

// ============================================================================
// Extract Template Variables
// ============================================================================

public class ExtractVariablesRequest
{
    [Required]
    public string TemplateContent { get; set; } = string.Empty;
}

public class ExtractVariablesEndpoint : Endpoint<ExtractVariablesRequest, List<string>>
{
    private readonly ITemplateRenderingService _templateService;

    public ExtractVariablesEndpoint(ITemplateRenderingService templateService)
    {
        _templateService = templateService;
    }

    public override void Configure()
    {
        Post("/api/templates/extract-variables");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Extract template variables";
            s.Description = "Extract all variable names from template content";
            s.Response<List<string>>(200, "Variables extracted successfully");
        });
        Tags("📝 Templates");
    }

    public override async Task HandleAsync(ExtractVariablesRequest req, CancellationToken ct)
    {
        var variables = await _templateService.ExtractVariablesFromTemplateAsync(req.TemplateContent);
        await SendOkAsync(variables, ct);
    }
}

// ============================================================================
// Notification Service Endpoint
// ============================================================================

public class SendNotificationDirectRequest
{
    [Required]
    public string Type { get; set; } = string.Empty;
    
    [Required]
    public string Recipient { get; set; } = string.Empty;
    
    public string Subject { get; set; } = string.Empty;
    
    [Required]
    public string Content { get; set; } = string.Empty;
    
    public Dictionary<string, object>? Metadata { get; set; }
}

public class SendNotificationDirectEndpoint : Endpoint<SendNotificationDirectRequest, object>
{
    private readonly INotificationService _notificationService;
    private readonly ILogger<SendNotificationDirectEndpoint> _logger;

    public SendNotificationDirectEndpoint(INotificationService notificationService, ILogger<SendNotificationDirectEndpoint> logger)
    {
        _notificationService = notificationService;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/notifications/send-direct");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Send notification directly";
            s.Description = "Send a notification through the core notification service";
            s.Response(200, "Notification sent successfully");
            s.Response(400, "Invalid notification request");
        });
        Tags("📨 Notifications");
    }

    public override async Task HandleAsync(SendNotificationDirectRequest req, CancellationToken ct)
    {
        try
        {
            // Convert to SendNotificationRequest (assuming this exists in NotificationContract)
            var request = new NotificationContract.Models.SendNotificationRequest
            {
                Type = req.Type,
                Recipient = req.Recipient,
                Subject = req.Subject,
                Content = req.Content,
                Metadata = req.Metadata ?? new Dictionary<string, object>()
            };

            await _notificationService.SendAsync(request);
            
            await SendOkAsync(new
            {
                Success = true,
                Message = "Notification sent successfully",
                Timestamp = DateTime.UtcNow
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification");
            await SendAsync(new
            {
                Success = false,
                Error = ex.Message,
                Timestamp = DateTime.UtcNow
            }, 400, ct);
        }
    }
}
