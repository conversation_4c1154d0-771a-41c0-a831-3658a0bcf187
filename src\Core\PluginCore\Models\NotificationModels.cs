namespace PluginCore.Models;

/// <summary>
/// Base record for notification requests
/// </summary>
public record NotificationRequest(
    string Content,
    Dictionary<string, object>? Metadata = null
);

/// <summary>
/// Comprehensive request for sending notifications through the notification service
/// </summary>
public class SendNotificationRequest
{
    /// <summary>
    /// Unique identifier for this notification request
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// Tenant ID for multi-tenant scenarios
    /// </summary>
    public string? TenantId { get; set; }

    /// <summary>
    /// User ID of the sender (optional)
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// Correlation ID for tracking related requests
    /// </summary>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// Priority level of the notification
    /// </summary>
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;

    /// <summary>
    /// List of recipients for this notification
    /// </summary>
    public List<Recipient> Recipients { get; set; } = new();

    /// <summary>
    /// Message content and details
    /// </summary>
    public MessagePayload Message { get; set; } = new();

    /// <summary>
    /// Channels to send through (Email, SMS, Push, etc.)
    /// </summary>
    public List<string> Channels { get; set; } = new();

    /// <summary>
    /// Template ID if using a predefined template
    /// </summary>
    public string? TemplateId { get; set; }

    /// <summary>
    /// Template variables for dynamic content
    /// </summary>
    public Dictionary<string, object>? TemplateVariables { get; set; }

    /// <summary>
    /// Scheduled delivery time (optional)
    /// </summary>
    public DateTime? ScheduledAt { get; set; }

    /// <summary>
    /// Expiration time for the notification
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Additional metadata for the notification
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// Whether to track delivery status
    /// </summary>
    public bool TrackDelivery { get; set; } = true;

    /// <summary>
    /// Maximum retry attempts for failed deliveries
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Tags for categorizing notifications
    /// </summary>
    public List<string>? Tags { get; set; }
}

/// <summary>
/// Base record for notification responses
/// </summary>
public record NotificationResponse(
    bool IsSuccess,
    string? ErrorMessage = null,
    Dictionary<string, object>? ResponseData = null
);

/// <summary>
/// Represents a notification log entry
/// </summary>
public class NotificationLog
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string MessageId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string Recipient { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public string Content { get; set; } = string.Empty;
    public NotificationStatus Status { get; set; }
    public string? Provider { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? FailedAt { get; set; }
    public int RetryCount { get; set; } = 0;
    public DateTime? LastRetryAt { get; set; }
    public string? CorrelationId { get; set; }
    public string? UserId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Represents a message template
/// </summary>
public class MessageTemplate
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public string Content { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
}

/// <summary>
/// Represents a scheduled message
/// </summary>
public class ScheduledMessage
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string MessageId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string Recipient { get; set; } = string.Empty;
    public string? Subject { get; set; }
    public string Content { get; set; } = string.Empty;
    public DateTime ScheduledTime { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public bool IsProcessed { get; set; } = false;
    public DateTime? ProcessedAt { get; set; }
}

/// <summary>
/// Represents a webhook endpoint
/// </summary>
public class WebhookEndpoint
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public string? Secret { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Notification type enumeration
/// </summary>
public enum NotificationType
{
    Email = 0,
    Sms = 1,
    PushMessage = 2,
    WebApp = 3
}

/// <summary>
/// Notification priority enumeration
/// </summary>
public enum NotificationPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}

/// <summary>
/// Notification status enumeration
/// </summary>
public enum NotificationStatus
{
    Pending = 0,
    Processing = 1,
    Sent = 2,
    Delivered = 3,
    Failed = 4,
    Cancelled = 5,
    Retrying = 6
}
