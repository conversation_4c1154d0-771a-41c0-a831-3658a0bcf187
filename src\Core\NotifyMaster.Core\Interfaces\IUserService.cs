// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Interface for user management service
/// </summary>
public interface IUserService
{
    /// <summary>
    /// Gets a user by ID
    /// </summary>
    Task<User?> GetUserAsync(string userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a user by email
    /// </summary>
    Task<User?> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a user by username
    /// </summary>
    Task<User?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets users by tenant with pagination
    /// </summary>
    Task<IReadOnlyList<User>> GetUsersByTenantAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Creates a new user
    /// </summary>
    Task<OperationResult<User>> CreateUserAsync(CreateUserRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates an existing user
    /// </summary>
    Task<OperationResult<User>> UpdateUserAsync(string userId, UpdateUserRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes a user
    /// </summary>
    Task<OperationResult> DeleteUserAsync(string userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets user roles
    /// </summary>
    Task<IReadOnlyList<Role>> GetUserRolesAsync(string userId, string? tenantId = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets user permissions
    /// </summary>
    Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, string? tenantId = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Assigns a role to a user
    /// </summary>
    Task<OperationResult> AssignRoleAsync(string userId, string roleId, string? tenantId = null, string? assignedBy = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Removes a role from a user
    /// </summary>
    Task<OperationResult> RemoveRoleAsync(string userId, string roleId, string? tenantId = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Grants a permission to a user
    /// </summary>
    Task<OperationResult> GrantPermissionAsync(string userId, string permissionId, string? tenantId = null, string? grantedBy = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Revokes a permission from a user
    /// </summary>
    Task<OperationResult> RevokePermissionAsync(string userId, string permissionId, string? tenantId = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Authenticates a user
    /// </summary>
    Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a user has a specific permission
    /// </summary>
    Task<bool> HasPermissionAsync(string userId, string resource, string action, string? tenantId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a user has a specific role
    /// </summary>
    Task<bool> HasRoleAsync(string userId, string roleName, string? tenantId = null, CancellationToken cancellationToken = default);
}
