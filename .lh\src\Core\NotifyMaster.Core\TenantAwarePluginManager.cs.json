{"sourceFile": "src/Core/NotifyMaster.Core/TenantAwarePluginManager.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1751229827976, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751229840062, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,10 +20,10 @@\n public class TenantAwarePluginManager : ITenantAwarePluginManager\n {\n     private readonly ILogger<TenantAwarePluginManager> _logger;\n     private readonly IPluginManager _basePluginManager;\n-    private readonly ITenantService _tenantService;\n-    private readonly ITenantContext _tenantContext;\n+    private readonly NotifyMaster.Core.Interfaces.ITenantService _tenantService;\n+    private readonly NotifyMaster.Core.Interfaces.ITenantContext _tenantContext;\n \n     public TenantAwarePluginManager(\n         ILogger<TenantAwarePluginManager> logger,\n         IPluginManager basePluginManager,\n"}, {"date": 1751229852125, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,10 +26,10 @@\n \n     public TenantAwarePluginManager(\n         ILogger<TenantAwarePluginManager> logger,\n         IPluginManager basePluginManager,\n-        ITenantService tenantService,\n-        ITenantContext tenantContext)\n+        NotifyMaster.Core.Interfaces.ITenantService tenantService,\n+        NotifyMaster.Core.Interfaces.ITenantContext tenantContext)\n     {\n         _logger = logger;\n         _basePluginManager = basePluginManager;\n         _tenantService = tenantService;\n"}, {"date": 1751230095872, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,9 @@\n using PluginCore.Interfaces;\n using PluginCore.Models;\n using PluginCore.Base;\n using ValidationResult = PluginCore.Models.ValidationResult;\n+using OperationResult = NotifyMaster.Core.Models.OperationResult;\n \n namespace NotifyMaster.Core.Services;\n \n public interface ITenantAwarePluginManager : IPluginManager\n"}, {"date": 1751230554146, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,15 +37,15 @@\n         _tenantContext = tenantContext;\n     }\n \n     // IPluginManager passthroughs\n-    public Task<OperationResult> LoadPluginsAsync(string pluginDirectory, CancellationToken cancellationToken = default)\n+    public Task<PluginCore.Models.OperationResult> LoadPluginsAsync(string pluginDirectory, CancellationToken cancellationToken = default)\n         => _basePluginManager.LoadPluginsAsync(pluginDirectory, cancellationToken);\n \n-    public Task<OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n+    public Task<PluginCore.Models.OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n         => _basePluginManager.LoadPluginAsync(pluginPath, cancellationToken);\n \n-    public Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default)\n+    public Task<PluginCore.Models.OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default)\n         => _basePluginManager.LoadPluginByNameAsync(pluginName, pluginDirectory, cancellationToken);\n \n     public IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType\n         => _basePluginManager.GetPlugins<T>();\n"}, {"date": 1751230570891, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -55,12 +55,12 @@\n \n     public PluginManifest? GetPluginManifest(string pluginName)\n         => _basePluginManager.GetPluginManifest(pluginName);\n \n-    public Task<OperationResult> UnloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n+    public Task<PluginCore.Models.OperationResult> UnloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n         => _basePluginManager.UnloadPluginAsync(pluginName, cancellationToken);\n \n-    public Task<OperationResult> ReloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n+    public Task<PluginCore.Models.OperationResult> ReloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n         => _basePluginManager.ReloadPluginAsync(pluginName, cancellationToken);\n \n     public Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(CancellationToken cancellationToken = default)\n         => _basePluginManager.GetPluginStatusesAsync(cancellationToken);\n"}], "date": 1751229827976, "name": "Commit-0", "content": "using PluginCore.Interfaces;\nusing PluginCore.Models;\nusing PluginCore.Base;\nusing ValidationResult = PluginCore.Models.ValidationResult;\n\nnamespace NotifyMaster.Core.Services;\n\npublic interface ITenantAwarePluginManager : IPluginManager\n{\n    Task<IReadOnlyList<Plugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default);\n    Task<T?> GetTenantPluginAsync<T>(string tenantId, string pluginName, CancellationToken cancellationToken = default) where T : class, IPluginType;\n    Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default);\n    Task<OperationResult> EnableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);\n    Task<OperationResult> DisableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);\n    Task<Dictionary<string, object>?> GetTenantPluginConfigurationAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);\n    Task<PluginMetrics?> GetTenantPluginMetricsAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);\n    Task<bool> HasReachedPluginLimitAsync(string tenantId, CancellationToken cancellationToken = default);\n}\n\npublic class TenantAwarePluginManager : ITenantAwarePluginManager\n{\n    private readonly ILogger<TenantAwarePluginManager> _logger;\n    private readonly IPluginManager _basePluginManager;\n    private readonly ITenantService _tenantService;\n    private readonly ITenantContext _tenantContext;\n\n    public TenantAwarePluginManager(\n        ILogger<TenantAwarePluginManager> logger,\n        IPluginManager basePluginManager,\n        ITenantService tenantService,\n        ITenantContext tenantContext)\n    {\n        _logger = logger;\n        _basePluginManager = basePluginManager;\n        _tenantService = tenantService;\n        _tenantContext = tenantContext;\n    }\n\n    // IPluginManager passthroughs\n    public Task<OperationResult> LoadPluginsAsync(string pluginDirectory, CancellationToken cancellationToken = default)\n        => _basePluginManager.LoadPluginsAsync(pluginDirectory, cancellationToken);\n\n    public Task<OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n        => _basePluginManager.LoadPluginAsync(pluginPath, cancellationToken);\n\n    public Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default)\n        => _basePluginManager.LoadPluginByNameAsync(pluginName, pluginDirectory, cancellationToken);\n\n    public IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType\n        => _basePluginManager.GetPlugins<T>();\n\n    public IReadOnlyList<PluginManifest> GetPluginManifests()\n        => _basePluginManager.GetPluginManifests();\n\n    public PluginManifest? GetPluginManifest(string pluginName)\n        => _basePluginManager.GetPluginManifest(pluginName);\n\n    public Task<OperationResult> UnloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n        => _basePluginManager.UnloadPluginAsync(pluginName, cancellationToken);\n\n    public Task<OperationResult> ReloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n        => _basePluginManager.ReloadPluginAsync(pluginName, cancellationToken);\n\n    public Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(CancellationToken cancellationToken = default)\n        => _basePluginManager.GetPluginStatusesAsync(cancellationToken);\n\n    public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n        => _basePluginManager.GetPlugin<T>(pluginName);\n\n    public Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n        => _basePluginManager.ValidatePluginAsync(pluginPath, cancellationToken);\n\n    public Task<PluginMetrics?> GetPluginMetricsAsync(string pluginName, CancellationToken cancellationToken = default)\n        => _basePluginManager.GetPluginMetricsAsync(pluginName, cancellationToken);\n\n    // Tenant-specific logic\n    public async Task<IReadOnlyList<Plugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);\n            var enabled = tenantPlugins.Where(p => p.IsEnabled).Select(p => p.PluginName).ToHashSet();\n\n            var result = new List<Plugin>();\n\n            foreach (var manifest in GetPluginManifests().Where(m => enabled.Contains(m.Name)))\n            {\n                // Create Plugin model from manifest and status information\n                var pluginStatuses = await GetPluginStatusesAsync(cancellationToken);\n                var status = pluginStatuses.FirstOrDefault(s => s.Name == manifest.Name);\n\n                var plugin = new Plugin\n                {\n                    Name = manifest.Name,\n                    Version = manifest.Version,\n                    Description = manifest.Description,\n                    Author = manifest.Author,\n                    Type = manifest.Type,\n                    IsLoaded = status?.IsLoaded ?? false,\n                    IsEnabled = manifest.IsEnabled,\n                    IsHealthy = status?.IsHealthy ?? false,\n                    LoadedAt = status?.LastChecked.DateTime ?? DateTime.MinValue,\n                    Configuration = tenantPlugins.FirstOrDefault(tp => tp.PluginName == manifest.Name)?.Configuration ?? new(),\n                    Metadata = manifest.Metadata ?? new(),\n                    ErrorMessage = status?.ErrorMessage\n                };\n\n                result.Add(plugin);\n            }\n\n            return result.AsReadOnly();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error retrieving tenant plugins for {TenantId}\", tenantId);\n            return Array.Empty<Plugin>();\n        }\n    }\n\n    public async Task<IReadOnlyList<T>> GetTenantPluginsAsync<T>(string tenantId, CancellationToken cancellationToken = default) where T : class, IPluginType\n    {\n        try\n        {\n            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);\n            var enabled = tenantPlugins.Where(p => p.IsEnabled).Select(p => p.PluginName).ToHashSet();\n\n            var result = new List<T>();\n\n            foreach (var manifest in GetPluginManifests().Where(m => enabled.Contains(m.Name)))\n            {\n                var plugin = GetPlugin<T>(manifest.Name);\n                if (plugin != null)\n                    result.Add(plugin);\n            }\n\n            return result.AsReadOnly();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error retrieving tenant plugins for {TenantId}\", tenantId);\n            return Array.Empty<T>();\n        }\n    }\n\n    public async Task<T?> GetTenantPluginAsync<T>(string tenantId, string pluginName, CancellationToken cancellationToken = default) where T : class, IPluginType\n    {\n        try\n        {\n            var pluginMeta = (await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken))\n                .FirstOrDefault(p => p.PluginName == pluginName && p.IsEnabled);\n\n            if (pluginMeta == null)\n                return null;\n\n            var plugin = GetPlugins<T>().FirstOrDefault(p => p.GetType().Name.Contains(pluginName));\n            if (plugin == null)\n                return null;\n\n            await ApplyTenantConfigurationAsync(plugin, pluginMeta.Configuration);\n            return plugin;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting plugin {PluginName} for tenant {TenantId}\", pluginName, tenantId);\n            return null;\n        }\n    }\n\n    public async Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            if (await HasReachedPluginLimitAsync(tenantId, cancellationToken))\n                return OperationResult.Failure(\"Tenant has reached plugin limit\");\n\n            await _tenantService.ConfigureTenantPluginAsync(tenantId, pluginName, configuration, cancellationToken);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error configuring plugin {PluginName} for tenant {TenantId}\", pluginName, tenantId);\n            return OperationResult.Failure(ex.Message);\n        }\n    }\n\n    public async Task<OperationResult> EnableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)\n    {\n        return await ConfigureTenantPluginAsync(tenantId, pluginName, new() { [\"enabled\"] = true }, cancellationToken);\n    }\n\n    public async Task<OperationResult> DisableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)\n    {\n        return await ConfigureTenantPluginAsync(tenantId, pluginName, new() { [\"enabled\"] = false }, cancellationToken);\n    }\n\n    public async Task<Dictionary<string, object>?> GetTenantPluginConfigurationAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var plugin = (await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken))\n                .FirstOrDefault(p => p.PluginName == pluginName);\n\n            return plugin?.Configuration;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error retrieving config for plugin {PluginName} on tenant {TenantId}\", pluginName, tenantId);\n            return null;\n        }\n    }\n\n    public async Task<PluginMetrics?> GetTenantPluginMetricsAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _basePluginManager.GetPluginMetricsAsync(pluginName, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error retrieving metrics for plugin {PluginName} on tenant {TenantId}\", pluginName, tenantId);\n            return null;\n        }\n    }\n\n    public async Task<bool> HasReachedPluginLimitAsync(string tenantId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var tenant = await _tenantService.GetTenantAsync(tenantId, cancellationToken);\n            if (tenant == null) return true;\n\n            var count = (await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken))\n                .Count(p => p.IsEnabled);\n\n            return count >= tenant.Limits.MaxPlugins;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error checking plugin limit for tenant {TenantId}\", tenantId);\n            return true;\n        }\n    }\n\n    private async Task ApplyTenantConfigurationAsync(object plugin, Dictionary<string, object> config)\n    {\n        foreach (var pair in config)\n        {\n            var property = plugin.GetType().GetProperty(pair.Key);\n            if (property?.CanWrite != true) continue;\n\n            try { property.SetValue(plugin, pair.Value); }\n            catch (Exception ex)\n            {\n                _logger.LogWarning(ex, \"Failed to apply property {Property} on {PluginType}\", pair.Key, plugin.GetType().Name);\n            }\n        }\n    }\n}\n\npublic static class TenantAwarePluginManagerExtensions\n{\n    public static IServiceCollection AddTenantAwarePluginManager(this IServiceCollection services)\n    {\n        return services.AddScoped<ITenantAwarePluginManager, TenantAwarePluginManager>();\n    }\n}\n\n"}]}