{"sourceFile": "src/Core/NotifyMaster.Core/Interfaces/IRoleService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1751230014041, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751230083860, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,10 @@\n // Using statements are handled by GlobalUsings.cs\n using Role = NotifyMaster.Core.Models.Role;\n using Permission = NotifyMaster.Core.Models.Permission;\n using RoleScope = NotifyMaster.Core.Models.RoleScope;\n+using User = NotifyMaster.Core.Models.User;\n+using OperationResult = NotifyMaster.Core.Models.OperationResult;\n \n namespace NotifyMaster.Core.Interfaces;\n \n /// <summary>\n"}, {"date": 1751232381857, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,5 @@\n // Using statements are handled by GlobalUsings.cs\n-using Role = NotifyMaster.Core.Models.Role;\n-using Permission = NotifyMaster.Core.Models.Permission;\n-using RoleScope = NotifyMaster.Core.Models.RoleScope;\n-using User = NotifyMaster.Core.Models.User;\n-using OperationResult = NotifyMaster.Core.Models.OperationResult;\n \n namespace NotifyMaster.Core.Interfaces;\n \n /// <summary>\n"}, {"date": 1751232995372, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -92,50 +92,4 @@\n     /// Deletes a permission\n     /// </summary>\n     Task<OperationResult> DeletePermissionAsync(string permissionId, CancellationToken cancellationToken = default);\n }\n-\n-/// <summary>\n-/// Request model for creating a role\n-/// </summary>\n-public class CreateRoleRequest\n-{\n-    public string Name { get; set; } = string.Empty;\n-    public string? Description { get; set; }\n-    public string? TenantId { get; set; }\n-    public RoleScope Scope { get; set; } = RoleScope.Tenant;\n-    public List<string> PermissionIds { get; set; } = new();\n-    public string? CreatedBy { get; set; }\n-}\n-\n-/// <summary>\n-/// Request model for updating a role\n-/// </summary>\n-public class UpdateRoleRequest\n-{\n-    public string? Name { get; set; }\n-    public string? Description { get; set; }\n-    public List<string>? PermissionIds { get; set; }\n-    public string? UpdatedBy { get; set; }\n-}\n-\n-/// <summary>\n-/// Request model for creating a permission\n-/// </summary>\n-public class CreatePermissionRequest\n-{\n-    public string Resource { get; set; } = string.Empty;\n-    public string Action { get; set; } = string.Empty;\n-    public string? Description { get; set; }\n-    public bool IsSystemPermission { get; set; } = false;\n-    public string? CreatedBy { get; set; }\n-}\n-\n-/// <summary>\n-/// Request model for updating a permission\n-/// </summary>\n-public class UpdatePermissionRequest\n-{\n-    public string? Resource { get; set; }\n-    public string? Action { get; set; }\n-    public string? Description { get; set; }\n-}\n"}, {"date": 1751233057402, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -57,39 +57,5 @@\n     /// </summary>\n     Task<OperationResult> RemovePermissionAsync(string roleId, string permissionId, CancellationToken cancellationToken = default);\n }\n \n-/// <summary>\n-/// Interface for permission management service\n-/// </summary>\n-public interface IPermissionService\n-{\n-    /// <summary>\n-    /// Gets a permission by ID\n-    /// </summary>\n-    Task<Permission?> GetPermissionAsync(string permissionId, CancellationToken cancellationToken = default);\n-    \n-    /// <summary>\n-    /// Gets a permission by resource and action\n-    /// </summary>\n-    Task<Permission?> GetPermissionAsync(string resource, string action, CancellationToken cancellationToken = default);\n-    \n-    /// <summary>\n-    /// Gets all permissions with pagination\n-    /// </summary>\n-    Task<IReadOnlyList<Permission>> GetPermissionsAsync(string? resource = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);\n-    \n-    /// <summary>\n-    /// Creates a new permission\n-    /// </summary>\n-    Task<OperationResult<Permission>> CreatePermissionAsync(CreatePermissionRequest request, CancellationToken cancellationToken = default);\n-    \n-    /// <summary>\n-    /// Updates an existing permission\n-    /// </summary>\n-    Task<OperationResult<Permission>> UpdatePermissionAsync(string permissionId, UpdatePermissionRequest request, CancellationToken cancellationToken = default);\n-    \n-    /// <summary>\n-    /// Deletes a permission\n-    /// </summary>\n-    Task<OperationResult> DeletePermissionAsync(string permissionId, CancellationToken cancellationToken = default);\n-}\n+\n"}], "date": 1751230014041, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\nusing Role = NotifyMaster.Core.Models.Role;\nusing Permission = NotifyMaster.Core.Models.Permission;\nusing RoleScope = NotifyMaster.Core.Models.RoleScope;\n\nnamespace NotifyMaster.Core.Interfaces;\n\n/// <summary>\n/// Interface for role management service\n/// </summary>\npublic interface IRoleService\n{\n    /// <summary>\n    /// Gets a role by ID\n    /// </summary>\n    Task<Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets a role by name\n    /// </summary>\n    Task<Role?> GetRoleByNameAsync(string roleName, RoleScope scope = RoleScope.Tenant, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets all roles with pagination\n    /// </summary>\n    Task<IReadOnlyList<Role>> GetRolesAsync(RoleScope? scope = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets roles by tenant\n    /// </summary>\n    Task<IReadOnlyList<Role>> GetRolesByTenantAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Creates a new role\n    /// </summary>\n    Task<OperationResult<Role>> CreateRoleAsync(CreateRoleRequest request, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Updates an existing role\n    /// </summary>\n    Task<OperationResult<Role>> UpdateRoleAsync(string roleId, UpdateRoleRequest request, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Deletes a role\n    /// </summary>\n    Task<OperationResult> DeleteRoleAsync(string roleId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets role permissions\n    /// </summary>\n    Task<IReadOnlyList<Permission>> GetRolePermissionsAsync(string roleId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Assigns a permission to a role\n    /// </summary>\n    Task<OperationResult> AssignPermissionAsync(string roleId, string permissionId, string? assignedBy = null, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Removes a permission from a role\n    /// </summary>\n    Task<OperationResult> RemovePermissionAsync(string roleId, string permissionId, CancellationToken cancellationToken = default);\n}\n\n/// <summary>\n/// Interface for permission management service\n/// </summary>\npublic interface IPermissionService\n{\n    /// <summary>\n    /// Gets a permission by ID\n    /// </summary>\n    Task<Permission?> GetPermissionAsync(string permissionId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets a permission by resource and action\n    /// </summary>\n    Task<Permission?> GetPermissionAsync(string resource, string action, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets all permissions with pagination\n    /// </summary>\n    Task<IReadOnlyList<Permission>> GetPermissionsAsync(string? resource = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Creates a new permission\n    /// </summary>\n    Task<OperationResult<Permission>> CreatePermissionAsync(CreatePermissionRequest request, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Updates an existing permission\n    /// </summary>\n    Task<OperationResult<Permission>> UpdatePermissionAsync(string permissionId, UpdatePermissionRequest request, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Deletes a permission\n    /// </summary>\n    Task<OperationResult> DeletePermissionAsync(string permissionId, CancellationToken cancellationToken = default);\n}\n\n/// <summary>\n/// Request model for creating a role\n/// </summary>\npublic class CreateRoleRequest\n{\n    public string Name { get; set; } = string.Empty;\n    public string? Description { get; set; }\n    public string? TenantId { get; set; }\n    public RoleScope Scope { get; set; } = RoleScope.Tenant;\n    public List<string> PermissionIds { get; set; } = new();\n    public string? CreatedBy { get; set; }\n}\n\n/// <summary>\n/// Request model for updating a role\n/// </summary>\npublic class UpdateRoleRequest\n{\n    public string? Name { get; set; }\n    public string? Description { get; set; }\n    public List<string>? PermissionIds { get; set; }\n    public string? UpdatedBy { get; set; }\n}\n\n/// <summary>\n/// Request model for creating a permission\n/// </summary>\npublic class CreatePermissionRequest\n{\n    public string Resource { get; set; } = string.Empty;\n    public string Action { get; set; } = string.Empty;\n    public string? Description { get; set; }\n    public bool IsSystemPermission { get; set; } = false;\n    public string? CreatedBy { get; set; }\n}\n\n/// <summary>\n/// Request model for updating a permission\n/// </summary>\npublic class UpdatePermissionRequest\n{\n    public string? Resource { get; set; }\n    public string? Action { get; set; }\n    public string? Description { get; set; }\n}\n"}]}