{"sourceFile": "src/Core/PluginCore/Utilities/ConfigurationValidator.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751228970765, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751228970765, "name": "Commit-0", "content": "using PluginCore.Models;\nusing System.Text.RegularExpressions;\nusing ValidationResult = PluginCore.Models.ValidationResult;\n\nnamespace PluginCore.Utilities;\n\n/// <summary>\n/// Utility class for validating gateway configurations.\n/// </summary>\npublic static class ConfigurationValidator\n{\n    /// <summary>\n    /// Validates a gateway configuration and returns validation results.\n    /// </summary>\n    public static ValidationResult ValidateGatewayConfiguration(\n        Dictionary<string, object> configuration,\n        GatewayConfigurationSchema schema)\n    {\n        var errors = new List<string>();\n\n        foreach (var field in schema.RequiredFields)\n        {\n            var result = ValidateField(configuration, field);\n            if (!result.IsValid)\n            {\n                errors.Add(result.ErrorMessage ?? $\"Validation failed for field {field.Name}\");\n            }\n        }\n\n        foreach (var field in schema.OptionalFields)\n        {\n            if (configuration.ContainsKey(field.Name))\n            {\n                var result = ValidateField(configuration, field);\n                if (!result.IsValid)\n                {\n                    errors.Add(result.ErrorMessage ?? $\"Validation failed for field {field.Name}\");\n                }\n            }\n        }\n\n        return new ValidationResult\n        {\n            IsValid = !errors.Any(),\n            ErrorMessage = errors.Any() ? string.Join(\"; \", errors) : null\n        };\n    }\n\n    /// <summary>\n    /// Validates a single configuration field.\n    /// </summary>\n    public static ValidationResult ValidateField(Dictionary<string, object> configuration, ConfigurationField field)\n    {\n        if (!configuration.TryGetValue(field.Name, out var value))\n        {\n            if (field.IsRequired)\n            {\n                return new ValidationResult\n                {\n                    IsValid = false,\n                    ErrorMessage = $\"Required field '{field.Name}' is missing\"\n                };\n            }\n            return new ValidationResult { IsValid = true };\n        }\n\n        if (value == null)\n        {\n            if (field.IsRequired)\n            {\n                return new ValidationResult\n                {\n                    IsValid = false,\n                    ErrorMessage = $\"Required field '{field.Name}' cannot be null\"\n                };\n            }\n            return new ValidationResult { IsValid = true };\n        }\n\n        var stringValue = value.ToString();\n        if (string.IsNullOrWhiteSpace(stringValue) && field.IsRequired)\n        {\n            return new ValidationResult\n            {\n                IsValid = false,\n                ErrorMessage = $\"Required field '{field.Name}' cannot be empty\"\n            };\n        }\n\n        // Type validation\n        var typeValidation = ValidateFieldType(stringValue, field.Type);\n        if (!typeValidation.IsValid)\n        {\n            return typeValidation;\n        }\n\n        // Pattern validation\n        if (!string.IsNullOrEmpty(field.ValidationPattern))\n        {\n            var patternValidation = ValidatePattern(stringValue, field.ValidationPattern, field.Name);\n            if (!patternValidation.IsValid)\n            {\n                return patternValidation;\n            }\n        }\n\n        // Length validation\n        if (field.MinLength.HasValue && stringValue.Length < field.MinLength.Value)\n        {\n            return new ValidationResult\n            {\n                IsValid = false,\n                ErrorMessage = $\"Field '{field.Name}' must be at least {field.MinLength.Value} characters long\"\n            };\n        }\n\n        if (field.MaxLength.HasValue && stringValue.Length > field.MaxLength.Value)\n        {\n            return new ValidationResult\n            {\n                IsValid = false,\n                ErrorMessage = $\"Field '{field.Name}' must be no more than {field.MaxLength.Value} characters long\"\n            };\n        }\n\n        // Allowed values validation\n        if (field.AllowedValues?.Any() == true && !field.AllowedValues.Contains(stringValue))\n        {\n            return new ValidationResult\n            {\n                IsValid = false,\n                ErrorMessage = $\"Field '{field.Name}' must be one of: {string.Join(\", \", field.AllowedValues)}\"\n            };\n        }\n\n        return new ValidationResult { IsValid = true };\n    }\n\n    /// <summary>\n    /// Validates the type of a field value.\n    /// </summary>\n    public static ValidationResult ValidateFieldType(string value, string expectedType)\n    {\n        return expectedType.ToLowerInvariant() switch\n        {\n            \"string\" => new ValidationResult { IsValid = true },\n            \"int\" or \"integer\" => ValidateInteger(value),\n            \"bool\" or \"boolean\" => ValidateBoolean(value),\n            \"double\" or \"decimal\" => ValidateDouble(value),\n            \"url\" => ValidateUrl(value),\n            \"email\" => ValidateEmail(value),\n            \"phone\" => ValidatePhoneNumber(value),\n            _ => new ValidationResult { IsValid = true } // Unknown types pass validation\n        };\n    }\n\n    /// <summary>\n    /// Validates a pattern using regular expressions.\n    /// </summary>\n    public static ValidationResult ValidatePattern(string value, string pattern, string fieldName)\n    {\n        try\n        {\n            var regex = new Regex(pattern, RegexOptions.IgnoreCase);\n            if (!regex.IsMatch(value))\n            {\n                return new ValidationResult\n                {\n                    IsValid = false,\n                    ErrorMessage = $\"Field '{fieldName}' does not match the required pattern\"\n                };\n            }\n            return new ValidationResult { IsValid = true };\n        }\n        catch (Exception ex)\n        {\n            return new ValidationResult\n            {\n                IsValid = false,\n                ErrorMessage = $\"Invalid validation pattern for field '{fieldName}': {ex.Message}\"\n            };\n        }\n    }\n\n    private static ValidationResult ValidateInteger(string value)\n    {\n        if (int.TryParse(value, out _))\n        {\n            return new ValidationResult { IsValid = true };\n        }\n        return new ValidationResult\n        {\n            IsValid = false,\n            ErrorMessage = \"Value must be a valid integer\"\n        };\n    }\n\n    private static ValidationResult ValidateBoolean(string value)\n    {\n        if (bool.TryParse(value, out _))\n        {\n            return new ValidationResult { IsValid = true };\n        }\n        return new ValidationResult\n        {\n            IsValid = false,\n            ErrorMessage = \"Value must be a valid boolean (true/false)\"\n        };\n    }\n\n    private static ValidationResult ValidateDouble(string value)\n    {\n        if (double.TryParse(value, out _))\n        {\n            return new ValidationResult { IsValid = true };\n        }\n        return new ValidationResult\n        {\n            IsValid = false,\n            ErrorMessage = \"Value must be a valid number\"\n        };\n    }\n\n    private static ValidationResult ValidateUrl(string value)\n    {\n        if (Uri.TryCreate(value, UriKind.Absolute, out var uri) &&\n            (uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps))\n        {\n            return new ValidationResult { IsValid = true };\n        }\n        return new ValidationResult\n        {\n            IsValid = false,\n            ErrorMessage = \"Value must be a valid HTTP or HTTPS URL\"\n        };\n    }\n\n    private static ValidationResult ValidateEmail(string value)\n    {\n        var emailRegex = new Regex(@\"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$\", RegexOptions.IgnoreCase);\n        if (emailRegex.IsMatch(value))\n        {\n            return new ValidationResult { IsValid = true };\n        }\n        return new ValidationResult\n        {\n            IsValid = false,\n            ErrorMessage = \"Value must be a valid email address\"\n        };\n    }\n\n    private static ValidationResult ValidatePhoneNumber(string value)\n    {\n        // Remove common formatting characters\n        var cleaned = Regex.Replace(value, @\"[\\s\\-\\(\\)\\+]\", \"\");\n        \n        // Basic validation: should be 10-15 digits\n        if (Regex.IsMatch(cleaned, @\"^\\d{10,15}$\"))\n        {\n            return new ValidationResult { IsValid = true };\n        }\n        return new ValidationResult\n        {\n            IsValid = false,\n            ErrorMessage = \"Value must be a valid phone number\"\n        };\n    }\n}\n\n/// <summary>\n/// Represents a configuration schema for a gateway.\n/// </summary>\npublic class GatewayConfigurationSchema\n{\n    public List<ConfigurationField> RequiredFields { get; set; } = new();\n    public List<ConfigurationField> OptionalFields { get; set; } = new();\n}\n\n/// <summary>\n/// Represents a configuration field definition.\n/// </summary>\npublic class ConfigurationField\n{\n    public string Name { get; set; } = string.Empty;\n    public string Type { get; set; } = \"string\";\n    public string Description { get; set; } = string.Empty;\n    public bool IsRequired { get; set; }\n    public bool IsSecret { get; set; }\n    public string? ValidationPattern { get; set; }\n    public int? MinLength { get; set; }\n    public int? MaxLength { get; set; }\n    public List<string>? AllowedValues { get; set; }\n    public object? DefaultValue { get; set; }\n\n    public ConfigurationField() { }\n\n    public ConfigurationField(string name, string type, string description, bool isRequired = false, bool isSecret = false)\n    {\n        Name = name;\n        Type = type;\n        Description = description;\n        IsRequired = isRequired;\n        IsSecret = isSecret;\n    }\n}\n"}]}