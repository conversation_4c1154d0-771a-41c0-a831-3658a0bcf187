using PluginCore.Extensions;

namespace PluginCore.Services;

/// <summary>
/// Background service that loads plugins on application startup.
/// </summary>
public class PluginLoaderService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<PluginLoaderService> _logger;
    private readonly GatewayPluginSystemOptions _options;

    public PluginLoaderService(
        IServiceProvider serviceProvider,
        ILogger<PluginLoaderService> logger,
        IOptions<GatewayPluginSystemOptions> options)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _options = options.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_options.AutoLoadPlugins)
        {
            _logger.LogInformation("Auto-loading plugins is disabled");
            return;
        }

        try
        {
            _logger.LogInformation("Starting plugin loading from directory: {PluginDirectory}", _options.PluginDirectory);

            var pluginManager = _serviceProvider.GetRequiredService<IPluginManager>();
            var result = await pluginManager.LoadPluginsAsync(_options.PluginDirectory, stoppingToken);

            if (result.IsSuccess)
            {
                _logger.LogInformation("Plugin loading completed successfully: {Message}", result.Message);
                
                // Log loaded plugins
                var manifests = pluginManager.GetPluginManifests();
                foreach (var manifest in manifests)
                {
                    _logger.LogInformation("Loaded plugin: {Name} v{Version} ({Type})", 
                        manifest.Name, manifest.Version, manifest.Type);
                }
            }
            else
            {
                _logger.LogError("Plugin loading failed: {Message}", result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load plugins during startup");
        }
    }
}

/// <summary>
/// Static helper class for loading plugins manually.
/// </summary>
public static class PluginLoader
{
    /// <summary>
    /// Loads plugins from the specified directory using the provided service provider.
    /// </summary>
    public static async Task<bool> LoadPluginsAsync(
        IServiceProvider serviceProvider, 
        string pluginDirectory,
        ILogger? logger = null)
    {
        try
        {
            logger?.LogInformation("Loading plugins from directory: {PluginDirectory}", pluginDirectory);

            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();
            var result = await pluginManager.LoadPluginsAsync(pluginDirectory);

            if (result.IsSuccess)
            {
                logger?.LogInformation("Plugins loaded successfully: {Message}", result.Message);
                
                // Log loaded plugins
                var manifests = pluginManager.GetPluginManifests();
                foreach (var manifest in manifests)
                {
                    logger?.LogInformation("Loaded plugin: {Name} v{Version} ({Type})", 
                        manifest.Name, manifest.Version, manifest.Type);
                }
                
                return true;
            }
            else
            {
                logger?.LogError("Failed to load plugins: {Message}", result.Message);
                return false;
            }
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "Exception occurred while loading plugins");
            return false;
        }
    }

    /// <summary>
    /// Gets all loaded plugins of a specific type.
    /// </summary>
    public static IReadOnlyList<T> GetPlugins<T>(IServiceProvider serviceProvider) where T : class, IPluginType
    {
        try
        {
            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();
            return pluginManager.GetPlugins<T>();
        }
        catch (Exception)
        {
            return new List<T>().AsReadOnly();
        }
    }

    /// <summary>
    /// Gets a specific plugin by name and type.
    /// </summary>
    public static T? GetPlugin<T>(IServiceProvider serviceProvider, string pluginName) where T : class, IPluginType
    {
        try
        {
            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();
            return pluginManager.GetPlugin<T>(pluginName);
        }
        catch (Exception)
        {
            return null;
        }
    }

    /// <summary>
    /// Gets the status of all loaded plugins.
    /// </summary>
    public static async Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(
        IServiceProvider serviceProvider,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();
            return await pluginManager.GetPluginStatusesAsync(cancellationToken);
        }
        catch (Exception)
        {
            return new List<PluginStatus>().AsReadOnly();
        }
    }

    /// <summary>
    /// Validates a plugin file before loading.
    /// </summary>
    public static async Task<bool> ValidatePluginAsync(
        IServiceProvider serviceProvider,
        string pluginPath,
        ILogger? logger = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();
            var result = await pluginManager.ValidatePluginAsync(pluginPath, cancellationToken);

            if (result.IsValid)
            {
                logger?.LogInformation("Plugin validation successful: {PluginPath}", pluginPath);
                return true;
            }
            else
            {
                logger?.LogWarning("Plugin validation failed for {PluginPath}: {ErrorMessage}", 
                    pluginPath, result.ErrorMessage);
                return false;
            }
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, "Exception occurred while validating plugin: {PluginPath}", pluginPath);
            return false;
        }
    }
}

/// <summary>
/// Extension methods for easier plugin loading in startup.
/// </summary>
public static class PluginLoaderExtensions
{
    /// <summary>
    /// Adds the plugin loader service to run on startup.
    /// </summary>
    public static IServiceCollection AddPluginLoader(this IServiceCollection services)
    {
        services.AddHostedService<PluginLoaderService>();
        return services;
    }

    /// <summary>
    /// Loads plugins immediately during application startup.
    /// </summary>
    public static async Task<IServiceProvider> LoadPluginsAsync(
        this IServiceProvider serviceProvider,
        string pluginDirectory,
        ILogger? logger = null)
    {
        await PluginLoader.LoadPluginsAsync(serviceProvider, pluginDirectory, logger);
        return serviceProvider;
    }

    /// <summary>
    /// Configures and loads the complete gateway plugin system.
    /// </summary>
    public static IServiceCollection AddCompleteGatewayPluginSystem(
        this IServiceCollection services,
        string pluginDirectory = "plugins",
        bool autoLoad = true)
    {
        // Add the core plugin system
        services.AddGatewayPluginSystem();
        
        // Configure options
        services.ConfigureGatewayPluginSystem(options =>
        {
            options.PluginDirectory = pluginDirectory;
            options.AutoLoadPlugins = autoLoad;
        });

        // Add plugin loader if auto-load is enabled
        if (autoLoad)
        {
            services.AddPluginLoader();
        }

        return services;
    }
}

/// <summary>
/// Simple plugin discovery helper.
/// </summary>
public static class PluginDiscovery
{
    /// <summary>
    /// Discovers all plugin files in a directory.
    /// </summary>
    public static IEnumerable<string> DiscoverPluginFiles(string directory)
    {
        if (!Directory.Exists(directory))
        {
            return Enumerable.Empty<string>();
        }

        return Directory.GetFiles(directory, "*.dll", SearchOption.AllDirectories)
            .Where(file => !Path.GetFileName(file).StartsWith("System.") &&
                          !Path.GetFileName(file).StartsWith("Microsoft.") &&
                          !Path.GetFileName(file).StartsWith("Newtonsoft.") &&
                          Path.GetFileName(file).Contains("Plugin"));
    }

    /// <summary>
    /// Gets plugin information without loading the assembly.
    /// </summary>
    public static PluginFileInfo? GetPluginFileInfo(string pluginPath)
    {
        try
        {
            if (!File.Exists(pluginPath))
                return null;

            var fileInfo = new FileInfo(pluginPath);
            return new PluginFileInfo(
                Path: pluginPath,
                FileName: fileInfo.Name,
                Size: fileInfo.Length,
                LastModified: fileInfo.LastWriteTime,
                Directory: fileInfo.DirectoryName ?? ""
            );
        }
        catch
        {
            return null;
        }
    }
}

/// <summary>
/// Information about a plugin file.
/// </summary>
public record PluginFileInfo(
    string Path,
    string FileName,
    long Size,
    DateTime LastModified,
    string Directory);
