using NotifyMaster.Core.Models;

namespace NotifyMasterApi.Services.Setup;

/// <summary>
/// Implementation of the setup service for system initialization
/// </summary>
public class SetupService : ISetupService
{
    private readonly INotifyMasterDbContext _context;
    private readonly ITenantService _tenantService;
    private readonly IUserService _userService;
    private readonly IPermissionService _permissionService;
    private readonly ILogger<SetupService> _logger;
    private readonly IConfiguration _configuration;

    public SetupService(
        INotifyMasterDbContext context,
        ITenantService tenantService,
        IUserService userService,
        IPermissionService permissionService,
        ILogger<SetupService> logger,
        IConfiguration configuration)
    {
        _context = context;
        _tenantService = tenantService;
        _userService = userService;
        _permissionService = permissionService;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<SetupStatus> GetSetupStatusAsync()
    {
        try
        {
            var status = new SetupStatus();

            // Check database connection
            try
            {
                await _context.Database.CanConnectAsync();
                status.DatabaseConfigured = true;
                status.CompletedSteps.Add("Database Connection");
            }
            catch
            {
                status.DatabaseConfigured = false;
                status.RequiredSteps.Add("Configure Database Connection");
            }

            // Check if we have any tenants
            if (status.DatabaseConfigured)
            {
                try
                {
                    var tenantCount = await _context.Tenants.CountAsync();
                    status.HasRootTenant = tenantCount > 0;
                    if (status.HasRootTenant)
                    {
                        status.CompletedSteps.Add("Root Tenant Created");
                    }
                    else
                    {
                        status.RequiredSteps.Add("Create Root Tenant");
                    }
                }
                catch
                {
                    status.HasRootTenant = false;
                    status.RequiredSteps.Add("Create Root Tenant");
                }

                // Check if we have any admin users
                try
                {
                    var adminCount = await _context.Users.CountAsync();
                    status.HasAdminUser = adminCount > 0;
                    if (status.HasAdminUser)
                    {
                        status.CompletedSteps.Add("Admin User Created");
                    }
                    else
                    {
                        status.RequiredSteps.Add("Create Admin User");
                    }
                }
                catch
                {
                    status.HasAdminUser = false;
                    status.RequiredSteps.Add("Create Admin User");
                }
            }

            // System is initialized if all key components are present
            status.IsInitialized = status.DatabaseConfigured && status.HasRootTenant && status.HasAdminUser;
            
            if (status.IsInitialized)
            {
                status.Version = "2.0.0";
                status.InitializedAt = DateTime.UtcNow; // This should be stored in database
            }

            return status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting setup status");
            return new SetupStatus
            {
                IsInitialized = false,
                RequiredSteps = { "Fix System Errors" }
            };
        }
    }

    public async Task<bool> InitializeSystemAsync(InitializeSystemRequest request)
    {
        try
        {
            _logger.LogInformation("🚀 Starting system initialization...");

            // Step 1: Seed default permissions and roles
            _logger.LogInformation("📋 Seeding default permissions and roles...");
            await _permissionService.SeedDefaultPermissionsAsync();

            // Step 2: Create the first tenant
            _logger.LogInformation("🏢 Creating first tenant: {TenantName}", request.TenantName);
            var tenantResult = await _tenantService.CreateTenantAsync(new CreateTenantRequest
            {
                Name = request.TenantName,
                Description = request.TenantDescription,
                Domain = request.TenantDomain,
                Plan = request.TenantPlan,
                CreatedBy = "System Setup"
            });

            if (!tenantResult.IsSuccess)
            {
                _logger.LogError("❌ Failed to create first tenant: {Error}", tenantResult.Message);
                return false;
            }

            var tenant = tenantResult.Data;
            if (tenant == null)
            {
                _logger.LogError("❌ Tenant creation returned null data");
                return false;
            }

            // Step 3: Get or create SuperAdmin role
            var superAdminRole = await _permissionService.GetRoleByNameAsync("SuperAdmin");
            if (superAdminRole == null)
            {
                _logger.LogError("❌ SuperAdmin role not found after seeding");
                return false;
            }

            // Step 4: Create the first admin user
            _logger.LogInformation("👤 Creating first admin user: {AdminEmail}", request.AdminEmail);
            var userResult = await _userService.CreateUserAsync(new CreateUserRequest
            {
                TenantId = tenant.Id,
                Username = request.AdminEmail.Split('@')[0], // Use email prefix as username
                Email = request.AdminEmail,
                FirstName = request.AdminFirstName,
                LastName = request.AdminLastName,
                Password = request.AdminPassword,
                Status = UserStatus.Active,
                RoleIds = new List<string> { superAdminRole.Id },
                CreatedBy = "System Setup"
            });

            if (!userResult.IsSuccess)
            {
                _logger.LogError("❌ Failed to create first admin user: {Error}", userResult.Message);
                return false;
            }

            // Step 5: Mark system as initialized
            await LockSystemAsync();

            _logger.LogInformation("✅ System initialization completed successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ System initialization failed");
            return false;
        }
    }

    public async Task<bool> IsSystemInitializedAsync()
    {
        var status = await GetSetupStatusAsync();
        return status.IsInitialized;
    }

    public async Task LockSystemAsync()
    {
        try
        {
            // This could store a flag in the database or configuration
            // For now, we'll just log it
            _logger.LogInformation("🔒 System locked - initialization complete");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error locking system");
        }
    }

    public async Task<ValidationResult> ValidateConfigurationAsync(SetupConfiguration configuration)
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // Validate database configuration
            if (string.IsNullOrEmpty(configuration.Database.ConnectionString))
            {
                result.Errors.Add("Database connection string is required");
                result.IsValid = false;
            }

            // Validate tenant configuration
            if (string.IsNullOrEmpty(configuration.Tenant.Name))
            {
                result.Errors.Add("Tenant name is required");
                result.IsValid = false;
            }

            if (string.IsNullOrEmpty(configuration.Tenant.Domain))
            {
                result.Errors.Add("Tenant domain is required");
                result.IsValid = false;
            }

            // Validate admin user configuration
            if (string.IsNullOrEmpty(configuration.AdminUser.Email))
            {
                result.Errors.Add("Admin email is required");
                result.IsValid = false;
            }

            if (string.IsNullOrEmpty(configuration.AdminUser.Password))
            {
                result.Errors.Add("Admin password is required");
                result.IsValid = false;
            }

            // Test database connection if requested
            if (configuration.Database.TestConnection && !string.IsNullOrEmpty(configuration.Database.ConnectionString))
            {
                try
                {
                    // This would test the connection - simplified for now
                    result.ValidationData["DatabaseConnectionTest"] = "Success";
                }
                catch (Exception ex)
                {
                    result.Errors.Add($"Database connection test failed: {ex.Message}");
                    result.IsValid = false;
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating configuration");
            result.Errors.Add($"Validation error: {ex.Message}");
            result.IsValid = false;
            return result;
        }
    }
}
