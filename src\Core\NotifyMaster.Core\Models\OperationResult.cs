// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Models;

/// <summary>
/// Represents the result of an operation
/// </summary>
public class OperationResult
{
    public bool IsSuccess { get; init; }
    public string Message { get; init; } = string.Empty;
    public Exception? Exception { get; init; }

    protected OperationResult(bool isSuccess, string message, Exception? exception = null)
    {
        IsSuccess = isSuccess;
        Message = message;
        Exception = exception;
    }

    public static OperationResult Success(string message = "Operation completed successfully")
    {
        return new OperationResult(true, message);
    }

    public static OperationResult Failure(string message, Exception? exception = null)
    {
        return new OperationResult(false, message, exception);
    }
}

/// <summary>
/// Represents the result of an operation with a return value
/// </summary>
/// <typeparam name="T">The type of the return value</typeparam>
public class OperationResult<T> : OperationResult
{
    public T? Data { get; init; }

    private OperationResult(bool isSuccess, string message, T? data = default, Exception? exception = null)
        : base(isSuccess, message, exception)
    {
        Data = data;
    }

    public static OperationResult<T> Success(T data, string message = "Operation completed successfully")
    {
        return new OperationResult<T>(true, message, data);
    }

    public static new OperationResult<T> Failure(string message, Exception? exception = null)
    {
        return new OperationResult<T>(false, message, default, exception);
    }
}
