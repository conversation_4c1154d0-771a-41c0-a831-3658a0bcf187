// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Interface for processing queued messages
/// </summary>
public interface IQueueProcessor
{
    /// <summary>
    /// Processes a queued message
    /// </summary>
    Task ProcessMessageAsync<T>(QueueMessage<T> message, CancellationToken cancellationToken = default) where T : class;
}

/// <summary>
/// Interface for message handlers
/// </summary>
public interface IMessageHandler<T> where T : class
{
    /// <summary>
    /// Handles a specific type of message
    /// </summary>
    Task<OperationResult> HandleAsync(T message, QueueMessage<T> queueMessage, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the message type this handler supports
    /// </summary>
    Type MessageType { get; }
    
    /// <summary>
    /// Gets the queue name this handler processes
    /// </summary>
    string QueueName { get; }
}

/// <summary>
/// Base class for message handlers
/// </summary>
public abstract class MessageHandlerBase<T> : IMessageHandler<T> where T : class
{
    protected readonly ILogger Logger;

    protected MessageHandlerBase(ILogger logger)
    {
        Logger = logger;
    }

    public abstract Task<OperationResult> HandleAsync(T message, QueueMessage<T> queueMessage, CancellationToken cancellationToken = default);
    
    public virtual Type MessageType => typeof(T);
    
    public abstract string QueueName { get; }

    protected virtual async Task SaveToDatabase<TEntity>(TEntity entity, CancellationToken cancellationToken = default) where TEntity : class
    {
        // Override in derived classes to implement database saving
        Logger.LogDebug("Saving entity of type {EntityType} to database", typeof(TEntity).Name);
        await Task.CompletedTask;
    }

    protected virtual async Task<OperationResult> SendResponse<TResponse>(TResponse response, QueueMessage<T> queueMessage, CancellationToken cancellationToken = default) where TResponse : class
    {
        // Override in derived classes to implement response sending
        Logger.LogDebug("Sending response of type {ResponseType} for message {MessageId}", typeof(TResponse).Name, queueMessage.Id);
        return OperationResult.Success("Response sent successfully");
    }
}

/// <summary>
/// Message types for different operations
/// </summary>
public static class MessageTypes
{
    /// <summary>
    /// Message for sending notifications
    /// </summary>
    public class SendNotificationMessage : IHasTenantId, IHasCorrelationId
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string TenantId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string PluginName { get; set; } = string.Empty;
        public string Recipient { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string? Subject { get; set; }
        public string? From { get; set; }
        public Dictionary<string, string> Headers { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
        public DateTime? ScheduledAt { get; set; }
        public string? CorrelationId { get; set; }
        public int Priority { get; set; } = 0;
    }

    /// <summary>
    /// Message for user operations
    /// </summary>
    public class UserOperationMessage : IHasTenantId, IHasCorrelationId
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string TenantId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string Operation { get; set; } = string.Empty; // CREATE, UPDATE, DELETE, etc.
        public Dictionary<string, object> Data { get; set; } = new();
        public string? RequestedBy { get; set; }
        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
        public string? CorrelationId { get; set; }
    }

    /// <summary>
    /// Message for tenant operations
    /// </summary>
    public class TenantOperationMessage
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string TenantId { get; set; } = string.Empty;
        public string Operation { get; set; } = string.Empty; // CREATE, UPDATE, DELETE, SUSPEND, etc.
        public Dictionary<string, object> Data { get; set; } = new();
        public string? RequestedBy { get; set; }
        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
        public string? CorrelationId { get; set; }
    }

    /// <summary>
    /// Message for plugin operations
    /// </summary>
    public class PluginOperationMessage
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string TenantId { get; set; } = string.Empty;
        public string PluginName { get; set; } = string.Empty;
        public string Operation { get; set; } = string.Empty; // CONFIGURE, ENABLE, DISABLE, etc.
        public Dictionary<string, object> Configuration { get; set; } = new();
        public string? RequestedBy { get; set; }
        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
        public string? CorrelationId { get; set; }
    }

    /// <summary>
    /// Message for webhook delivery
    /// </summary>
    public class WebhookDeliveryMessage
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string TenantId { get; set; } = string.Empty;
        public string WebhookUrl { get; set; } = string.Empty;
        public string Event { get; set; } = string.Empty;
        public Dictionary<string, object> Payload { get; set; } = new();
        public Dictionary<string, string> Headers { get; set; } = new();
        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
        public int RetryCount { get; set; } = 0;
        public int MaxRetries { get; set; } = 3;
        public string? CorrelationId { get; set; }
    }

    /// <summary>
    /// Message for report generation
    /// </summary>
    public class ReportGenerationMessage
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string TenantId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string ReportType { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public string Format { get; set; } = "PDF"; // PDF, CSV, XLSX, etc.
        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? CorrelationId { get; set; }
    }
}

/// <summary>
/// Interfaces for message properties
/// </summary>
public interface IHasTenantId
{
    string TenantId { get; set; }
}

public interface IHasCorrelationId
{
    string? CorrelationId { get; set; }
}
