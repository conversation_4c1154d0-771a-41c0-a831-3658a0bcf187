{"sourceFile": "src/Implementations/NotifyMasterApi/Features/Users/<USER>", "activeCommit": 0, "commits": [{"activePatchIndex": 10, "patches": [{"date": 1751234047815, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751234069919, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -415,4 +415,118 @@\n             await SendErrorsAsync(500, ct);\n         }\n     }\n }\n+\n+/// <summary>\n+/// Endpoint for assigning a role to a user\n+/// </summary>\n+public class AssignRoleEndpoint : Endpoint<AssignRoleRequest, object>\n+{\n+    private readonly IUserService _userService;\n+    private readonly ITenantContext _tenantContext;\n+    private readonly ILogger<AssignRoleEndpoint> _logger;\n+\n+    public AssignRoleEndpoint(IUserService userService, ITenantContext tenantContext, ILogger<AssignRoleEndpoint> logger)\n+    {\n+        _userService = userService;\n+        _tenantContext = tenantContext;\n+        _logger = logger;\n+    }\n+\n+    public override void Configure()\n+    {\n+        Post(\"/api/users/{userId}/roles/{roleId}\");\n+        Policies(\"RequireTenantAdmin\");\n+        Summary(s =>\n+        {\n+            s.Summary = \"Assign Role to User\";\n+            s.Description = \"Assign a role to a user\";\n+            s.Response(200, \"Role assigned successfully\");\n+            s.Response(400, \"Invalid request\");\n+            s.Response(401, \"Unauthorized\");\n+            s.Response(403, \"Forbidden\");\n+            s.Response(404, \"User or role not found\");\n+        });\n+        Tags(\"User Management\");\n+    }\n+\n+    public override async Task HandleAsync(AssignRoleRequest req, CancellationToken ct)\n+    {\n+        try\n+        {\n+            var userId = Route<string>(\"userId\")!;\n+            var roleId = Route<string>(\"roleId\")!;\n+\n+            var result = await _userService.AssignRoleAsync(userId, roleId, _tenantContext.TenantId, _tenantContext.UserId, ct);\n+\n+            if (!result.IsSuccess)\n+            {\n+                await SendAsync(new { Success = false, Message = result.Message }, 400, ct);\n+                return;\n+            }\n+\n+            await SendOkAsync(new { Success = true, Message = \"Role assigned successfully\" }, ct);\n+        }\n+        catch (Exception ex)\n+        {\n+            _logger.LogError(ex, \"Error assigning role {RoleId} to user {UserId}\", req.RoleId, req.UserId);\n+            await SendErrorsAsync(500, ct);\n+        }\n+    }\n+}\n+\n+/// <summary>\n+/// Endpoint for removing a role from a user\n+/// </summary>\n+public class RemoveRoleEndpoint : Endpoint<AssignRoleRequest, object>\n+{\n+    private readonly IUserService _userService;\n+    private readonly ILogger<RemoveRoleEndpoint> _logger;\n+\n+    public RemoveRoleEndpoint(IUserService userService, ILogger<RemoveRoleEndpoint> logger)\n+    {\n+        _userService = userService;\n+        _logger = logger;\n+    }\n+\n+    public override void Configure()\n+    {\n+        Delete(\"/api/users/{userId}/roles/{roleId}\");\n+        Policies(\"RequireTenantAdmin\");\n+        Summary(s =>\n+        {\n+            s.Summary = \"Remove Role from User\";\n+            s.Description = \"Remove a role from a user\";\n+            s.Response(200, \"Role removed successfully\");\n+            s.Response(400, \"Invalid request\");\n+            s.Response(401, \"Unauthorized\");\n+            s.Response(403, \"Forbidden\");\n+            s.Response(404, \"User or role not found\");\n+        });\n+        Tags(\"User Management\");\n+    }\n+\n+    public override async Task HandleAsync(AssignRoleRequest req, CancellationToken ct)\n+    {\n+        try\n+        {\n+            var userId = Route<string>(\"userId\")!;\n+            var roleId = Route<string>(\"roleId\")!;\n+\n+            var result = await _userService.RemoveRoleAsync(userId, roleId, ct);\n+\n+            if (!result.IsSuccess)\n+            {\n+                await SendAsync(new { Success = false, Message = result.Message }, 400, ct);\n+                return;\n+            }\n+\n+            await SendOkAsync(new { Success = true, Message = \"Role removed successfully\" }, ct);\n+        }\n+        catch (Exception ex)\n+        {\n+            _logger.LogError(ex, \"Error removing role {RoleId} from user {UserId}\", req.RoleId, req.UserId);\n+            await SendErrorsAsync(500, ct);\n+        }\n+    }\n+}\n"}, {"date": 1751234335273, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,9 @@\n using MediatR;\n using NotifyMaster.Core.Interfaces;\n using NotifyMaster.Core.Models;\n using NotifyMaster.Core.Services;\n+using NotifyMasterApi.Infrastructure;\n \n namespace NotifyMasterApi.Features.Users;\n \n /// <summary>\n"}, {"date": 1751234353199, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -82,34 +82,30 @@\n \n /// <summary>\n /// Endpoint for getting all users\n /// </summary>\n-public class GetUsersEndpoint : Endpoint<GetUsersRequest, object>\n+[SkipQueueProcessing] // Read-only operation doesn't need queuing\n+public class GetUsersEndpoint : StandardEndpointBase<GetUsersRequest, object>\n {\n     private readonly IUserService _userService;\n     private readonly ITenantContext _tenantContext;\n-    private readonly ILogger<GetUsersEndpoint> _logger;\n \n     public GetUsersEndpoint(IUserService userService, ITenantContext tenantContext, ILogger<GetUsersEndpoint> logger)\n+        : base(logger)\n     {\n         _userService = userService;\n         _tenantContext = tenantContext;\n-        _logger = logger;\n     }\n \n-    public override void Configure()\n+    protected override void ConfigureEndpoint()\n     {\n-        Get(\"/api/users\");\n-        Policies(\"RequireTenantAdmin\");\n-        Summary(s =>\n-        {\n-            s.Summary = \"Get Users\";\n-            s.Description = \"Get a paginated list of users in the tenant\";\n-            s.Response(200, \"Users retrieved successfully\");\n-            s.Response(401, \"Unauthorized\");\n-            s.Response(403, \"Forbidden - Tenant admin access required\");\n-        });\n-        Tags(\"User Management\");\n+        this.ConfigureManagementEndpoint(\n+            \"GET\",\n+            \"/api/users\",\n+            \"Get Users\",\n+            \"Get a paginated list of users in the tenant with filtering and sorting options\",\n+            \"RequireTenantAdmin\",\n+            new[] { \"User Management\" });\n     }\n \n     public override async Task HandleAsync(GetUsersRequest req, CancellationToken ct)\n     {\n"}, {"date": 1751234370770, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -113,15 +113,15 @@\n         {\n             var tenantId = req.TenantId ?? _tenantContext.TenantId;\n             if (string.IsNullOrEmpty(tenantId))\n             {\n-                await SendErrorsAsync(400, ct);\n+                await SendAsync(CreateErrorResponse(\"Tenant ID is required\"), 400, ct);\n                 return;\n             }\n \n             var users = await _userService.GetUsersByTenantAsync(tenantId, req.Skip, req.Take, ct);\n-            \n-            var response = new\n+\n+            var userData = new\n             {\n                 Users = users.Select(u => new\n                 {\n                     u.Id,\n@@ -139,14 +139,14 @@\n                 Skip = req.Skip,\n                 Take = req.Take\n             };\n \n-            await SendOkAsync(response, ct);\n+            await SendOkAsync(CreateSuccessResponse(userData, \"Users retrieved successfully\"), ct);\n         }\n         catch (Exception ex)\n         {\n-            _logger.LogError(ex, \"Error getting users for tenant\");\n-            await SendErrorsAsync(500, ct);\n+            Logger.LogError(ex, \"Error getting users for tenant\");\n+            await SendAsync(CreateErrorResponse(\"Failed to retrieve users\", ex.Message), 500, ct);\n         }\n     }\n }\n \n"}, {"date": 1751234387097, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -152,33 +152,27 @@\n \n /// <summary>\n /// Endpoint for getting a specific user\n /// </summary>\n-public class GetUserEndpoint : Endpoint<GetUserRequest, object>\n+[SkipQueueProcessing] // Read-only operation doesn't need queuing\n+public class GetUserEndpoint : StandardEndpointBase<GetUserRequest, object>\n {\n     private readonly IUserService _userService;\n-    private readonly ILogger<GetUserEndpoint> _logger;\n \n-    public GetUserEndpoint(IUserService userService, ILogger<GetUserEndpoint> logger)\n+    public GetUserEndpoint(IUserService userService, ILogger<GetUserEndpoint> logger) : base(logger)\n     {\n         _userService = userService;\n-        _logger = logger;\n     }\n \n-    public override void Configure()\n+    protected override void ConfigureEndpoint()\n     {\n-        Get(\"/api/users/{userId}\");\n-        Policies(\"RequireTenantAccess\");\n-        Summary(s =>\n-        {\n-            s.Summary = \"Get User\";\n-            s.Description = \"Get details of a specific user\";\n-            s.Response(200, \"User retrieved successfully\");\n-            s.Response(401, \"Unauthorized\");\n-            s.Response(403, \"Forbidden\");\n-            s.Response(404, \"User not found\");\n-        });\n-        Tags(\"User Management\");\n+        this.ConfigureManagementEndpoint(\n+            \"GET\",\n+            \"/api/users/{userId}\",\n+            \"Get User\",\n+            \"Get details of a specific user including roles and permissions\",\n+            \"RequireTenantAccess\",\n+            new[] { \"User Management\" });\n     }\n \n     public override async Task HandleAsync(GetUserRequest req, CancellationToken ct)\n     {\n"}, {"date": 1751234413839, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -180,16 +180,16 @@\n         {\n             var user = await _userService.GetUserAsync(req.UserId, ct);\n             if (user == null)\n             {\n-                await SendNotFoundAsync(ct);\n+                await SendAsync(CreateErrorResponse(\"User not found\"), 404, ct);\n                 return;\n             }\n \n             var roles = await _userService.GetUserRolesAsync(req.UserId, null, ct);\n             var permissions = await _userService.GetUserPermissionsAsync(req.UserId, null, ct);\n \n-            var response = new\n+            var userData = new\n             {\n                 User = new\n                 {\n                     user.Id,\n@@ -206,14 +206,14 @@\n                 Roles = roles.Select(r => new { r.Id, r.Name, r.Description }).ToList(),\n                 Permissions = permissions.Select(p => new { p.Id, p.Resource, p.Action, p.Description }).ToList()\n             };\n \n-            await SendOkAsync(response, ct);\n+            await SendOkAsync(CreateSuccessResponse(userData, \"User retrieved successfully\"), ct);\n         }\n         catch (Exception ex)\n         {\n-            _logger.LogError(ex, \"Error getting user {UserId}\", req.UserId);\n-            await SendErrorsAsync(500, ct);\n+            Logger.LogError(ex, \"Error getting user {UserId}\", req.UserId);\n+            await SendAsync(CreateErrorResponse(\"Failed to retrieve user\", ex.Message), 500, ct);\n         }\n     }\n }\n \n"}, {"date": 1751234434911, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -219,36 +219,29 @@\n \n /// <summary>\n /// Endpoint for creating a new user\n /// </summary>\n-public class CreateUserEndpoint : Endpoint<CreateUserRequest, object>\n+public class CreateUserEndpoint : QueueFirstEndpointBase<CreateUserRequest, object>\n {\n     private readonly IUserService _userService;\n     private readonly ITenantContext _tenantContext;\n-    private readonly ILogger<CreateUserEndpoint> _logger;\n \n     public CreateUserEndpoint(IUserService userService, ITenantContext tenantContext, ILogger<CreateUserEndpoint> logger)\n+        : base(logger)\n     {\n         _userService = userService;\n         _tenantContext = tenantContext;\n-        _logger = logger;\n     }\n \n-    public override void Configure()\n+    protected override void ConfigureEndpoint()\n     {\n-        Post(\"/api/users\");\n-        Policies(\"RequireTenantAdmin\");\n-        Summary(s =>\n-        {\n-            s.Summary = \"Create User\";\n-            s.Description = \"Create a new user in the tenant\";\n-            s.Response(201, \"User created successfully\");\n-            s.Response(400, \"Invalid request\");\n-            s.Response(401, \"Unauthorized\");\n-            s.Response(403, \"Forbidden - Tenant admin access required\");\n-            s.Response(409, \"User with email already exists\");\n-        });\n-        Tags(\"User Management\");\n+        this.ConfigureManagementEndpoint(\n+            \"POST\",\n+            \"/api/users\",\n+            \"Create User\",\n+            \"Create a new user in the tenant with role and permission assignment\",\n+            \"RequireTenantAdmin\",\n+            new[] { \"User Management\" });\n     }\n \n     public override async Task HandleAsync(CreateUserRequest req, CancellationToken ct)\n     {\n"}, {"date": 1751234465252, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -249,9 +249,9 @@\n         {\n             var tenantId = req.TenantId ?? _tenantContext.TenantId;\n             if (string.IsNullOrEmpty(tenantId))\n             {\n-                await SendErrorsAsync(400, ct);\n+                await SendAsync(CreateErrorResponse(\"Tenant ID is required\"), 400, ct);\n                 return;\n             }\n \n             var createRequest = new NotifyMaster.Core.Models.CreateUserRequest\n@@ -265,12 +265,12 @@\n                 CreatedBy = _tenantContext.UserId\n             };\n \n             var result = await _userService.CreateUserAsync(createRequest, ct);\n-            \n+\n             if (!result.IsSuccess)\n             {\n-                await SendAsync(new { Success = false, Message = result.Message }, 400, ct);\n+                await SendAsync(CreateErrorResponse(\"Failed to create user\", result.Message), 400, ct);\n                 return;\n             }\n \n             // Assign roles if provided\n@@ -284,31 +284,27 @@\n             {\n                 await _userService.GrantPermissionAsync(result.Data!.Id, permissionId, tenantId, _tenantContext.UserId, ct);\n             }\n \n-            var response = new\n+            var userData = new\n             {\n-                Success = true,\n-                Message = \"User created successfully\",\n-                User = new\n-                {\n-                    result.Data.Id,\n-                    result.Data.TenantId,\n-                    result.Data.Username,\n-                    result.Data.Email,\n-                    result.Data.FirstName,\n-                    result.Data.LastName,\n-                    result.Data.IsActive,\n-                    result.Data.CreatedAt\n-                }\n+                result.Data.Id,\n+                result.Data.TenantId,\n+                result.Data.Username,\n+                result.Data.Email,\n+                result.Data.FirstName,\n+                result.Data.LastName,\n+                result.Data.IsActive,\n+                result.Data.CreatedAt\n             };\n \n+            var response = CreateQueuedResponse(userData, \"User created successfully\");\n             await SendCreatedAtAsync<GetUserEndpoint>(new { userId = result.Data.Id }, response, cancellation: ct);\n         }\n         catch (Exception ex)\n         {\n-            _logger.LogError(ex, \"Error creating user\");\n-            await SendErrorsAsync(500, ct);\n+            Logger.LogError(ex, \"Error creating user\");\n+            await SendAsync(CreateErrorResponse(\"Failed to create user\", ex.Message), 500, ct);\n         }\n     }\n }\n \n"}, {"date": 1751234533181, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -310,36 +310,29 @@\n \n /// <summary>\n /// Endpoint for updating a user\n /// </summary>\n-public class UpdateUserEndpoint : Endpoint<UpdateUserRequest, object>\n+public class UpdateUserEndpoint : QueueFirstEndpointBase<UpdateUserRequest, object>\n {\n     private readonly IUserService _userService;\n     private readonly ITenantContext _tenantContext;\n-    private readonly ILogger<UpdateUserEndpoint> _logger;\n \n     public UpdateUserEndpoint(IUserService userService, ITenantContext tenantContext, ILogger<UpdateUserEndpoint> logger)\n+        : base(logger)\n     {\n         _userService = userService;\n         _tenantContext = tenantContext;\n-        _logger = logger;\n     }\n \n-    public override void Configure()\n+    protected override void ConfigureEndpoint()\n     {\n-        Put(\"/api/users/{userId}\");\n-        Policies(\"RequireTenantAdmin\");\n-        Summary(s =>\n-        {\n-            s.Summary = \"Update User\";\n-            s.Description = \"Update an existing user\";\n-            s.Response(200, \"User updated successfully\");\n-            s.Response(400, \"Invalid request\");\n-            s.Response(401, \"Unauthorized\");\n-            s.Response(403, \"Forbidden\");\n-            s.Response(404, \"User not found\");\n-        });\n-        Tags(\"User Management\");\n+        this.ConfigureManagementEndpoint(\n+            \"PUT\",\n+            \"/api/users/{userId}\",\n+            \"Update User\",\n+            \"Update an existing user's profile information\",\n+            \"RequireTenantAdmin\",\n+            new[] { \"User Management\" });\n     }\n \n     public override async Task HandleAsync(UpdateUserRequest req, CancellationToken ct)\n     {\n"}, {"date": 1751234556580, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -355,38 +355,33 @@\n             if (!result.IsSuccess)\n             {\n                 if (result.Message?.Contains(\"not found\") == true)\n                 {\n-                    await SendNotFoundAsync(ct);\n+                    await SendAsync(CreateErrorResponse(\"User not found\"), 404, ct);\n                     return;\n                 }\n-                await SendAsync(new { Success = false, Message = result.Message }, 400, ct);\n+                await SendAsync(CreateErrorResponse(\"Failed to update user\", result.Message), 400, ct);\n                 return;\n             }\n \n-            var response = new\n+            var userData = new\n             {\n-                Success = true,\n-                Message = \"User updated successfully\",\n-                User = new\n-                {\n-                    result.Data!.Id,\n-                    result.Data.TenantId,\n-                    result.Data.Username,\n-                    result.Data.Email,\n-                    result.Data.FirstName,\n-                    result.Data.LastName,\n-                    result.Data.IsActive,\n-                    result.Data.UpdatedAt\n-                }\n+                result.Data!.Id,\n+                result.Data.TenantId,\n+                result.Data.Username,\n+                result.Data.Email,\n+                result.Data.FirstName,\n+                result.Data.LastName,\n+                result.Data.IsActive,\n+                result.Data.UpdatedAt\n             };\n \n-            await SendOkAsync(response, ct);\n+            await SendOkAsync(CreateQueuedResponse(userData, \"User updated successfully\"), ct);\n         }\n         catch (Exception ex)\n         {\n-            _logger.LogError(ex, \"Error updating user\");\n-            await SendErrorsAsync(500, ct);\n+            Logger.LogError(ex, \"Error updating user\");\n+            await SendAsync(CreateErrorResponse(\"Failed to update user\", ex.Message), 500, ct);\n         }\n     }\n }\n \n"}], "date": 1751234047815, "name": "Commit-0", "content": "using FastEndpoints;\nusing MediatR;\nusing NotifyMaster.Core.Interfaces;\nusing NotifyMaster.Core.Models;\nusing NotifyMaster.Core.Services;\n\nnamespace NotifyMasterApi.Features.Users;\n\n/// <summary>\n/// Request model for creating a user\n/// </summary>\npublic class CreateUserRequest\n{\n    public string TenantId { get; set; } = string.Empty;\n    public string Username { get; set; } = string.Empty;\n    public string Email { get; set; } = string.Empty;\n    public string FirstName { get; set; } = string.Empty;\n    public string LastName { get; set; } = string.Empty;\n    public string Password { get; set; } = string.Empty;\n    public List<string> RoleIds { get; set; } = new();\n    public List<string> PermissionIds { get; set; } = new();\n}\n\n/// <summary>\n/// Request model for updating a user\n/// </summary>\npublic class UpdateUserRequest\n{\n    public string? Username { get; set; }\n    public string? Email { get; set; }\n    public string? FirstName { get; set; }\n    public string? LastName { get; set; }\n    public bool? IsActive { get; set; }\n}\n\n/// <summary>\n/// Request model for getting users with pagination\n/// </summary>\npublic class GetUsersRequest\n{\n    public string? TenantId { get; set; }\n    public int Skip { get; set; } = 0;\n    public int Take { get; set; } = 50;\n}\n\n/// <summary>\n/// Request model for getting a specific user\n/// </summary>\npublic class GetUserRequest\n{\n    public string UserId { get; set; } = string.Empty;\n}\n\n/// <summary>\n/// Request model for changing user password\n/// </summary>\npublic class ChangePasswordRequest\n{\n    public string UserId { get; set; } = string.Empty;\n    public string CurrentPassword { get; set; } = string.Empty;\n    public string NewPassword { get; set; } = string.Empty;\n}\n\n/// <summary>\n/// Request model for assigning role to user\n/// </summary>\npublic class AssignRoleRequest\n{\n    public string UserId { get; set; } = string.Empty;\n    public string RoleId { get; set; } = string.Empty;\n}\n\n/// <summary>\n/// Request model for granting permission to user\n/// </summary>\npublic class GrantPermissionRequest\n{\n    public string UserId { get; set; } = string.Empty;\n    public string PermissionId { get; set; } = string.Empty;\n}\n\n/// <summary>\n/// Endpoint for getting all users\n/// </summary>\npublic class GetUsersEndpoint : Endpoint<GetUsersRequest, object>\n{\n    private readonly IUserService _userService;\n    private readonly ITenantContext _tenantContext;\n    private readonly ILogger<GetUsersEndpoint> _logger;\n\n    public GetUsersEndpoint(IUserService userService, ITenantContext tenantContext, ILogger<GetUsersEndpoint> logger)\n    {\n        _userService = userService;\n        _tenantContext = tenantContext;\n        _logger = logger;\n    }\n\n    public override void Configure()\n    {\n        Get(\"/api/users\");\n        Policies(\"RequireTenantAdmin\");\n        Summary(s =>\n        {\n            s.Summary = \"Get Users\";\n            s.Description = \"Get a paginated list of users in the tenant\";\n            s.Response(200, \"Users retrieved successfully\");\n            s.Response(401, \"Unauthorized\");\n            s.Response(403, \"Forbidden - Tenant admin access required\");\n        });\n        Tags(\"User Management\");\n    }\n\n    public override async Task HandleAsync(GetUsersRequest req, CancellationToken ct)\n    {\n        try\n        {\n            var tenantId = req.TenantId ?? _tenantContext.TenantId;\n            if (string.IsNullOrEmpty(tenantId))\n            {\n                await SendErrorsAsync(400, ct);\n                return;\n            }\n\n            var users = await _userService.GetUsersByTenantAsync(tenantId, req.Skip, req.Take, ct);\n            \n            var response = new\n            {\n                Users = users.Select(u => new\n                {\n                    u.Id,\n                    u.TenantId,\n                    u.Username,\n                    u.Email,\n                    u.FirstName,\n                    u.LastName,\n                    u.IsActive,\n                    u.CreatedAt,\n                    u.UpdatedAt,\n                    Roles = u.Roles?.Select(ur => new { ur.Role.Id, ur.Role.Name }).ToList() ?? new List<object>()\n                }).ToList(),\n                Total = users.Count,\n                Skip = req.Skip,\n                Take = req.Take\n            };\n\n            await SendOkAsync(response, ct);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting users for tenant\");\n            await SendErrorsAsync(500, ct);\n        }\n    }\n}\n\n/// <summary>\n/// Endpoint for getting a specific user\n/// </summary>\npublic class GetUserEndpoint : Endpoint<GetUserRequest, object>\n{\n    private readonly IUserService _userService;\n    private readonly ILogger<GetUserEndpoint> _logger;\n\n    public GetUserEndpoint(IUserService userService, ILogger<GetUserEndpoint> logger)\n    {\n        _userService = userService;\n        _logger = logger;\n    }\n\n    public override void Configure()\n    {\n        Get(\"/api/users/{userId}\");\n        Policies(\"RequireTenantAccess\");\n        Summary(s =>\n        {\n            s.Summary = \"Get User\";\n            s.Description = \"Get details of a specific user\";\n            s.Response(200, \"User retrieved successfully\");\n            s.Response(401, \"Unauthorized\");\n            s.Response(403, \"Forbidden\");\n            s.Response(404, \"User not found\");\n        });\n        Tags(\"User Management\");\n    }\n\n    public override async Task HandleAsync(GetUserRequest req, CancellationToken ct)\n    {\n        try\n        {\n            var user = await _userService.GetUserAsync(req.UserId, ct);\n            if (user == null)\n            {\n                await SendNotFoundAsync(ct);\n                return;\n            }\n\n            var roles = await _userService.GetUserRolesAsync(req.UserId, null, ct);\n            var permissions = await _userService.GetUserPermissionsAsync(req.UserId, null, ct);\n\n            var response = new\n            {\n                User = new\n                {\n                    user.Id,\n                    user.TenantId,\n                    user.Username,\n                    user.Email,\n                    user.FirstName,\n                    user.LastName,\n                    user.IsActive,\n                    user.CreatedAt,\n                    user.UpdatedAt,\n                    user.LastLoginAt\n                },\n                Roles = roles.Select(r => new { r.Id, r.Name, r.Description }).ToList(),\n                Permissions = permissions.Select(p => new { p.Id, p.Resource, p.Action, p.Description }).ToList()\n            };\n\n            await SendOkAsync(response, ct);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting user {UserId}\", req.UserId);\n            await SendErrorsAsync(500, ct);\n        }\n    }\n}\n\n/// <summary>\n/// Endpoint for creating a new user\n/// </summary>\npublic class CreateUserEndpoint : Endpoint<CreateUserRequest, object>\n{\n    private readonly IUserService _userService;\n    private readonly ITenantContext _tenantContext;\n    private readonly ILogger<CreateUserEndpoint> _logger;\n\n    public CreateUserEndpoint(IUserService userService, ITenantContext tenantContext, ILogger<CreateUserEndpoint> logger)\n    {\n        _userService = userService;\n        _tenantContext = tenantContext;\n        _logger = logger;\n    }\n\n    public override void Configure()\n    {\n        Post(\"/api/users\");\n        Policies(\"RequireTenantAdmin\");\n        Summary(s =>\n        {\n            s.Summary = \"Create User\";\n            s.Description = \"Create a new user in the tenant\";\n            s.Response(201, \"User created successfully\");\n            s.Response(400, \"Invalid request\");\n            s.Response(401, \"Unauthorized\");\n            s.Response(403, \"Forbidden - Tenant admin access required\");\n            s.Response(409, \"User with email already exists\");\n        });\n        Tags(\"User Management\");\n    }\n\n    public override async Task HandleAsync(CreateUserRequest req, CancellationToken ct)\n    {\n        try\n        {\n            var tenantId = req.TenantId ?? _tenantContext.TenantId;\n            if (string.IsNullOrEmpty(tenantId))\n            {\n                await SendErrorsAsync(400, ct);\n                return;\n            }\n\n            var createRequest = new NotifyMaster.Core.Models.CreateUserRequest\n            {\n                TenantId = tenantId,\n                Username = req.Username,\n                Email = req.Email,\n                FirstName = req.FirstName,\n                LastName = req.LastName,\n                Password = req.Password,\n                CreatedBy = _tenantContext.UserId\n            };\n\n            var result = await _userService.CreateUserAsync(createRequest, ct);\n            \n            if (!result.IsSuccess)\n            {\n                await SendAsync(new { Success = false, Message = result.Message }, 400, ct);\n                return;\n            }\n\n            // Assign roles if provided\n            foreach (var roleId in req.RoleIds)\n            {\n                await _userService.AssignRoleAsync(result.Data!.Id, roleId, tenantId, _tenantContext.UserId, ct);\n            }\n\n            // Grant permissions if provided\n            foreach (var permissionId in req.PermissionIds)\n            {\n                await _userService.GrantPermissionAsync(result.Data!.Id, permissionId, tenantId, _tenantContext.UserId, ct);\n            }\n\n            var response = new\n            {\n                Success = true,\n                Message = \"User created successfully\",\n                User = new\n                {\n                    result.Data.Id,\n                    result.Data.TenantId,\n                    result.Data.Username,\n                    result.Data.Email,\n                    result.Data.FirstName,\n                    result.Data.LastName,\n                    result.Data.IsActive,\n                    result.Data.CreatedAt\n                }\n            };\n\n            await SendCreatedAtAsync<GetUserEndpoint>(new { userId = result.Data.Id }, response, cancellation: ct);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error creating user\");\n            await SendErrorsAsync(500, ct);\n        }\n    }\n}\n\n/// <summary>\n/// Endpoint for updating a user\n/// </summary>\npublic class UpdateUserEndpoint : Endpoint<UpdateUserRequest, object>\n{\n    private readonly IUserService _userService;\n    private readonly ITenantContext _tenantContext;\n    private readonly ILogger<UpdateUserEndpoint> _logger;\n\n    public UpdateUserEndpoint(IUserService userService, ITenantContext tenantContext, ILogger<UpdateUserEndpoint> logger)\n    {\n        _userService = userService;\n        _tenantContext = tenantContext;\n        _logger = logger;\n    }\n\n    public override void Configure()\n    {\n        Put(\"/api/users/{userId}\");\n        Policies(\"RequireTenantAdmin\");\n        Summary(s =>\n        {\n            s.Summary = \"Update User\";\n            s.Description = \"Update an existing user\";\n            s.Response(200, \"User updated successfully\");\n            s.Response(400, \"Invalid request\");\n            s.Response(401, \"Unauthorized\");\n            s.Response(403, \"Forbidden\");\n            s.Response(404, \"User not found\");\n        });\n        Tags(\"User Management\");\n    }\n\n    public override async Task HandleAsync(UpdateUserRequest req, CancellationToken ct)\n    {\n        try\n        {\n            var userId = Route<string>(\"userId\")!;\n\n            var updateRequest = new NotifyMaster.Core.Models.UpdateUserRequest\n            {\n                Username = req.Username,\n                Email = req.Email,\n                FirstName = req.FirstName,\n                LastName = req.LastName,\n                IsActive = req.IsActive,\n                UpdatedBy = _tenantContext.UserId\n            };\n\n            var result = await _userService.UpdateUserAsync(userId, updateRequest, ct);\n\n            if (!result.IsSuccess)\n            {\n                if (result.Message?.Contains(\"not found\") == true)\n                {\n                    await SendNotFoundAsync(ct);\n                    return;\n                }\n                await SendAsync(new { Success = false, Message = result.Message }, 400, ct);\n                return;\n            }\n\n            var response = new\n            {\n                Success = true,\n                Message = \"User updated successfully\",\n                User = new\n                {\n                    result.Data!.Id,\n                    result.Data.TenantId,\n                    result.Data.Username,\n                    result.Data.Email,\n                    result.Data.FirstName,\n                    result.Data.LastName,\n                    result.Data.IsActive,\n                    result.Data.UpdatedAt\n                }\n            };\n\n            await SendOkAsync(response, ct);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error updating user\");\n            await SendErrorsAsync(500, ct);\n        }\n    }\n}\n"}]}