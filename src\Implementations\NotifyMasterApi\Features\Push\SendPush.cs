using NotifyMasterApi.Documentation;

namespace NotifyMasterApi.Features.Push;

/// <summary>
/// Request model for sending a push notification
/// </summary>
public class SendPushRequest
{
    /// <summary>
    /// Device token for the target device (required)
    /// </summary>
    /// <example>device_token_here_1234567890abcdef</example>
    [Required(ErrorMessage = "Device token is required")]
    [StringLength(500, MinimumLength = 10, ErrorMessage = "Device token must be between 10 and 500 characters")]
    public string DeviceToken { get; set; } = string.Empty;

    /// <summary>
    /// Notification title (required)
    /// </summary>
    /// <example>New Message</example>
    [Required(ErrorMessage = "Notification title is required")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Title must be between 1 and 100 characters")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Notification message body (required)
    /// </summary>
    /// <example>You have a new notification</example>
    [Required(ErrorMessage = "Notification message is required")]
    [StringLength(500, MinimumLength = 1, ErrorMessage = "Message must be between 1 and 500 characters")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// URL to an image to display in the notification (optional)
    /// </summary>
    /// <example>https://example.com/image.png</example>
    [Url(ErrorMessage = "Invalid image URL format")]
    public string? ImageUrl { get; set; }

    /// <summary>
    /// Custom data payload for the notification (optional)
    /// </summary>
    /// <example>{"action": "open_chat", "chatId": "chat_123"}</example>
    public Dictionary<string, string>? Data { get; set; }

    /// <summary>
    /// Target platform (auto-detected if not specified)
    /// </summary>
    /// <example>ios, android, web</example>
    public string? Platform { get; set; }

    /// <summary>
    /// Additional metadata for tracking and analytics
    /// </summary>
    /// <example>{"campaign": "promotion", "userId": "12345"}</example>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// Notification priority level
    /// </summary>
    /// <example>high, normal, low</example>
    public string? Priority { get; set; }

    /// <summary>
    /// Sound to play with the notification
    /// </summary>
    /// <example>default, custom_sound.wav</example>
    public string? Sound { get; set; }

    /// <summary>
    /// Badge count for iOS notifications
    /// </summary>
    /// <example>1</example>
    [Range(0, 999, ErrorMessage = "Badge count must be between 0 and 999")]
    public int? Badge { get; set; }

    /// <summary>
    /// Time to live for the notification (in seconds)
    /// </summary>
    /// <example>3600</example>
    [Range(1, 2419200, ErrorMessage = "TTL must be between 1 second and 28 days")]
    public int? TimeToLive { get; set; }
}

/// <summary>
/// Response model for push notification sending operation
/// </summary>
public class SendPushResponse
{
    /// <summary>
    /// Indicates whether the push notification was sent successfully
    /// </summary>
    /// <example>true</example>
    public bool Success { get; set; }

    /// <summary>
    /// Unique identifier for the sent notification (available when successful)
    /// </summary>
    /// <example>push_def456ghi789</example>
    public string? MessageId { get; set; }

    /// <summary>
    /// Error message if the operation failed
    /// </summary>
    /// <example>Invalid device token</example>
    public string? Error { get; set; }

    /// <summary>
    /// Timestamp when the operation was completed
    /// </summary>
    /// <example>2024-01-15T10:30:00Z</example>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Push notification provider used for sending
    /// </summary>
    /// <example>FCM, APNS, OneSignal</example>
    public string? Provider { get; set; }

    /// <summary>
    /// Platform that received the notification
    /// </summary>
    /// <example>ios, android, web</example>
    public string? Platform { get; set; }

    /// <summary>
    /// Delivery status information
    /// </summary>
    /// <example>queued, sent, delivered, failed</example>
    public string? DeliveryStatus { get; set; }

    /// <summary>
    /// Additional response data from the provider
    /// </summary>
    public Dictionary<string, object>? ProviderResponse { get; set; }
}

public class SendPushEndpoint : Endpoint<SendPushRequest, SendPushResponse>
{
    private readonly IPushGateway _pushGateway;
    private readonly ILogger<SendPushEndpoint> _logger;

    public SendPushEndpoint(IPushGateway pushGateway, ILogger<SendPushEndpoint> logger)
    {
        _pushGateway = pushGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        this.ConfigureNotificationEndpoint(
            "POST",
            "/api/push/send",
            "Send Push Notification",
            "Send a push notification to mobile devices and web browsers through configured push notification providers.\n\n" +
            "## 🎯 Features\n" +
            "- **Multi-Platform Support**: iOS, Android, and Web push notifications\n" +
            "- **Rich Notifications**: Images, custom sounds, and interactive elements\n" +
            "- **Smart Delivery**: Automatic platform detection and optimization\n" +
            "- **Delivery Tracking**: Real-time delivery status and analytics\n" +
            "- **Batch Processing**: Send to multiple devices efficiently\n\n" +
            "## 📋 Provider Support\n" +
            "- **Firebase Cloud Messaging (FCM)**: Android and Web\n" +
            "- **Apple Push Notification Service (APNS)**: iOS\n" +
            "- **OneSignal**: Cross-platform solution\n" +
            "- **Amazon SNS**: AWS-based push notifications\n" +
            "- **Custom Push Plugins**: Extensible provider system\n\n" +
            "## 📱 Platform Features\n" +
            "### iOS (APNS)\n- Rich media notifications with images\n- Custom sounds and badge counts\n- Silent notifications for background updates\n- Notification categories and actions\n\n" +
            "### Android (FCM)\n- High priority messaging\n- Notification channels and groups\n- Custom icons and colors\n- Direct reply and action buttons\n\n" +
            "### Web Push\n- Browser notifications for PWAs\n- Service worker integration\n- Offline message queuing\n- Click tracking and analytics\n\n" +
            "## ⚡ Rate Limits\n" +
            "- **Default**: 1000 notifications/minute per API key\n" +
            "- **Burst**: Up to 5000 notifications in 10 seconds\n" +
            "- **Daily**: 100,000 notifications per day (configurable)\n\n" +
            "## 🔒 Security & Privacy\n" +
            "- Device token validation and encryption\n- Message content filtering\n- User consent tracking\n- GDPR compliance features",
            "Push",
            new[] { "Core Messaging", "Mobile Notifications", "Real-time" }
        );
    }

    public override async Task HandleAsync(SendPushRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Sending push notification to device {DeviceToken}", req.DeviceToken);

            var pushRequest = new PushMessageRequest
            {
                DeviceToken = req.DeviceToken,
                Title = req.Title,
                Message = req.Message,
                ImageUrl = req.ImageUrl,
                Data = req.Data,
                Platform = req.Platform,
                Metadata = req.Metadata
            };

            var result = await _pushGateway.SendAsync(pushRequest);

            if (result.IsSuccess)
            {
                await SendOkAsync(new SendPushResponse
                {
                    Success = true,
                    MessageId = result.MessageId
                }, ct);
            }
            else
            {
                await SendAsync(new SendPushResponse
                {
                    Success = false,
                    Error = result.ErrorMessage
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending push notification");
            await SendAsync(new SendPushResponse
            {
                Success = false,
                Error = "Internal server error"
            }, 500, ct);
        }
    }
}

public class SendBulkPushRequest
{
    public List<SendPushRequest> Messages { get; set; } = new();
}

public class SendBulkPushResponse
{
    public bool Success { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<SendPushResponse> Results { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class SendBulkPushEndpoint : Endpoint<SendBulkPushRequest, SendBulkPushResponse>
{
    private readonly IPushGateway _pushGateway;
    private readonly ILogger<SendBulkPushEndpoint> _logger;

    public SendBulkPushEndpoint(IPushGateway pushGateway, ILogger<SendBulkPushEndpoint> logger)
    {
        _pushGateway = pushGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/push/send/bulk");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Send bulk push notifications";
            s.Description = "Send multiple push notifications through available push plugins";
            s.Responses[200] = "Bulk push notifications processed";
            s.Responses[400] = "Invalid request";
            s.Responses[500] = "Internal server error";
        });
        Tags("Push");
    }

    public override async Task HandleAsync(SendBulkPushRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Sending bulk push notifications to {Count} devices", req.Messages.Count);

            var bulkRequest = new BulkPushRequest
            {
                Messages = req.Messages.Select(m => new PushMessageRequest
                {
                    DeviceToken = m.DeviceToken,
                    Title = m.Title,
                    Message = m.Message,
                    ImageUrl = m.ImageUrl,
                    Data = m.Data,
                    Platform = m.Platform,
                    Metadata = m.Metadata
                }).ToList()
            };

            var result = await _pushGateway.SendBulkPushAsync(bulkRequest);

            var response = new SendBulkPushResponse
            {
                Success = result.IsSuccess,
                SuccessCount = result.SuccessCount,
                FailureCount = result.FailureCount,
                Results = result.Results?.Select(r => new SendPushResponse
                {
                    Success = r.IsSuccess,
                    MessageId = r.MessageId,
                    Error = r.ErrorMessage
                }).ToList() ?? new List<SendPushResponse>()
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending bulk push notifications");
            await SendAsync(new SendBulkPushResponse
            {
                Success = false,
                FailureCount = req.Messages.Count
            }, 500, ct);
        }
    }
}

public class GetPushStatusRequest
{
    public string MessageId { get; set; } = string.Empty;
}

public class GetPushStatusEndpoint : Endpoint<GetPushStatusRequest, object>
{
    private readonly IPushGateway _pushGateway;
    private readonly ILogger<GetPushStatusEndpoint> _logger;

    public GetPushStatusEndpoint(IPushGateway pushGateway, ILogger<GetPushStatusEndpoint> logger)
    {
        _pushGateway = pushGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/push/status");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get push notification status";
            s.Description = "Get the status of a push notification";
            s.Responses[200] = "Push status retrieved successfully";
            s.Responses[404] = "Message not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("Push");
    }

    public override async Task HandleAsync(GetPushStatusRequest req, CancellationToken ct)
    {
        try
        {
            var status = await _pushGateway.GetMessageStatusAsync(req.MessageId);
            await SendOkAsync(status, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push status for {MessageId}", req.MessageId);
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetPushPlatformsEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly IPushGateway _pushGateway;
    private readonly ILogger<GetPushPlatformsEndpoint> _logger;

    public GetPushPlatformsEndpoint(IPushGateway pushGateway, ILogger<GetPushPlatformsEndpoint> logger)
    {
        _pushGateway = pushGateway;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/push/platforms");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get push platforms";
            s.Description = "Get list of available push notification platforms";
            s.Responses[200] = "Platforms retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("Push");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var platforms = await _pushGateway.GetPlatformsAsync();
            await SendOkAsync(platforms, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push platforms");
            await SendErrorsAsync(500, ct);
        }
    }
}
