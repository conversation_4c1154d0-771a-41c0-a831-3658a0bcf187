{"sourceFile": "src/Core/NotifyMaster.Core/Models/QueueModels.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751241124200, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751241124200, "name": "Commit-0", "content": "using NotificationContract.Enums;\n\nnamespace NotifyMaster.Core.Models;\n\n/// <summary>\n/// Represents a notification item in the queue\n/// </summary>\npublic class NotificationQueueItem\n{\n    /// <summary>\n    /// Unique queue identifier\n    /// </summary>\n    public string QueueId { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// Original message identifier\n    /// </summary>\n    public string MessageId { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// Type of notification (Email, SMS, Push, etc.)\n    /// </summary>\n    public NotificationType Type { get; set; }\n    \n    /// <summary>\n    /// Recipient of the notification\n    /// </summary>\n    public string Recipient { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// Subject line for the notification\n    /// </summary>\n    public string Subject { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// Content/body of the notification\n    /// </summary>\n    public string Content { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// User ID associated with the notification\n    /// </summary>\n    public string? UserId { get; set; }\n    \n    /// <summary>\n    /// Correlation ID for tracking related operations\n    /// </summary>\n    public string? CorrelationId { get; set; }\n    \n    /// <summary>\n    /// Additional metadata for the notification\n    /// </summary>\n    public Dictionary<string, object>? Metadata { get; set; }\n    \n    /// <summary>\n    /// When the notification was queued\n    /// </summary>\n    public DateTime QueuedAt { get; set; }\n    \n    /// <summary>\n    /// When the notification was processed (if completed)\n    /// </summary>\n    public DateTime? ProcessedAt { get; set; }\n    \n    /// <summary>\n    /// Number of retry attempts\n    /// </summary>\n    public int RetryCount { get; set; }\n    \n    /// <summary>\n    /// Maximum number of retries allowed\n    /// </summary>\n    public int MaxRetries { get; set; } = 3;\n    \n    /// <summary>\n    /// Error message if processing failed\n    /// </summary>\n    public string? ErrorMessage { get; set; }\n    \n    /// <summary>\n    /// Priority level of the notification\n    /// </summary>\n    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;\n    \n    /// <summary>\n    /// Scheduled delivery time (if applicable)\n    /// </summary>\n    public DateTime? ScheduledFor { get; set; }\n}\n\n/// <summary>\n/// Priority levels for notifications\n/// </summary>\npublic enum NotificationPriority\n{\n    /// <summary>\n    /// Low priority - can be delayed\n    /// </summary>\n    Low = 0,\n    \n    /// <summary>\n    /// Normal priority - standard processing\n    /// </summary>\n    Normal = 1,\n    \n    /// <summary>\n    /// High priority - expedited processing\n    /// </summary>\n    High = 2,\n    \n    /// <summary>\n    /// Critical priority - immediate processing\n    /// </summary>\n    Critical = 3\n}\n\n/// <summary>\n/// Represents a message that failed processing and was moved to dead letter queue\n/// </summary>\npublic class DeadLetterMessage\n{\n    /// <summary>\n    /// Unique identifier for the dead letter message\n    /// </summary>\n    public string Id { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// Original message ID that failed\n    /// </summary>\n    public string OriginalMessageId { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// Tenant ID associated with the message\n    /// </summary>\n    public string TenantId { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// Channel/type of the failed message\n    /// </summary>\n    public string Channel { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// Original message content\n    /// </summary>\n    public string OriginalMessage { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// Error message describing the failure\n    /// </summary>\n    public string ErrorMessage { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// Stack trace of the error (if available)\n    /// </summary>\n    public string? StackTrace { get; set; }\n    \n    /// <summary>\n    /// Number of retry attempts made\n    /// </summary>\n    public int RetryCount { get; set; }\n    \n    /// <summary>\n    /// When the message first failed\n    /// </summary>\n    public DateTime FirstFailedAt { get; set; }\n    \n    /// <summary>\n    /// When the message last failed\n    /// </summary>\n    public DateTime LastFailedAt { get; set; }\n    \n    /// <summary>\n    /// Current status of the dead letter message\n    /// </summary>\n    public string Status { get; set; } = string.Empty;\n    \n    /// <summary>\n    /// Additional metadata about the failure\n    /// </summary>\n    public Dictionary<string, object>? Metadata { get; set; }\n}\n\n/// <summary>\n/// Represents an outbound webhook job\n/// </summary>\npublic class OutboundWebhookJob\n{\n    /// <summary>\n    /// Unique identifier for the webhook job\n    /// </summary>\n    public string Id { get; set; } = string.Empty;\n\n    /// <summary>\n    /// Tenant ID associated with the webhook\n    /// </summary>\n    public string TenantId { get; set; } = string.Empty;\n\n    /// <summary>\n    /// Target URL for the webhook\n    /// </summary>\n    public string Url { get; set; } = string.Empty;\n\n    /// <summary>\n    /// HTTP method to use (GET, POST, PUT, etc.)\n    /// </summary>\n    public string HttpMethod { get; set; } = \"POST\";\n\n    /// <summary>\n    /// Headers to include in the webhook request\n    /// </summary>\n    public Dictionary<string, string>? Headers { get; set; }\n\n    /// <summary>\n    /// Payload/body of the webhook\n    /// </summary>\n    public string Payload { get; set; } = string.Empty;\n\n    /// <summary>\n    /// Content type of the payload\n    /// </summary>\n    public string ContentType { get; set; } = \"application/json\";\n\n    /// <summary>\n    /// Current status of the webhook job\n    /// </summary>\n    public string Status { get; set; } = string.Empty;\n\n    /// <summary>\n    /// When the webhook job was created\n    /// </summary>\n    public DateTime CreatedAt { get; set; }\n\n    /// <summary>\n    /// When the webhook was last attempted\n    /// </summary>\n    public DateTime? LastAttemptAt { get; set; }\n\n    /// <summary>\n    /// When the webhook was completed successfully\n    /// </summary>\n    public DateTime? CompletedAt { get; set; }\n\n    /// <summary>\n    /// Number of retry attempts made\n    /// </summary>\n    public int RetryCount { get; set; }\n\n    /// <summary>\n    /// Maximum number of retries allowed\n    /// </summary>\n    public int MaxRetries { get; set; } = 3;\n\n    /// <summary>\n    /// Error message if webhook failed\n    /// </summary>\n    public string? ErrorMessage { get; set; }\n\n    /// <summary>\n    /// HTTP response code from the last attempt\n    /// </summary>\n    public int? ResponseCode { get; set; }\n\n    /// <summary>\n    /// Response body from the last attempt\n    /// </summary>\n    public string? ResponseBody { get; set; }\n\n    /// <summary>\n    /// Timeout for the webhook request in seconds\n    /// </summary>\n    public int TimeoutSeconds { get; set; } = 30;\n}\n\n/// <summary>\n/// Represents the result of a webhook retry attempt\n/// </summary>\npublic class WebhookRetryResult\n{\n    /// <summary>\n    /// When the retry attempt was made\n    /// </summary>\n    public DateTime AttemptedAt { get; set; }\n\n    /// <summary>\n    /// HTTP response code received\n    /// </summary>\n    public int? ResponseCode { get; set; }\n\n    /// <summary>\n    /// Response body received\n    /// </summary>\n    public string? ResponseBody { get; set; }\n\n    /// <summary>\n    /// Error message if the attempt failed\n    /// </summary>\n    public string? ErrorMessage { get; set; }\n\n    /// <summary>\n    /// Whether the attempt was successful\n    /// </summary>\n    public bool IsSuccess { get; set; }\n\n    /// <summary>\n    /// Duration of the request in milliseconds\n    /// </summary>\n    public long DurationMs { get; set; }\n}\n"}]}