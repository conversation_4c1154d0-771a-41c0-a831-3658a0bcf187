using NotifyMasterApi.Data.Entities;

namespace NotifyMasterApi.Data;

public class NotificationDbContext : DbContext
{
    public NotificationDbContext(DbContextOptions<NotificationDbContext> options) : base(options)
    {
    }

    public DbSet<NotificationLog> NotificationLogs { get; set; }
    public DbSet<NotificationMetrics> NotificationMetrics { get; set; }
    public DbSet<NotificationError> NotificationErrors { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure NotificationLog
        modelBuilder.Entity<NotificationLog>(entity =>
        {
            entity.ToTable("notification_logs");
            
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Id)
                .HasColumnName("id")
                .ValueGeneratedOnAdd();
                
            entity.Property(e => e.MessageId)
                .HasColumnName("message_id")
                .HasMaxLength(100)
                .IsRequired();
                
            entity.Property(e => e.Type)
                .HasColumnName("type")
                .HasConversion<int>()
                .IsRequired();
                
            entity.Property(e => e.Recipient)
                .HasColumnName("recipient")
                .HasMaxLength(500)
                .IsRequired();
                
            entity.Property(e => e.Subject)
                .HasColumnName("subject")
                .HasMaxLength(200);
                
            entity.Property(e => e.Content)
                .HasColumnName("content")
                .IsRequired();
                
            entity.Property(e => e.Status)
                .HasColumnName("status")
                .HasConversion<int>()
                .IsRequired();
                
            entity.Property(e => e.Provider)
                .HasColumnName("provider")
                .HasMaxLength(100);
                
            entity.Property(e => e.ErrorMessage)
                .HasColumnName("error_message")
                .HasMaxLength(1000);
                
            entity.Property(e => e.ResponseData)
                .HasColumnName("response_data")
                .HasColumnType("jsonb");
                
            entity.Property(e => e.CreatedAt)
                .HasColumnName("created_at")
                .IsRequired();
                
            entity.Property(e => e.SentAt)
                .HasColumnName("sent_at");
                
            entity.Property(e => e.DeliveredAt)
                .HasColumnName("delivered_at");
                
            entity.Property(e => e.FailedAt)
                .HasColumnName("failed_at");
                
            entity.Property(e => e.RetryCount)
                .HasColumnName("retry_count")
                .HasDefaultValue(0);
                
            entity.Property(e => e.LastRetryAt)
                .HasColumnName("last_retry_at");
                
            entity.Property(e => e.CorrelationId)
                .HasColumnName("correlation_id")
                .HasMaxLength(50);
                
            entity.Property(e => e.UserId)
                .HasColumnName("user_id")
                .HasMaxLength(100);
                
            entity.Property(e => e.Metadata)
                .HasColumnName("metadata")
                .HasColumnType("jsonb")
                .HasConversion(
                    v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                    v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null));

            // Indexes
            entity.HasIndex(e => e.MessageId).HasDatabaseName("ix_notification_logs_message_id");
            entity.HasIndex(e => e.Type).HasDatabaseName("ix_notification_logs_type");
            entity.HasIndex(e => e.Status).HasDatabaseName("ix_notification_logs_status");
            entity.HasIndex(e => e.CreatedAt).HasDatabaseName("ix_notification_logs_created_at");
            entity.HasIndex(e => e.Recipient).HasDatabaseName("ix_notification_logs_recipient");
            entity.HasIndex(e => e.CorrelationId).HasDatabaseName("ix_notification_logs_correlation_id");
        });

        // Configure NotificationMetrics
        modelBuilder.Entity<NotificationMetrics>(entity =>
        {
            entity.ToTable("notification_metrics");
            
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Id)
                .HasColumnName("id")
                .ValueGeneratedOnAdd();
                
            entity.Property(e => e.Type)
                .HasColumnName("type")
                .HasConversion<int>()
                .IsRequired();
                
            entity.Property(e => e.Provider)
                .HasColumnName("provider")
                .HasMaxLength(100)
                .IsRequired();
                
            entity.Property(e => e.Date)
                .HasColumnName("date")
                .IsRequired();
                
            entity.Property(e => e.TotalSent)
                .HasColumnName("total_sent");
                
            entity.Property(e => e.TotalDelivered)
                .HasColumnName("total_delivered");
                
            entity.Property(e => e.TotalFailed)
                .HasColumnName("total_failed");
                
            entity.Property(e => e.TotalRetries)
                .HasColumnName("total_retries");
                
            entity.Property(e => e.AverageResponseTime)
                .HasColumnName("average_response_time");
                
            entity.Property(e => e.SuccessRate)
                .HasColumnName("success_rate");
                
            entity.Property(e => e.FailureRate)
                .HasColumnName("failure_rate");
                
            entity.Property(e => e.CreatedAt)
                .HasColumnName("created_at")
                .IsRequired();
                
            entity.Property(e => e.UpdatedAt)
                .HasColumnName("updated_at");

            // Indexes
            entity.HasIndex(e => new { e.Type, e.Provider, e.Date }).HasDatabaseName("ix_notification_metrics_type_provider_date");
            entity.HasIndex(e => e.Date).HasDatabaseName("ix_notification_metrics_date");
        });

        // Configure NotificationError
        modelBuilder.Entity<NotificationError>(entity =>
        {
            entity.ToTable("notification_errors");
            
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Id)
                .HasColumnName("id")
                .ValueGeneratedOnAdd();
                
            entity.Property(e => e.Type)
                .HasColumnName("type")
                .HasConversion<int>()
                .IsRequired();
                
            entity.Property(e => e.Provider)
                .HasColumnName("provider")
                .HasMaxLength(100)
                .IsRequired();
                
            entity.Property(e => e.ErrorCode)
                .HasColumnName("error_code")
                .HasMaxLength(100)
                .IsRequired();
                
            entity.Property(e => e.ErrorMessage)
                .HasColumnName("error_message")
                .HasMaxLength(1000)
                .IsRequired();
                
            entity.Property(e => e.Recipient)
                .HasColumnName("recipient")
                .HasMaxLength(500);
                
            entity.Property(e => e.MessageId)
                .HasColumnName("message_id")
                .HasMaxLength(100);
                
            entity.Property(e => e.StackTrace)
                .HasColumnName("stack_trace");
                
            entity.Property(e => e.RequestData)
                .HasColumnName("request_data")
                .HasColumnType("jsonb");
                
            entity.Property(e => e.OccurredAt)
                .HasColumnName("occurred_at")
                .IsRequired();
                
            entity.Property(e => e.Severity)
                .HasColumnName("severity");
                
            entity.Property(e => e.IsResolved)
                .HasColumnName("is_resolved")
                .HasDefaultValue(false);
                
            entity.Property(e => e.ResolvedAt)
                .HasColumnName("resolved_at");
                
            entity.Property(e => e.Resolution)
                .HasColumnName("resolution")
                .HasMaxLength(1000);

            // Indexes
            entity.HasIndex(e => e.Type).HasDatabaseName("ix_notification_errors_type");
            entity.HasIndex(e => e.Provider).HasDatabaseName("ix_notification_errors_provider");
            entity.HasIndex(e => e.OccurredAt).HasDatabaseName("ix_notification_errors_occurred_at");
            entity.HasIndex(e => e.IsResolved).HasDatabaseName("ix_notification_errors_is_resolved");
        });
    }
}
