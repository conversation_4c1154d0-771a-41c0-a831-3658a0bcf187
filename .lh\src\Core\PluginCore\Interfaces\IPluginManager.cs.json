{"sourceFile": "src/Core/PluginCore/Interfaces/IPluginManager.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1751212076069, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751212309109, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,8 +23,18 @@\n     /// </summary>\n     Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default);\n \n     /// <summary>\n+    /// Loads a specific plugin by file path.\n+    /// </summary>\n+    Task<OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default);\n+\n+    /// <summary>\n+    /// Loads a plugin by name from the plugin directory.\n+    /// </summary>\n+    Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default);\n+\n+    /// <summary>\n     /// Gets all loaded plugins of a specific type.\n     /// </summary>\n     IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType;\n \n"}, {"date": 1751213646777, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,18 +23,8 @@\n     /// </summary>\n     Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default);\n \n     /// <summary>\n-    /// Loads a specific plugin by file path.\n-    /// </summary>\n-    Task<OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default);\n-\n-    /// <summary>\n-    /// Loads a plugin by name from the plugin directory.\n-    /// </summary>\n-    Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default);\n-\n-    /// <summary>\n     /// Gets all loaded plugins of a specific type.\n     /// </summary>\n     IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType;\n \n"}, {"date": 1751228252463, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -61,8 +61,10 @@\n     /// <summary>\n     /// Validates a plugin before loading.\n     /// </summary>\n     Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default);\n+    Task<PluginMetrics?> GetPluginMetricsAsync(string pluginName, CancellationToken cancellationToken);\n+\n }\n \n /// <summary>\n /// Represents the status of a plugin.\n"}, {"date": 1751228986993, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,7 @@\n using PluginCore.Base;\n using PluginCore.Models;\n+using ValidationResult = PluginCore.Models.ValidationResult;\n \n namespace PluginCore.Interfaces;\n \n /// <summary>\n"}], "date": 1751212076069, "name": "Commit-0", "content": "using PluginCore.Base;\nusing PluginCore.Models;\n\nnamespace PluginCore.Interfaces;\n\n/// <summary>\n/// Interface for managing plugins in the notification service.\n/// </summary>\npublic interface IPluginManager\n{\n    /// <summary>\n    /// Loads all plugins from the specified directory.\n    /// </summary>\n    Task<OperationResult> LoadPluginsAsync(string pluginDirectory, CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Loads a specific plugin by file path.\n    /// </summary>\n    Task<OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Loads a plugin by name from the plugin directory.\n    /// </summary>\n    Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Gets all loaded plugins of a specific type.\n    /// </summary>\n    IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType;\n\n    /// <summary>\n    /// Gets a specific plugin by name and type.\n    /// </summary>\n    T? GetPlugin<T>(string pluginName) where T : class, IPluginType;\n\n    /// <summary>\n    /// Gets all loaded plugin manifests.\n    /// </summary>\n    IReadOnlyList<PluginManifest> GetPluginManifests();\n\n    /// <summary>\n    /// Gets the manifest for a specific plugin.\n    /// </summary>\n    PluginManifest? GetPluginManifest(string pluginName);\n\n    /// <summary>\n    /// Unloads a specific plugin.\n    /// </summary>\n    Task<OperationResult> UnloadPluginAsync(string pluginName, CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Reloads a specific plugin.\n    /// </summary>\n    Task<OperationResult> ReloadPluginAsync(string pluginName, CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Gets the status of all loaded plugins.\n    /// </summary>\n    Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Validates a plugin before loading.\n    /// </summary>\n    Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default);\n}\n\n/// <summary>\n/// Represents the status of a plugin.\n/// </summary>\npublic record PluginStatus(\n    string Name,\n    string Version,\n    string Type,\n    bool IsLoaded,\n    bool IsHealthy,\n    string Status,\n    DateTimeOffset LastChecked,\n    string? ErrorMessage = null);\n"}]}