using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;
using BCrypt.Net;

namespace NotifyMasterApi.Services.Setup;

/// <summary>
/// Service for managing configuration file operations
/// </summary>
public interface IConfigurationService
{
    Task<bool> SaveConfigurationAsync(WizardConfiguration config, string? filePath = null);
    Task<WizardConfiguration?> LoadConfigurationAsync(string? filePath = null);
    Task<bool> CreateInitializedMarkerAsync();
    Task<bool> IsSystemInitializedAsync();
    Task<bool> BackupExistingConfigAsync(string? filePath = null);
    Task<ValidationResult> ValidateConfigurationAsync(WizardConfiguration config);
    string GenerateSecureKey(int length = 32);
    string HashPassword(string password);
}

/// <summary>
/// Configuration service implementation
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly ILogger<ConfigurationService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;
    private const string ConfigFileName = "appsettings.Production.json";
    private const string InitializedMarkerFile = ".system-initialized";

    public ConfigurationService(ILogger<ConfigurationService> logger)
    {
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
    }

    public async Task<bool> SaveConfigurationAsync(WizardConfiguration config, string? filePath = null)
    {
        try
        {
            filePath ??= ConfigFileName;
            
            // Create backup if file exists
            if (File.Exists(filePath))
            {
                await BackupExistingConfigAsync(filePath);
            }

            // Generate secure keys if not provided
            if (string.IsNullOrEmpty(config.Preferences.CustomSettings.GetValueOrDefault("JwtSecret")?.ToString()))
            {
                config.Preferences.CustomSettings["JwtSecret"] = GenerateSecureKey(64);
            }

            // Hash admin password if not already hashed
            if (!string.IsNullOrEmpty(config.AdminUser.Password) && !config.AdminUser.Password.StartsWith("$2"))
            {
                config.AdminUser.Password = HashPassword(config.AdminUser.Password);
            }

            // Convert to appsettings format
            var appSettings = ConvertToAppSettings(config);
            var json = JsonSerializer.Serialize(appSettings, _jsonOptions);
            
            await File.WriteAllTextAsync(filePath, json);
            
            _logger.LogInformation("✅ Configuration saved to {FilePath}", filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to save configuration to {FilePath}", filePath);
            return false;
        }
    }

    public async Task<WizardConfiguration?> LoadConfigurationAsync(string? filePath = null)
    {
        try
        {
            filePath ??= ConfigFileName;
            
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("Configuration file not found: {FilePath}", filePath);
                return null;
            }

            var json = await File.ReadAllTextAsync(filePath);
            var appSettings = JsonSerializer.Deserialize<Dictionary<string, object>>(json, _jsonOptions);
            
            if (appSettings == null)
            {
                _logger.LogWarning("Failed to deserialize configuration from {FilePath}", filePath);
                return null;
            }

            // Convert from appsettings format
            var config = ConvertFromAppSettings(appSettings);
            
            _logger.LogInformation("✅ Configuration loaded from {FilePath}", filePath);
            return config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to load configuration from {FilePath}", filePath);
            return null;
        }
    }

    public async Task<bool> CreateInitializedMarkerAsync()
    {
        try
        {
            var markerData = new
            {
                InitializedAt = DateTime.UtcNow,
                Version = "2.0.0",
                InitializedBy = "Configuration Wizard"
            };

            var json = JsonSerializer.Serialize(markerData, _jsonOptions);
            await File.WriteAllTextAsync(InitializedMarkerFile, json);
            
            _logger.LogInformation("✅ System initialization marker created");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to create initialization marker");
            return false;
        }
    }

    public async Task<bool> IsSystemInitializedAsync()
    {
        try
        {
            return File.Exists(InitializedMarkerFile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to check system initialization status");
            return false;
        }
    }

    public async Task<bool> BackupExistingConfigAsync(string? filePath = null)
    {
        try
        {
            filePath ??= ConfigFileName;
            
            if (!File.Exists(filePath))
            {
                return true; // Nothing to backup
            }

            var backupPath = $"{filePath}.backup.{DateTime.UtcNow:yyyyMMdd-HHmmss}";
            File.Copy(filePath, backupPath);
            
            _logger.LogInformation("✅ Configuration backed up to {BackupPath}", backupPath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to backup configuration");
            return false;
        }
    }

    public async Task<ValidationResult> ValidateConfigurationAsync(WizardConfiguration config)
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // Validate database configuration
            if (string.IsNullOrEmpty(config.Database.ConnectionString))
            {
                result.Errors.Add("Database connection string is required");
                result.IsValid = false;
            }

            // Validate tenant configuration
            if (string.IsNullOrEmpty(config.Tenant.Name))
            {
                result.Errors.Add("Tenant name is required");
                result.IsValid = false;
            }

            if (string.IsNullOrEmpty(config.Tenant.Domain))
            {
                result.Errors.Add("Tenant domain is required");
                result.IsValid = false;
            }

            // Validate admin user configuration
            if (string.IsNullOrEmpty(config.AdminUser.Email))
            {
                result.Errors.Add("Admin email is required");
                result.IsValid = false;
            }

            if (string.IsNullOrEmpty(config.AdminUser.Password))
            {
                result.Errors.Add("Admin password is required");
                result.IsValid = false;
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating configuration");
            result.Errors.Add($"Validation error: {ex.Message}");
            result.IsValid = false;
            return result;
        }
    }

    public string GenerateSecureKey(int length = 32)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    public string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt());
    }

    private Dictionary<string, object> ConvertToAppSettings(WizardConfiguration config)
    {
        return new Dictionary<string, object>
        {
            ["ConnectionStrings"] = new Dictionary<string, string>
            {
                ["DefaultConnection"] = config.Database.ConnectionString,
                ["DefaultProvider"] = config.Database.Provider,
                ["Redis"] = config.Redis.ConnectionString
            },
            ["Tenant"] = new Dictionary<string, object>
            {
                ["Name"] = config.Tenant.Name,
                ["Description"] = config.Tenant.Description,
                ["Domain"] = config.Tenant.Domain,
                ["Plan"] = config.Tenant.Plan
            },
            ["AdminUser"] = new Dictionary<string, object>
            {
                ["Email"] = config.AdminUser.Email,
                ["FirstName"] = config.AdminUser.FirstName,
                ["LastName"] = config.AdminUser.LastName
            },
            ["SystemPreferences"] = config.Preferences.CustomSettings
        };
    }

    private WizardConfiguration ConvertFromAppSettings(Dictionary<string, object> appSettings)
    {
        var config = new WizardConfiguration();

        // Extract connection strings
        if (appSettings.TryGetValue("ConnectionStrings", out var connStrings) && connStrings is JsonElement connElement)
        {
            if (connElement.TryGetProperty("DefaultConnection", out var defaultConn))
                config.Database.ConnectionString = defaultConn.GetString() ?? string.Empty;
            if (connElement.TryGetProperty("DefaultProvider", out var provider))
                config.Database.Provider = provider.GetString() ?? "SqlServer";
            if (connElement.TryGetProperty("Redis", out var redis))
                config.Redis.ConnectionString = redis.GetString() ?? string.Empty;
        }

        // Extract tenant info
        if (appSettings.TryGetValue("Tenant", out var tenant) && tenant is JsonElement tenantElement)
        {
            if (tenantElement.TryGetProperty("Name", out var name))
                config.Tenant.Name = name.GetString() ?? string.Empty;
            if (tenantElement.TryGetProperty("Description", out var desc))
                config.Tenant.Description = desc.GetString() ?? string.Empty;
            if (tenantElement.TryGetProperty("Domain", out var domain))
                config.Tenant.Domain = domain.GetString() ?? string.Empty;
            if (tenantElement.TryGetProperty("Plan", out var plan))
                config.Tenant.Plan = plan.GetString() ?? "Standard";
        }

        return config;
    }
}
