namespace PluginCore.Base;

/// <summary>
/// Background service that processes scheduled messages.
/// </summary>
public class ScheduledMessageProcessor : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ScheduledMessageProcessor> _logger;
    private readonly TimeSpan _processingInterval;

    public ScheduledMessageProcessor(
        IServiceProvider serviceProvider,
        ILogger<ScheduledMessageProcessor> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _processingInterval = TimeSpan.FromMinutes(1); // Check every minute
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Scheduled message processor started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessScheduledMessages(stoppingToken);
                await Task.Delay(_processingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing scheduled messages");
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Wait longer on error
            }
        }

        _logger.LogInformation("Scheduled message processor stopped");
    }

    private async Task ProcessScheduledMessages(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var schedulingService = scope.ServiceProvider.GetService<IMessageSchedulingService>();
        
        if (schedulingService == null)
        {
            _logger.LogWarning("No message scheduling service found");
            return;
        }

        // Get messages that are ready to be sent
        var cutoffTime = DateTimeOffset.UtcNow.AddMinutes(1); // Include messages scheduled for the next minute
        var pendingMessages = await schedulingService.GetPendingMessagesAsync(cutoffTime, cancellationToken);

        if (!pendingMessages.Any())
        {
            _logger.LogDebug("No pending scheduled messages found");
            return;
        }

        _logger.LogInformation("Processing {Count} scheduled messages", pendingMessages.Count);

        foreach (var scheduledMessage in pendingMessages)
        {
            try
            {
                await ProcessSingleScheduledMessage(scheduledMessage, schedulingService, scope.ServiceProvider, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing scheduled message {MessageId}", scheduledMessage.Id);
                await schedulingService.MarkMessageAsFailedAsync(scheduledMessage.Id, ex.Message, cancellationToken);
            }
        }
    }

    private async Task ProcessSingleScheduledMessage(
        ScheduledMessage scheduledMessage,
        IMessageSchedulingService schedulingService,
        IServiceProvider serviceProvider,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Processing scheduled message {MessageId} for gateway {GatewayId}", 
            scheduledMessage.Id, scheduledMessage.GatewayId);

        // Find the appropriate gateway service
        var gateway = FindGatewayService(scheduledMessage.GatewayId, serviceProvider);
        if (gateway == null)
        {
            await schedulingService.MarkMessageAsFailedAsync(
                scheduledMessage.Id, 
                $"Gateway {scheduledMessage.GatewayId} not found", 
                cancellationToken);
            return;
        }

        try
        {
            // Send the message
            var sendResult = await gateway.SendMessageAsync(scheduledMessage.Payload, cancellationToken);
            
            // Mark as sent or failed based on the result
            if (sendResult.Status == "Sent" || sendResult.Status == "Delivered")
            {
                await schedulingService.MarkMessageAsSentAsync(scheduledMessage.Id, sendResult, cancellationToken);
                _logger.LogInformation("Scheduled message {MessageId} sent successfully", scheduledMessage.Id);
            }
            else
            {
                await schedulingService.MarkMessageAsFailedAsync(
                    scheduledMessage.Id, 
                    $"Send failed with status: {sendResult.Status}", 
                    cancellationToken);
                _logger.LogWarning("Scheduled message {MessageId} failed to send", scheduledMessage.Id);
            }
        }
        catch (Exception ex)
        {
            await schedulingService.MarkMessageAsFailedAsync(scheduledMessage.Id, ex.Message, cancellationToken);
            throw;
        }
    }

    private IGatewayMessagePluginType? FindGatewayService(string gatewayId, IServiceProvider serviceProvider)
    {
        // This is a simplified approach. In a real implementation, you might have a registry
        // of gateway services or use a more sophisticated service discovery mechanism.
        
        var gateways = serviceProvider.GetServices<IGatewayMessagePluginType>();
        
        // Try to match by gateway type name
        return gateways.FirstOrDefault(g => g.GetType().Name.Contains(gatewayId, StringComparison.OrdinalIgnoreCase));
    }
}

/// <summary>
/// Extension methods for registering the scheduled message processor.
/// </summary>
public static class ScheduledMessageProcessorExtensions
{
    /// <summary>
    /// Adds the scheduled message processor as a hosted service.
    /// </summary>
    public static IServiceCollection AddScheduledMessageProcessor(this IServiceCollection services)
    {
        services.AddHostedService<ScheduledMessageProcessor>();
        return services;
    }

    /// <summary>
    /// Adds the default message scheduling and storage services.
    /// </summary>
    public static IServiceCollection AddMessageSchedulingServices(this IServiceCollection services)
    {
        services.AddSingleton<IMessageSchedulingService, DefaultMessageSchedulingService>();
        services.AddSingleton<IMessageStorageService, DefaultMessageStorageService>();
        return services;
    }
}

/// <summary>
/// Configuration options for the scheduled message processor.
/// </summary>
public class ScheduledMessageProcessorOptions
{
    /// <summary>
    /// How often to check for pending messages (default: 1 minute).
    /// </summary>
    public TimeSpan ProcessingInterval { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// How long to wait before retrying after an error (default: 5 minutes).
    /// </summary>
    public TimeSpan ErrorRetryDelay { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Maximum number of retry attempts for failed messages (default: 3).
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// How far in advance to look for scheduled messages (default: 1 minute).
    /// </summary>
    public TimeSpan LookaheadTime { get; set; } = TimeSpan.FromMinutes(1);
}

/// <summary>
/// Enhanced scheduled message processor with configurable options.
/// </summary>
public class ConfigurableScheduledMessageProcessor : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ConfigurableScheduledMessageProcessor> _logger;
    private readonly ScheduledMessageProcessorOptions _options;

    public ConfigurableScheduledMessageProcessor(
        IServiceProvider serviceProvider,
        ILogger<ConfigurableScheduledMessageProcessor> logger,
        ScheduledMessageProcessorOptions? options = null)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _options = options ?? new ScheduledMessageProcessorOptions();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Configurable scheduled message processor started with interval {Interval}", 
            _options.ProcessingInterval);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessScheduledMessagesWithRetry(stoppingToken);
                await Task.Delay(_options.ProcessingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing scheduled messages");
                await Task.Delay(_options.ErrorRetryDelay, stoppingToken);
            }
        }

        _logger.LogInformation("Configurable scheduled message processor stopped");
    }

    private async Task ProcessScheduledMessagesWithRetry(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var schedulingService = scope.ServiceProvider.GetService<IMessageSchedulingService>();
        
        if (schedulingService == null)
        {
            _logger.LogWarning("No message scheduling service found");
            return;
        }

        var cutoffTime = DateTimeOffset.UtcNow.Add(_options.LookaheadTime);
        var pendingMessages = await schedulingService.GetPendingMessagesAsync(cutoffTime, cancellationToken);

        if (!pendingMessages.Any())
        {
            return;
        }

        _logger.LogInformation("Processing {Count} scheduled messages", pendingMessages.Count);

        foreach (var scheduledMessage in pendingMessages)
        {
            // Skip messages that have exceeded retry attempts
            if (scheduledMessage.RetryCount >= _options.MaxRetryAttempts)
            {
                await schedulingService.MarkMessageAsFailedAsync(
                    scheduledMessage.Id, 
                    $"Exceeded maximum retry attempts ({_options.MaxRetryAttempts})", 
                    cancellationToken);
                continue;
            }

            try
            {
                await ProcessSingleScheduledMessageWithRetry(scheduledMessage, schedulingService, scope.ServiceProvider, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing scheduled message {MessageId} (attempt {Attempt})", 
                    scheduledMessage.Id, scheduledMessage.RetryCount + 1);
                
                await schedulingService.MarkMessageAsFailedAsync(scheduledMessage.Id, ex.Message, cancellationToken);
            }
        }
    }

    private async Task ProcessSingleScheduledMessageWithRetry(
        ScheduledMessage scheduledMessage,
        IMessageSchedulingService schedulingService,
        IServiceProvider serviceProvider,
        CancellationToken cancellationToken)
    {
        var gateway = FindGatewayService(scheduledMessage.GatewayId, serviceProvider);
        if (gateway == null)
        {
            await schedulingService.MarkMessageAsFailedAsync(
                scheduledMessage.Id, 
                $"Gateway {scheduledMessage.GatewayId} not found", 
                cancellationToken);
            return;
        }

        var sendResult = await gateway.SendMessageAsync(scheduledMessage.Payload, cancellationToken);
        
        if (sendResult.Status == "Sent" || sendResult.Status == "Delivered")
        {
            await schedulingService.MarkMessageAsSentAsync(scheduledMessage.Id, sendResult, cancellationToken);
            _logger.LogInformation("Scheduled message {MessageId} sent successfully", scheduledMessage.Id);
        }
        else
        {
            await schedulingService.MarkMessageAsFailedAsync(
                scheduledMessage.Id, 
                $"Send failed with status: {sendResult.Status}", 
                cancellationToken);
        }
    }

    private IGatewayMessagePluginType? FindGatewayService(string gatewayId, IServiceProvider serviceProvider)
    {
        var gateways = serviceProvider.GetServices<IGatewayMessagePluginType>();
        return gateways.FirstOrDefault(g => g.GetType().Name.Contains(gatewayId, StringComparison.OrdinalIgnoreCase));
    }
}
