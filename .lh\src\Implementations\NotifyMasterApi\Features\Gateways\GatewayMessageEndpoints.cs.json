{"sourceFile": "src/Implementations/NotifyMasterApi/Features/Gateways/GatewayMessageEndpoints.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751241529332, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751241529332, "name": "Commit-0", "content": "using FastEndpoints;\nusing PluginCore.Base;\nusing PluginCore.Models;\nusing PluginCore.Interfaces;\nusing System.ComponentModel.DataAnnotations;\n\nnamespace NotifyMasterApi.Features.Gateways;\n\n// ============================================================================\n// Send Single Message\n// ============================================================================\n\npublic class SendGatewayMessageRequest\n{\n    [Required]\n    public string GatewayName { get; set; } = string.Empty;\n    \n    [Required]\n    public MessagePayload Payload { get; set; } = new();\n}\n\npublic class SendGatewayMessageEndpoint : Endpoint<SendGatewayMessageRequest, MessageSendResult>\n{\n    private readonly IPluginManager _pluginManager;\n    private readonly ILogger<SendGatewayMessageEndpoint> _logger;\n\n    public SendGatewayMessageEndpoint(IPluginManager pluginManager, ILogger<SendGatewayMessageEndpoint> logger)\n    {\n        _pluginManager = pluginManager;\n        _logger = logger;\n    }\n\n    public override void Configure()\n    {\n        Post(\"/api/gateways/{gatewayName}/send\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Send message through gateway\";\n            s.Description = \"Send a single message through the specified gateway plugin\";\n            s.Response<MessageSendResult>(200, \"Message sent successfully\");\n            s.Response(404, \"Gateway not found\");\n            s.Response(400, \"Invalid message payload\");\n        });\n        Tags(\"🚀 Gateway Messages\");\n    }\n\n    public override async Task HandleAsync(SendGatewayMessageRequest req, CancellationToken ct)\n    {\n        var gateway = _pluginManager.GetPlugin<IGatewayMessagePluginType>(req.GatewayName);\n        \n        if (gateway == null)\n        {\n            await SendNotFoundAsync(ct);\n            return;\n        }\n\n        try\n        {\n            var result = await gateway.SendMessageAsync(req.Payload, ct);\n            await SendOkAsync(result, ct);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error sending message through gateway {GatewayName}\", req.GatewayName);\n            await SendErrorsAsync(500, ct);\n        }\n    }\n}\n\n// ============================================================================\n// Send Bulk Messages\n// ============================================================================\n\npublic class SendBulkGatewayMessagesRequest\n{\n    [Required]\n    public string GatewayName { get; set; } = string.Empty;\n    \n    [Required]\n    public List<MessagePayload> Payloads { get; set; } = new();\n}\n\npublic class SendBulkGatewayMessagesEndpoint : Endpoint<SendBulkGatewayMessagesRequest, List<MessageSendResult>>\n{\n    private readonly IPluginManager _pluginManager;\n    private readonly ILogger<SendBulkGatewayMessagesEndpoint> _logger;\n\n    public SendBulkGatewayMessagesEndpoint(IPluginManager pluginManager, ILogger<SendBulkGatewayMessagesEndpoint> logger)\n    {\n        _pluginManager = pluginManager;\n        _logger = logger;\n    }\n\n    public override void Configure()\n    {\n        Post(\"/api/gateways/{gatewayName}/send-bulk\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Send bulk messages through gateway\";\n            s.Description = \"Send multiple messages in a single optimized operation through the specified gateway\";\n            s.Response<List<MessageSendResult>>(200, \"Messages sent successfully\");\n            s.Response(404, \"Gateway not found\");\n            s.Response(400, \"Invalid message payloads\");\n        });\n        Tags(\"🚀 Gateway Messages\");\n    }\n\n    public override async Task HandleAsync(SendBulkGatewayMessagesRequest req, CancellationToken ct)\n    {\n        var gateway = _pluginManager.GetPlugin<IGatewayMessagePluginType>(req.GatewayName);\n        \n        if (gateway == null)\n        {\n            await SendNotFoundAsync(ct);\n            return;\n        }\n\n        try\n        {\n            var results = await gateway.SendBulkMessagesAsync(req.Payloads, ct);\n            await SendOkAsync(results.ToList(), ct);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error sending bulk messages through gateway {GatewayName}\", req.GatewayName);\n            await SendErrorsAsync(500, ct);\n        }\n    }\n}\n\n// ============================================================================\n// Send Templated Message\n// ============================================================================\n\npublic class SendTemplatedGatewayMessageRequest\n{\n    [Required]\n    public string GatewayName { get; set; } = string.Empty;\n    \n    [Required]\n    public string TemplateId { get; set; } = string.Empty;\n    \n    [Required]\n    public Dictionary<string, string> TemplateData { get; set; } = new();\n    \n    [Required]\n    public Recipient Recipient { get; set; } = new();\n}\n\npublic class SendTemplatedGatewayMessageEndpoint : Endpoint<SendTemplatedGatewayMessageRequest, MessageSendResult>\n{\n    private readonly IPluginManager _pluginManager;\n    private readonly ILogger<SendTemplatedGatewayMessageEndpoint> _logger;\n\n    public SendTemplatedGatewayMessageEndpoint(IPluginManager pluginManager, ILogger<SendTemplatedGatewayMessageEndpoint> logger)\n    {\n        _pluginManager = pluginManager;\n        _logger = logger;\n    }\n\n    public override void Configure()\n    {\n        Post(\"/api/gateways/{gatewayName}/send-templated\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Send templated message through gateway\";\n            s.Description = \"Send a message using a registered template and dynamic data through the specified gateway\";\n            s.Response<MessageSendResult>(200, \"Templated message sent successfully\");\n            s.Response(404, \"Gateway or template not found\");\n            s.Response(400, \"Invalid template data or recipient\");\n        });\n        Tags(\"🚀 Gateway Messages\");\n    }\n\n    public override async Task HandleAsync(SendTemplatedGatewayMessageRequest req, CancellationToken ct)\n    {\n        var gateway = _pluginManager.GetPlugin<IGatewayMessagePluginType>(req.GatewayName);\n        \n        if (gateway == null)\n        {\n            await SendNotFoundAsync(ct);\n            return;\n        }\n\n        try\n        {\n            var result = await gateway.SendTemplatedMessageAsync(req.TemplateId, req.TemplateData, req.Recipient, ct);\n            await SendOkAsync(result, ct);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error sending templated message through gateway {GatewayName}\", req.GatewayName);\n            await SendErrorsAsync(500, ct);\n        }\n    }\n}\n\n// ============================================================================\n// Schedule Message\n// ============================================================================\n\npublic class ScheduleGatewayMessageRequest\n{\n    [Required]\n    public string GatewayName { get; set; } = string.Empty;\n    \n    [Required]\n    public MessagePayload Payload { get; set; } = new();\n    \n    [Required]\n    public DateTimeOffset ScheduledTime { get; set; }\n}\n\npublic class ScheduleGatewayMessageEndpoint : Endpoint<ScheduleGatewayMessageRequest, MessageScheduleResult>\n{\n    private readonly IPluginManager _pluginManager;\n    private readonly ILogger<ScheduleGatewayMessageEndpoint> _logger;\n\n    public ScheduleGatewayMessageEndpoint(IPluginManager pluginManager, ILogger<ScheduleGatewayMessageEndpoint> logger)\n    {\n        _pluginManager = pluginManager;\n        _logger = logger;\n    }\n\n    public override void Configure()\n    {\n        Post(\"/api/gateways/{gatewayName}/schedule\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Schedule message through gateway\";\n            s.Description = \"Schedule a message for future delivery through the specified gateway\";\n            s.Response<MessageScheduleResult>(200, \"Message scheduled successfully\");\n            s.Response(404, \"Gateway not found\");\n            s.Response(400, \"Invalid message payload or schedule time\");\n        });\n        Tags(\"🚀 Gateway Messages\");\n    }\n\n    public override async Task HandleAsync(ScheduleGatewayMessageRequest req, CancellationToken ct)\n    {\n        var gateway = _pluginManager.GetPlugin<IGatewayMessagePluginType>(req.GatewayName);\n        \n        if (gateway == null)\n        {\n            await SendNotFoundAsync(ct);\n            return;\n        }\n\n        try\n        {\n            var result = await gateway.ScheduleMessageAsync(req.Payload, req.ScheduledTime, ct);\n            await SendOkAsync(result, ct);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error scheduling message through gateway {GatewayName}\", req.GatewayName);\n            await SendErrorsAsync(500, ct);\n        }\n    }\n}\n"}]}