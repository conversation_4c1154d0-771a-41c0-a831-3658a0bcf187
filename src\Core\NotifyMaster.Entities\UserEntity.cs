// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Entities;

/// <summary>
/// Database entity for User
/// </summary>
[Table("Users")]
public class UserEntity
{
    [Key]
    [MaxLength(36)]
    public string Id { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(36)]
    public string TenantId { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string Username { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(255)]
    public string Email { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? FirstName { get; set; }
    
    [MaxLength(100)]
    public string? LastName { get; set; }
    
    [Required]
    [MaxLength(255)]
    public string PasswordHash { get; set; } = string.Empty;
    
    public int Status { get; set; } = 0; // UserStatus enum as int
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public DateTime? LastLoginAt { get; set; }
    
    [MaxLength(36)]
    public string? CreatedBy { get; set; }
    
    [MaxLength(36)]
    public string? UpdatedBy { get; set; }
    
    [Column(TypeName = "jsonb")]
    public string Profile { get; set; } = "{}";
    
    // Navigation properties
    public virtual TenantEntity Tenant { get; set; } = null!;
    public virtual ICollection<UserRoleEntity> Roles { get; set; } = new List<UserRoleEntity>();
    public virtual ICollection<UserPermissionEntity> Permissions { get; set; } = new List<UserPermissionEntity>();

}
