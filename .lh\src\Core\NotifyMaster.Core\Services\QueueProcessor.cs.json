{"sourceFile": "src/Core/NotifyMaster.Core/Services/QueueProcessor.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751230746616, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751230746616, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Core.Services;\n\n/// <summary>\n/// Implementation of queue processor that routes messages to appropriate handlers\n/// </summary>\npublic class QueueProcessor : IQueueProcessor\n{\n    private readonly ILogger<QueueProcessor> _logger;\n    private readonly IServiceProvider _serviceProvider;\n    private readonly Dictionary<Type, Type> _messageHandlers;\n\n    public QueueProcessor(ILogger<QueueProcessor> logger, IServiceProvider serviceProvider)\n    {\n        _logger = logger;\n        _serviceProvider = serviceProvider;\n        _messageHandlers = new Dictionary<Type, Type>();\n        \n        // Register message handlers\n        RegisterMessageHandlers();\n    }\n\n    public async Task ProcessMessageAsync<T>(QueueMessage<T> message, CancellationToken cancellationToken = default) where T : class\n    {\n        try\n        {\n            _logger.LogInformation(\"Processing message {MessageId} of type {MessageType} from queue {QueueName}\", \n                message.Id, typeof(T).Name, message.QueueName);\n\n            // Update message status\n            message.Status = QueueMessageStatus.Processing;\n            message.ProcessedAt = DateTime.UtcNow;\n\n            // Get the appropriate handler\n            var handler = GetMessageHandler<T>();\n            if (handler == null)\n            {\n                var errorMessage = $\"No handler found for message type {typeof(T).Name}\";\n                _logger.LogError(errorMessage);\n                \n                message.Status = QueueMessageStatus.Failed;\n                message.ErrorMessage = errorMessage;\n                return;\n            }\n\n            // Process the message\n            var result = await handler.HandleAsync(message.Payload, message, cancellationToken);\n            \n            if (result.IsSuccess)\n            {\n                message.Status = QueueMessageStatus.Completed;\n                _logger.LogInformation(\"Successfully processed message {MessageId}\", message.Id);\n            }\n            else\n            {\n                message.Status = QueueMessageStatus.Failed;\n                message.ErrorMessage = result.Message;\n                message.RetryCount++;\n                \n                _logger.LogError(\"Failed to process message {MessageId}: {ErrorMessage}\", message.Id, result.Message);\n                \n                // Check if we should retry\n                if (message.RetryCount < message.MaxRetries)\n                {\n                    _logger.LogInformation(\"Retrying message {MessageId} (attempt {RetryCount}/{MaxRetries})\", \n                        message.Id, message.RetryCount + 1, message.MaxRetries);\n                    \n                    // Re-enqueue with delay\n                    var queueService = _serviceProvider.GetRequiredService<IQueueService>();\n                    var delay = TimeSpan.FromMinutes(Math.Pow(2, message.RetryCount)); // Exponential backoff\n                    await queueService.EnqueueDelayedAsync(message.QueueName, message.Payload, delay, cancellationToken);\n                }\n            }\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Unexpected error processing message {MessageId}\", message.Id);\n            \n            message.Status = QueueMessageStatus.Failed;\n            message.ErrorMessage = ex.Message;\n            message.RetryCount++;\n            \n            // Check if we should retry\n            if (message.RetryCount < message.MaxRetries)\n            {\n                try\n                {\n                    var queueService = _serviceProvider.GetRequiredService<IQueueService>();\n                    var delay = TimeSpan.FromMinutes(Math.Pow(2, message.RetryCount)); // Exponential backoff\n                    await queueService.EnqueueDelayedAsync(message.QueueName, message.Payload, delay, cancellationToken);\n                }\n                catch (Exception retryEx)\n                {\n                    _logger.LogError(retryEx, \"Failed to retry message {MessageId}\", message.Id);\n                }\n            }\n        }\n    }\n\n    private IMessageHandler<T>? GetMessageHandler<T>() where T : class\n    {\n        var messageType = typeof(T);\n        \n        if (_messageHandlers.TryGetValue(messageType, out var handlerType))\n        {\n            return _serviceProvider.GetService(handlerType) as IMessageHandler<T>;\n        }\n\n        // Try to find handler by convention\n        var handlerTypeName = $\"{messageType.Name}Handler\";\n        var handlerTypes = AppDomain.CurrentDomain.GetAssemblies()\n            .SelectMany(a => a.GetTypes())\n            .Where(t => t.Name == handlerTypeName && typeof(IMessageHandler<T>).IsAssignableFrom(t))\n            .ToList();\n\n        if (handlerTypes.Count == 1)\n        {\n            var handler = _serviceProvider.GetService(handlerTypes[0]) as IMessageHandler<T>;\n            if (handler != null)\n            {\n                _messageHandlers[messageType] = handlerTypes[0];\n                return handler;\n            }\n        }\n\n        _logger.LogWarning(\"No handler found for message type {MessageType}\", messageType.Name);\n        return null;\n    }\n\n    private void RegisterMessageHandlers()\n    {\n        // This method can be used to manually register message handlers\n        // For now, we'll use convention-based discovery in GetMessageHandler\n        _logger.LogDebug(\"Message handler registration completed\");\n    }\n}\n\n/// <summary>\n/// Base queue-aware service that implements the queue-first pattern\n/// </summary>\npublic abstract class QueueAwareServiceBase\n{\n    protected readonly ILogger Logger;\n    protected readonly IQueueService QueueService;\n    protected readonly ITenantContext TenantContext;\n\n    protected QueueAwareServiceBase(\n        ILogger logger,\n        IQueueService queueService,\n        ITenantContext tenantContext)\n    {\n        Logger = logger;\n        QueueService = queueService;\n        TenantContext = tenantContext;\n    }\n\n    /// <summary>\n    /// Enqueues a message and returns immediately with a correlation ID\n    /// </summary>\n    protected async Task<OperationResult<string>> EnqueueAndRespondAsync<T>(\n        string queueName, \n        T message, \n        CancellationToken cancellationToken = default) where T : class\n    {\n        try\n        {\n            // Generate correlation ID\n            var correlationId = Guid.NewGuid().ToString();\n            \n            // Add tenant context if available\n            if (message is IHasTenantId tenantMessage && !string.IsNullOrEmpty(TenantContext.TenantId))\n            {\n                tenantMessage.TenantId = TenantContext.TenantId;\n            }\n            \n            // Add correlation ID if message supports it\n            if (message is IHasCorrelationId correlationMessage)\n            {\n                correlationMessage.CorrelationId = correlationId;\n            }\n\n            // Enqueue the message\n            var result = await QueueService.EnqueueAsync(queueName, message, cancellationToken);\n            \n            if (result.IsSuccess)\n            {\n                Logger.LogInformation(\"Enqueued message of type {MessageType} to queue {QueueName} with correlation ID {CorrelationId}\", \n                    typeof(T).Name, queueName, correlationId);\n                \n                return OperationResult<string>.Success(correlationId, \"Request queued for processing\");\n            }\n            else\n            {\n                Logger.LogError(\"Failed to enqueue message of type {MessageType} to queue {QueueName}: {ErrorMessage}\", \n                    typeof(T).Name, queueName, result.Message);\n                \n                return OperationResult<string>.Failure(\"Failed to queue request for processing\", result.Exception);\n            }\n        }\n        catch (Exception ex)\n        {\n            Logger.LogError(ex, \"Unexpected error enqueueing message of type {MessageType} to queue {QueueName}\", \n                typeof(T).Name, queueName);\n            \n            return OperationResult<string>.Failure(\"Unexpected error occurred while queueing request\", ex);\n        }\n    }\n\n    /// <summary>\n    /// Enqueues a scheduled message\n    /// </summary>\n    protected async Task<OperationResult<string>> EnqueueScheduledAsync<T>(\n        string queueName, \n        T message, \n        DateTimeOffset scheduleAt,\n        CancellationToken cancellationToken = default) where T : class\n    {\n        try\n        {\n            var correlationId = Guid.NewGuid().ToString();\n            \n            if (message is IHasTenantId tenantMessage && !string.IsNullOrEmpty(TenantContext.TenantId))\n            {\n                tenantMessage.TenantId = TenantContext.TenantId;\n            }\n            \n            if (message is IHasCorrelationId correlationMessage)\n            {\n                correlationMessage.CorrelationId = correlationId;\n            }\n\n            var result = await QueueService.EnqueueScheduledAsync(queueName, message, scheduleAt, cancellationToken);\n            \n            if (result.IsSuccess)\n            {\n                Logger.LogInformation(\"Scheduled message of type {MessageType} to queue {QueueName} at {ScheduleAt} with correlation ID {CorrelationId}\", \n                    typeof(T).Name, queueName, scheduleAt, correlationId);\n                \n                return OperationResult<string>.Success(correlationId, \"Request scheduled for processing\");\n            }\n            else\n            {\n                return OperationResult<string>.Failure(\"Failed to schedule request for processing\", result.Exception);\n            }\n        }\n        catch (Exception ex)\n        {\n            Logger.LogError(ex, \"Unexpected error scheduling message of type {MessageType} to queue {QueueName}\", \n                typeof(T).Name, queueName);\n            \n            return OperationResult<string>.Failure(\"Unexpected error occurred while scheduling request\", ex);\n        }\n    }\n}\n\n\n"}]}