namespace NotifyMaster.Entities;

/// <summary>
/// Lightweight audit log entity for request tracking
/// Optimized for fast inserts and compliance requirements
/// </summary>
public class RequestAuditLog
{
    [Key]
    [MaxLength(50)]
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Queue identifier for tracking the request through the system
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string QueueId { get; set; } = string.Empty;

    /// <summary>
    /// Unique request identifier
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string RequestId { get; set; } = string.Empty;

    /// <summary>
    /// Correlation ID for tracing across services
    /// </summary>
    [MaxLength(50)]
    public string? CorrelationId { get; set; }

    /// <summary>
    /// Tenant ID for multi-tenancy
    /// </summary>
    [MaxLength(50)]
    public string? TenantId { get; set; }

    /// <summary>
    /// User ID who made the request
    /// </summary>
    [MaxLength(50)]
    public string? UserId { get; set; }

    /// <summary>
    /// HTTP method (GET, POST, PUT, DELETE, etc.)
    /// </summary>
    [Required]
    [MaxLength(10)]
    public string Method { get; set; } = string.Empty;

    /// <summary>
    /// Request path/endpoint
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string Path { get; set; } = string.Empty;

    /// <summary>
    /// Client IP address
    /// </summary>
    [MaxLength(45)] // IPv6 max length
    public string? IpAddress { get; set; }

    /// <summary>
    /// When the request was queued
    /// </summary>
    public DateTime QueuedAt { get; set; }

    /// <summary>
    /// Current status of the request
    /// </summary>
    [Required]
    public QueuedRequestStatus Status { get; set; }

    /// <summary>
    /// When the audit log entry was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// HTTP status code (set after processing)
    /// </summary>
    public int? StatusCode { get; set; }

    /// <summary>
    /// When the request was processed (set after completion)
    /// </summary>
    public DateTime? ProcessedAt { get; set; }

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public int? ProcessingTimeMs { get; set; }

    /// <summary>
    /// Response size in bytes
    /// </summary>
    public long? ResponseSize { get; set; }

    /// <summary>
    /// Error message if request failed
    /// </summary>
    [MaxLength(1000)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Detailed error information (JSON)
    /// </summary>
    public string? ErrorDetails { get; set; }

    // Navigation properties
    public TenantEntity? Tenant { get; set; }
    public UserEntity? User { get; set; }
}

/// <summary>
/// Status enumeration for queued requests
/// </summary>
public enum QueuedRequestStatus
{
    Queued = 0,
    Processing = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}
