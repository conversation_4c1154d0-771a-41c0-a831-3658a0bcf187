// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Services;

/// <summary>
/// Implementation of queue processor that routes messages to appropriate handlers
/// </summary>
public class QueueProcessor : IQueueProcessor
{
    private readonly ILogger<QueueProcessor> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly Dictionary<Type, Type> _messageHandlers;

    public QueueProcessor(ILogger<QueueProcessor> logger, IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _messageHandlers = new Dictionary<Type, Type>();
        
        // Register message handlers
        RegisterMessageHandlers();
    }

    public async Task ProcessMessageAsync<T>(QueueMessage<T> message, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            _logger.LogInformation("Processing message {MessageId} of type {MessageType} from queue {QueueName}", 
                message.Id, typeof(T).Name, message.QueueName);

            // Update message status
            message.Status = QueueMessageStatus.Processing;
            message.ProcessedAt = DateTime.UtcNow;

            // Get the appropriate handler
            var handler = GetMessageHandler<T>();
            if (handler == null)
            {
                var errorMessage = $"No handler found for message type {typeof(T).Name}";
                _logger.LogError(errorMessage);
                
                message.Status = QueueMessageStatus.Failed;
                message.ErrorMessage = errorMessage;
                return;
            }

            // Process the message
            var result = await handler.HandleAsync(message.Payload, message, cancellationToken);
            
            if (result.IsSuccess)
            {
                message.Status = QueueMessageStatus.Completed;
                _logger.LogInformation("Successfully processed message {MessageId}", message.Id);
            }
            else
            {
                message.Status = QueueMessageStatus.Failed;
                message.ErrorMessage = result.Message;
                message.RetryCount++;
                
                _logger.LogError("Failed to process message {MessageId}: {ErrorMessage}", message.Id, result.Message);
                
                // Check if we should retry
                if (message.RetryCount < message.MaxRetries)
                {
                    _logger.LogInformation("Retrying message {MessageId} (attempt {RetryCount}/{MaxRetries})", 
                        message.Id, message.RetryCount + 1, message.MaxRetries);
                    
                    // Re-enqueue with delay
                    var queueService = _serviceProvider.GetRequiredService<IQueueService>();
                    var delay = TimeSpan.FromMinutes(Math.Pow(2, message.RetryCount)); // Exponential backoff
                    await queueService.EnqueueDelayedAsync(message.QueueName, message.Payload, delay, cancellationToken);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error processing message {MessageId}", message.Id);
            
            message.Status = QueueMessageStatus.Failed;
            message.ErrorMessage = ex.Message;
            message.RetryCount++;
            
            // Check if we should retry
            if (message.RetryCount < message.MaxRetries)
            {
                try
                {
                    var queueService = _serviceProvider.GetRequiredService<IQueueService>();
                    var delay = TimeSpan.FromMinutes(Math.Pow(2, message.RetryCount)); // Exponential backoff
                    await queueService.EnqueueDelayedAsync(message.QueueName, message.Payload, delay, cancellationToken);
                }
                catch (Exception retryEx)
                {
                    _logger.LogError(retryEx, "Failed to retry message {MessageId}", message.Id);
                }
            }
        }
    }

    private IMessageHandler<T>? GetMessageHandler<T>() where T : class
    {
        var messageType = typeof(T);
        
        if (_messageHandlers.TryGetValue(messageType, out var handlerType))
        {
            return _serviceProvider.GetService(handlerType) as IMessageHandler<T>;
        }

        // Try to find handler by convention
        var handlerTypeName = $"{messageType.Name}Handler";
        var handlerTypes = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(a => a.GetTypes())
            .Where(t => t.Name == handlerTypeName && typeof(IMessageHandler<T>).IsAssignableFrom(t))
            .ToList();

        if (handlerTypes.Count == 1)
        {
            var handler = _serviceProvider.GetService(handlerTypes[0]) as IMessageHandler<T>;
            if (handler != null)
            {
                _messageHandlers[messageType] = handlerTypes[0];
                return handler;
            }
        }

        _logger.LogWarning("No handler found for message type {MessageType}", messageType.Name);
        return null;
    }

    private void RegisterMessageHandlers()
    {
        // This method can be used to manually register message handlers
        // For now, we'll use convention-based discovery in GetMessageHandler
        _logger.LogDebug("Message handler registration completed");
    }
}

/// <summary>
/// Base queue-aware service that implements the queue-first pattern
/// </summary>
public abstract class QueueAwareServiceBase
{
    protected readonly ILogger Logger;
    protected readonly IQueueService QueueService;
    protected readonly ITenantContext TenantContext;

    protected QueueAwareServiceBase(
        ILogger logger,
        IQueueService queueService,
        ITenantContext tenantContext)
    {
        Logger = logger;
        QueueService = queueService;
        TenantContext = tenantContext;
    }

    /// <summary>
    /// Enqueues a message and returns immediately with a correlation ID
    /// </summary>
    protected async Task<OperationResult<string>> EnqueueAndRespondAsync<T>(
        string queueName, 
        T message, 
        CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            // Generate correlation ID
            var correlationId = Guid.NewGuid().ToString();
            
            // Add tenant context if available
            if (message is IHasTenantId tenantMessage && !string.IsNullOrEmpty(TenantContext.TenantId))
            {
                tenantMessage.TenantId = TenantContext.TenantId;
            }
            
            // Add correlation ID if message supports it
            if (message is IHasCorrelationId correlationMessage)
            {
                correlationMessage.CorrelationId = correlationId;
            }

            // Enqueue the message
            var result = await QueueService.EnqueueAsync(queueName, message, cancellationToken);
            
            if (result.IsSuccess)
            {
                Logger.LogInformation("Enqueued message of type {MessageType} to queue {QueueName} with correlation ID {CorrelationId}", 
                    typeof(T).Name, queueName, correlationId);
                
                return OperationResult<string>.Success(correlationId, "Request queued for processing");
            }
            else
            {
                Logger.LogError("Failed to enqueue message of type {MessageType} to queue {QueueName}: {ErrorMessage}", 
                    typeof(T).Name, queueName, result.Message);
                
                return OperationResult<string>.Failure("Failed to queue request for processing", result.Exception);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Unexpected error enqueueing message of type {MessageType} to queue {QueueName}", 
                typeof(T).Name, queueName);
            
            return OperationResult<string>.Failure("Unexpected error occurred while queueing request", ex);
        }
    }

    /// <summary>
    /// Enqueues a scheduled message
    /// </summary>
    protected async Task<OperationResult<string>> EnqueueScheduledAsync<T>(
        string queueName, 
        T message, 
        DateTimeOffset scheduleAt,
        CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var correlationId = Guid.NewGuid().ToString();
            
            if (message is IHasTenantId tenantMessage && !string.IsNullOrEmpty(TenantContext.TenantId))
            {
                tenantMessage.TenantId = TenantContext.TenantId;
            }
            
            if (message is IHasCorrelationId correlationMessage)
            {
                correlationMessage.CorrelationId = correlationId;
            }

            var result = await QueueService.EnqueueScheduledAsync(queueName, message, scheduleAt, cancellationToken);
            
            if (result.IsSuccess)
            {
                Logger.LogInformation("Scheduled message of type {MessageType} to queue {QueueName} at {ScheduleAt} with correlation ID {CorrelationId}", 
                    typeof(T).Name, queueName, scheduleAt, correlationId);
                
                return OperationResult<string>.Success(correlationId, "Request scheduled for processing");
            }
            else
            {
                return OperationResult<string>.Failure("Failed to schedule request for processing", result.Exception);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Unexpected error scheduling message of type {MessageType} to queue {QueueName}", 
                typeof(T).Name, queueName);
            
            return OperationResult<string>.Failure("Unexpected error occurred while scheduling request", ex);
        }
    }
}


