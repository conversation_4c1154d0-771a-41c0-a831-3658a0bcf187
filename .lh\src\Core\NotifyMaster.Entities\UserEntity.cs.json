{"sourceFile": "src/Core/NotifyMaster.Entities/UserEntity.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751238992917, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751238992917, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Entities;\n\n/// <summary>\n/// Database entity for User\n/// </summary>\n[Table(\"Users\")]\npublic class UserEntity\n{\n    [Key]\n    [MaxLength(36)]\n    public string Id { get; set; } = string.Empty;\n    \n    [Required]\n    [MaxLength(36)]\n    public string TenantId { get; set; } = string.Empty;\n    \n    [Required]\n    [MaxLength(100)]\n    public string Username { get; set; } = string.Empty;\n    \n    [Required]\n    [MaxLength(255)]\n    public string Email { get; set; } = string.Empty;\n    \n    [MaxLength(100)]\n    public string? FirstName { get; set; }\n    \n    [MaxLength(100)]\n    public string? LastName { get; set; }\n    \n    [Required]\n    [MaxLength(255)]\n    public string PasswordHash { get; set; } = string.Empty;\n    \n    public int Status { get; set; } = 0; // UserStatus enum as int\n    \n    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;\n    \n    public DateTime? UpdatedAt { get; set; }\n    \n    public DateTime? LastLoginAt { get; set; }\n    \n    [MaxLength(36)]\n    public string? CreatedBy { get; set; }\n    \n    [MaxLength(36)]\n    public string? UpdatedBy { get; set; }\n    \n    [Column(TypeName = \"jsonb\")]\n    public string Profile { get; set; } = \"{}\";\n    \n    // Navigation properties\n    public virtual TenantEntity Tenant { get; set; } = null!;\n    public virtual ICollection<UserRoleEntity> Roles { get; set; } = new List<UserRoleEntity>();\n    public virtual ICollection<UserPermissionEntity> Permissions { get; set; } = new List<UserPermissionEntity>();\n\n}\n"}]}