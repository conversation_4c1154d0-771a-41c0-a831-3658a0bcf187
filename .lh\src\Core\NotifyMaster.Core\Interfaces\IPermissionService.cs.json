{"sourceFile": "src/Core/NotifyMaster.Core/Interfaces/IPermissionService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751233261512, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751233261512, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\r\n\r\nnamespace NotifyMaster.Core.Interfaces;\r\n\r\n/// <summary>\r\n/// Interface for permission management service\r\n/// </summary>\r\npublic interface IPermissionService\r\n{\r\n    /// <summary>\r\n    /// Gets a permission by ID\r\n    /// </summary>\r\n    Task<Permission?> GetPermissionAsync(string permissionId, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Gets a permission by resource and action\r\n    /// </summary>\r\n    Task<Permission?> GetPermissionAsync(string resource, string action, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Gets all permissions with pagination\r\n    /// </summary>\r\n    Task<IReadOnlyList<Permission>> GetPermissionsAsync(string? resource = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Creates a new permission\r\n    /// </summary>\r\n    Task<OperationResult<Permission>> CreatePermissionAsync(CreatePermissionRequest request, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Updates an existing permission\r\n    /// </summary>\r\n    Task<OperationResult<Permission>> UpdatePermissionAsync(string permissionId, UpdatePermissionRequest request, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Deletes a permission\r\n    /// </summary>\r\n    Task<OperationResult> DeletePermissionAsync(string permissionId, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Gets all roles that have a specific permission\r\n    /// </summary>\r\n    Task<IReadOnlyList<Role>> GetPermissionRolesAsync(string permissionId, CancellationToken cancellationToken = default);\r\n\r\n    /// <summary>\r\n    /// Gets all users that have a specific permission (directly or through roles)\r\n    /// </summary>\r\n    Task<IReadOnlyList<User>> GetPermissionUsersAsync(string permissionId, CancellationToken cancellationToken = default);\r\n}\r\n"}]}