using NotifyMaster.Core.Interfaces;

namespace NotifyMasterApi.Features.Users;

/// <summary>
/// Request model for creating a user
/// </summary>
public class CreateUserRequest
{
    public string TenantId { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public List<string> RoleIds { get; set; } = new();
    public List<string> PermissionIds { get; set; } = new();
}

/// <summary>
/// Request model for updating a user
/// </summary>
public class UpdateUserRequest
{
    public string? Username { get; set; }
    public string? Email { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public bool? IsActive { get; set; }
}

/// <summary>
/// Request model for getting users with pagination
/// </summary>
public class GetUsersRequest
{
    public string? TenantId { get; set; }
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
}

/// <summary>
/// Request model for getting a specific user
/// </summary>
public class GetUserRequest
{
    public string UserId { get; set; } = string.Empty;
}

/// <summary>
/// Request model for changing user password
/// </summary>
public class ChangePasswordRequest
{
    public string UserId { get; set; } = string.Empty;
    public string CurrentPassword { get; set; } = string.Empty;
    public string NewPassword { get; set; } = string.Empty;
}

/// <summary>
/// Request model for assigning role to user
/// </summary>
public class AssignRoleRequest
{
    public string UserId { get; set; } = string.Empty;
    public string RoleId { get; set; } = string.Empty;
}

/// <summary>
/// Request model for granting permission to user
/// </summary>
public class GrantPermissionRequest
{
    public string UserId { get; set; } = string.Empty;
    public string PermissionId { get; set; } = string.Empty;
}

/// <summary>
/// Endpoint for getting all users
/// </summary>
[SkipQueueProcessing] // Read-only operation doesn't need queuing
public class GetUsersEndpoint : StandardEndpointBase<GetUsersRequest, object>
{
    private readonly IUserService _userService;
    private readonly ITenantContext _tenantContext;

    public GetUsersEndpoint(IUserService userService, ITenantContext tenantContext, ILogger<GetUsersEndpoint> logger)
        : base(logger)
    {
        _userService = userService;
        _tenantContext = tenantContext;
    }

    protected override void ConfigureEndpoint()
    {
        this.ConfigureManagementEndpoint(
            "GET",
            "/api/users",
            "Get Users",
            "Get a paginated list of users in the tenant with filtering and sorting options",
            "RequireTenantAdmin",
            new[] { "User Management" });
    }

    public override async Task HandleAsync(GetUsersRequest req, CancellationToken ct)
    {
        try
        {
            var tenantId = req.TenantId ?? _tenantContext.TenantId;
            if (string.IsNullOrEmpty(tenantId))
            {
                await SendAsync(CreateErrorResponse("Tenant ID is required"), 400, ct);
                return;
            }

            var users = await _userService.GetUsersByTenantAsync(tenantId, req.Skip, req.Take, ct);

            var userData = new
            {
                Users = users.Select(u => new
                {
                    u.Id,
                    u.TenantId,
                    u.Username,
                    u.Email,
                    u.FirstName,
                    u.LastName,
                    u.IsActive,
                    u.CreatedAt,
                    u.UpdatedAt,
                    Roles = u.Roles?.Select(ur => new { ur.Role.Id, ur.Role.Name }).ToList() ?? new List<object>()
                }).ToList(),
                Total = users.Count,
                Skip = req.Skip,
                Take = req.Take
            };

            await SendOkAsync(CreateSuccessResponse(userData, "Users retrieved successfully"), ct);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error getting users for tenant");
            await SendAsync(CreateErrorResponse("Failed to retrieve users", ex.Message), 500, ct);
        }
    }
}

/// <summary>
/// Endpoint for getting a specific user
/// </summary>
[SkipQueueProcessing] // Read-only operation doesn't need queuing
public class GetUserEndpoint : StandardEndpointBase<GetUserRequest, object>
{
    private readonly IUserService _userService;

    public GetUserEndpoint(IUserService userService, ILogger<GetUserEndpoint> logger) : base(logger)
    {
        _userService = userService;
    }

    protected override void ConfigureEndpoint()
    {
        this.ConfigureManagementEndpoint(
            "GET",
            "/api/users/{userId}",
            "Get User",
            "Get details of a specific user including roles and permissions",
            "RequireTenantAccess",
            new[] { "User Management" });
    }

    public override async Task HandleAsync(GetUserRequest req, CancellationToken ct)
    {
        try
        {
            var user = await _userService.GetUserAsync(req.UserId, ct);
            if (user == null)
            {
                await SendAsync(CreateErrorResponse("User not found"), 404, ct);
                return;
            }

            var roles = await _userService.GetUserRolesAsync(req.UserId, null, ct);
            var permissions = await _userService.GetUserPermissionsAsync(req.UserId, null, ct);

            var userData = new
            {
                User = new
                {
                    user.Id,
                    user.TenantId,
                    user.Username,
                    user.Email,
                    user.FirstName,
                    user.LastName,
                    user.IsActive,
                    user.CreatedAt,
                    user.UpdatedAt,
                    user.LastLoginAt
                },
                Roles = roles.Select(r => new { r.Id, r.Name, r.Description }).ToList(),
                Permissions = permissions.Select(p => new { p.Id, p.Resource, p.Action, p.Description }).ToList()
            };

            await SendOkAsync(CreateSuccessResponse(userData, "User retrieved successfully"), ct);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error getting user {UserId}", req.UserId);
            await SendAsync(CreateErrorResponse("Failed to retrieve user", ex.Message), 500, ct);
        }
    }
}

/// <summary>
/// Endpoint for creating a new user
/// </summary>
public class CreateUserEndpoint : QueueFirstEndpointBase<CreateUserRequest, object>
{
    private readonly IUserService _userService;
    private readonly ITenantContext _tenantContext;

    public CreateUserEndpoint(IUserService userService, ITenantContext tenantContext, ILogger<CreateUserEndpoint> logger)
        : base(logger)
    {
        _userService = userService;
        _tenantContext = tenantContext;
    }

    protected override void ConfigureEndpoint()
    {
        this.ConfigureManagementEndpoint(
            "POST",
            "/api/users",
            "Create User",
            "Create a new user in the tenant with role and permission assignment",
            "RequireTenantAdmin",
            new[] { "User Management" });
    }

    public override async Task HandleAsync(CreateUserRequest req, CancellationToken ct)
    {
        try
        {
            var tenantId = req.TenantId ?? _tenantContext.TenantId;
            if (string.IsNullOrEmpty(tenantId))
            {
                await SendAsync(CreateErrorResponse("Tenant ID is required"), 400, ct);
                return;
            }

            var createRequest = new NotifyMaster.Core.Models.CreateUserRequest
            {
                TenantId = tenantId,
                Username = req.Username,
                Email = req.Email,
                FirstName = req.FirstName,
                LastName = req.LastName,
                Password = req.Password,
                CreatedBy = _tenantContext.UserId
            };

            var result = await _userService.CreateUserAsync(createRequest, ct);

            if (!result.IsSuccess)
            {
                await SendAsync(CreateErrorResponse("Failed to create user", result.Message), 400, ct);
                return;
            }

            // Assign roles if provided
            foreach (var roleId in req.RoleIds)
            {
                await _userService.AssignRoleAsync(result.Data!.Id, roleId, tenantId, _tenantContext.UserId, ct);
            }

            // Grant permissions if provided
            foreach (var permissionId in req.PermissionIds)
            {
                await _userService.GrantPermissionAsync(result.Data!.Id, permissionId, tenantId, _tenantContext.UserId, ct);
            }

            var userData = new
            {
                result.Data.Id,
                result.Data.TenantId,
                result.Data.Username,
                result.Data.Email,
                result.Data.FirstName,
                result.Data.LastName,
                result.Data.IsActive,
                result.Data.CreatedAt
            };

            var response = CreateQueuedResponse(userData, "User created successfully");
            await SendCreatedAtAsync<GetUserEndpoint>(new { userId = result.Data.Id }, response, cancellation: ct);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating user");
            await SendAsync(CreateErrorResponse("Failed to create user", ex.Message), 500, ct);
        }
    }
}

/// <summary>
/// Endpoint for updating a user
/// </summary>
public class UpdateUserEndpoint : QueueFirstEndpointBase<UpdateUserRequest, object>
{
    private readonly IUserService _userService;
    private readonly ITenantContext _tenantContext;

    public UpdateUserEndpoint(IUserService userService, ITenantContext tenantContext, ILogger<UpdateUserEndpoint> logger)
        : base(logger)
    {
        _userService = userService;
        _tenantContext = tenantContext;
    }

    protected override void ConfigureEndpoint()
    {
        this.ConfigureManagementEndpoint(
            "PUT",
            "/api/users/{userId}",
            "Update User",
            "Update an existing user's profile information",
            "RequireTenantAdmin",
            new[] { "User Management" });
    }

    public override async Task HandleAsync(UpdateUserRequest req, CancellationToken ct)
    {
        try
        {
            var userId = Route<string>("userId")!;

            var updateRequest = new NotifyMaster.Core.Models.UpdateUserRequest
            {
                Username = req.Username,
                Email = req.Email,
                FirstName = req.FirstName,
                LastName = req.LastName,
                IsActive = req.IsActive,
                UpdatedBy = _tenantContext.UserId
            };

            var result = await _userService.UpdateUserAsync(userId, updateRequest, ct);

            if (!result.IsSuccess)
            {
                if (result.Message?.Contains("not found") == true)
                {
                    await SendAsync(CreateErrorResponse("User not found"), 404, ct);
                    return;
                }
                await SendAsync(CreateErrorResponse("Failed to update user", result.Message), 400, ct);
                return;
            }

            var userData = new
            {
                result.Data!.Id,
                result.Data.TenantId,
                result.Data.Username,
                result.Data.Email,
                result.Data.FirstName,
                result.Data.LastName,
                result.Data.IsActive,
                result.Data.UpdatedAt
            };

            await SendOkAsync(CreateQueuedResponse(userData, "User updated successfully"), ct);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error updating user");
            await SendAsync(CreateErrorResponse("Failed to update user", ex.Message), 500, ct);
        }
    }
}

/// <summary>
/// Endpoint for assigning a role to a user
/// </summary>
public class AssignRoleEndpoint : Endpoint<AssignRoleRequest, object>
{
    private readonly IUserService _userService;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<AssignRoleEndpoint> _logger;

    public AssignRoleEndpoint(IUserService userService, ITenantContext tenantContext, ILogger<AssignRoleEndpoint> logger)
    {
        _userService = userService;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/users/{userId}/roles/{roleId}");
        Policies("RequireTenantAdmin");
        Summary(s =>
        {
            s.Summary = "Assign Role to User";
            s.Description = "Assign a role to a user";
            s.Response(200, "Role assigned successfully");
            s.Response(400, "Invalid request");
            s.Response(401, "Unauthorized");
            s.Response(403, "Forbidden");
            s.Response(404, "User or role not found");
        });
        Tags("User Management");
    }

    public override async Task HandleAsync(AssignRoleRequest req, CancellationToken ct)
    {
        try
        {
            var userId = Route<string>("userId")!;
            var roleId = Route<string>("roleId")!;

            var result = await _userService.AssignRoleAsync(userId, roleId, _tenantContext.TenantId, _tenantContext.UserId, ct);

            if (!result.IsSuccess)
            {
                await SendAsync(new { Success = false, Message = result.Message }, 400, ct);
                return;
            }

            await SendOkAsync(new { Success = true, Message = "Role assigned successfully" }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role {RoleId} to user {UserId}", req.RoleId, req.UserId);
            await SendErrorsAsync(500, ct);
        }
    }
}

/// <summary>
/// Endpoint for removing a role from a user
/// </summary>
public class RemoveRoleEndpoint : Endpoint<AssignRoleRequest, object>
{
    private readonly IUserService _userService;
    private readonly ILogger<RemoveRoleEndpoint> _logger;

    public RemoveRoleEndpoint(IUserService userService, ILogger<RemoveRoleEndpoint> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    public override void Configure()
    {
        Delete("/api/users/{userId}/roles/{roleId}");
        Policies("RequireTenantAdmin");
        Summary(s =>
        {
            s.Summary = "Remove Role from User";
            s.Description = "Remove a role from a user";
            s.Response(200, "Role removed successfully");
            s.Response(400, "Invalid request");
            s.Response(401, "Unauthorized");
            s.Response(403, "Forbidden");
            s.Response(404, "User or role not found");
        });
        Tags("User Management");
    }

    public override async Task HandleAsync(AssignRoleRequest req, CancellationToken ct)
    {
        try
        {
            var userId = Route<string>("userId")!;
            var roleId = Route<string>("roleId")!;

            var result = await _userService.RemoveRoleAsync(userId, roleId, ct);

            if (!result.IsSuccess)
            {
                await SendAsync(new { Success = false, Message = result.Message }, 400, ct);
                return;
            }

            await SendOkAsync(new { Success = true, Message = "Role removed successfully" }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing role {RoleId} from user {UserId}", req.RoleId, req.UserId);
            await SendErrorsAsync(500, ct);
        }
    }
}
