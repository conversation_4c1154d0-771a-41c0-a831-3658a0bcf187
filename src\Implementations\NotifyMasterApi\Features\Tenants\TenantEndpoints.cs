using NotifyMasterApi.Features.Tenants.Commands;
using NotifyMasterApi.Features.Tenants.Queries;

namespace NotifyMasterApi.Features.Tenants;

/// <summary>
/// Request model for creating a tenant
/// </summary>
public class CreateTenantRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Domain { get; set; } = string.Empty;
    public TenantPlan Plan { get; set; } = TenantPlan.Basic;
    public Dictionary<string, object> Settings { get; set; } = new();
    public TenantLimits? CustomLimits { get; set; }
}

/// <summary>
/// Request model for updating a tenant
/// </summary>
public class UpdateTenantRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Domain { get; set; }
    public TenantPlan? Plan { get; set; }
    public TenantStatus? Status { get; set; }
    public Dictionary<string, object>? Settings { get; set; }
    public TenantLimits? Limits { get; set; }
}

/// <summary>
/// Request model for getting tenants with pagination
/// </summary>
public class GetTenantsRequest
{
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = 50;
}

/// <summary>
/// Request model for getting a specific tenant
/// </summary>
public class GetTenantRequest
{
    public string TenantId { get; set; } = string.Empty;
}

/// <summary>
/// Request model for suspending a tenant
/// </summary>
public class SuspendTenantRequest
{
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// Endpoint for getting all tenants
/// </summary>
public class GetTenantsEndpoint : Endpoint<GetTenantsRequest, GetTenantsResult>
{
    private readonly IMediator _mediator;

    public GetTenantsEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Get("/api/tenants");
        Policies("RequireSystemAdmin");
        Summary(s =>
        {
            s.Summary = "Get All Tenants";
            s.Description = "Get a paginated list of all tenants in the system";
            s.Response<GetTenantsResult>(200, "Tenants retrieved successfully");
            s.Response(401, "Unauthorized");
            s.Response(403, "Forbidden - System admin access required");
        });
    }

    public override async Task HandleAsync(GetTenantsRequest req, CancellationToken ct)
    {
        var query = new GetTenantsQuery(req.Skip, req.Take);
        var result = await _mediator.Send(query, ct);
        await SendOkAsync(result, ct);
    }
}

/// <summary>
/// Endpoint for getting a specific tenant
/// </summary>
public class GetTenantEndpoint : Endpoint<GetTenantRequest, GetTenantResult>
{
    private readonly IMediator _mediator;

    public GetTenantEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Get("/api/tenants/{tenantId}");
        Policies("RequireTenantAccess");
        Summary(s =>
        {
            s.Summary = "Get Tenant";
            s.Description = "Get details of a specific tenant";
            s.Response<GetTenantResult>(200, "Tenant retrieved successfully");
            s.Response(401, "Unauthorized");
            s.Response(403, "Forbidden");
            s.Response(404, "Tenant not found");
        });
    }

    public override async Task HandleAsync(GetTenantRequest req, CancellationToken ct)
    {
        var query = new GetTenantQuery(req.TenantId);
        var result = await _mediator.Send(query, ct);
        
        if (!result.Success)
        {
            await SendNotFoundAsync(ct);
            return;
        }
        
        await SendOkAsync(result, ct);
    }
}

/// <summary>
/// Endpoint for creating a new tenant
/// </summary>
public class CreateTenantEndpoint : Endpoint<CreateTenantRequest, CreateTenantResult>
{
    private readonly IMediator _mediator;

    public CreateTenantEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Post("/api/tenants");
        Policies("RequireSystemAdmin");
        Summary(s =>
        {
            s.Summary = "Create Tenant";
            s.Description = "Create a new tenant in the system";
            s.Response<CreateTenantResult>(201, "Tenant created successfully");
            s.Response(400, "Invalid request");
            s.Response(401, "Unauthorized");
            s.Response(403, "Forbidden - System admin access required");
            s.Response(409, "Tenant with domain already exists");
        });
    }

    public override async Task HandleAsync(CreateTenantRequest req, CancellationToken ct)
    {
        var command = new CreateTenantCommand(
            req.Name,
            req.Description,
            req.Domain,
            req.Plan,
            req.Settings,
            req.CustomLimits,
            User?.Identity?.Name);

        var result = await _mediator.Send(command, ct);
        
        if (!result.Success)
        {
            await SendAsync(result, 400, ct);
            return;
        }
        
        await SendCreatedAtAsync<GetTenantEndpoint>(new { tenantId = result.Tenant!.Id }, result, cancellation: ct);
    }
}

/// <summary>
/// Endpoint for updating a tenant
/// </summary>
public class UpdateTenantEndpoint : Endpoint<UpdateTenantRequest, UpdateTenantResult>
{
    private readonly IMediator _mediator;

    public UpdateTenantEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Put("/api/tenants/{tenantId}");
        Policies("RequireTenantAdmin");
        Summary(s =>
        {
            s.Summary = "Update Tenant";
            s.Description = "Update an existing tenant";
            s.Response<UpdateTenantResult>(200, "Tenant updated successfully");
            s.Response(400, "Invalid request");
            s.Response(401, "Unauthorized");
            s.Response(403, "Forbidden");
            s.Response(404, "Tenant not found");
        });
    }

    public override async Task HandleAsync(UpdateTenantRequest req, CancellationToken ct)
    {
        var tenantId = Route<string>("tenantId")!;
        
        var command = new UpdateTenantCommand(
            tenantId,
            req.Name,
            req.Description,
            req.Domain,
            req.Plan,
            req.Status,
            req.Settings,
            req.Limits,
            User?.Identity?.Name);

        var result = await _mediator.Send(command, ct);
        
        if (!result.Success)
        {
            await SendAsync(result, 400, ct);
            return;
        }
        
        await SendOkAsync(result, ct);
    }
}

/// <summary>
/// Endpoint for deleting a tenant
/// </summary>
public class DeleteTenantEndpoint : Endpoint<GetTenantRequest, DeleteTenantResult>
{
    private readonly IMediator _mediator;

    public DeleteTenantEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Delete("/api/tenants/{tenantId}");
        Policies("RequireSystemAdmin");
        Summary(s =>
        {
            s.Summary = "Delete Tenant";
            s.Description = "Delete a tenant from the system";
            s.Response<DeleteTenantResult>(200, "Tenant deleted successfully");
            s.Response(401, "Unauthorized");
            s.Response(403, "Forbidden - System admin access required");
            s.Response(404, "Tenant not found");
        });
    }

    public override async Task HandleAsync(GetTenantRequest req, CancellationToken ct)
    {
        var command = new DeleteTenantCommand(req.TenantId);
        var result = await _mediator.Send(command, ct);
        
        if (!result.Success)
        {
            await SendAsync(result, 400, ct);
            return;
        }
        
        await SendOkAsync(result, ct);
    }
}

/// <summary>
/// Endpoint for suspending a tenant
/// </summary>
public class SuspendTenantEndpoint : Endpoint<SuspendTenantRequest, SuspendTenantResult>
{
    private readonly IMediator _mediator;

    public SuspendTenantEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Post("/api/tenants/{tenantId}/suspend");
        Policies("RequireSystemAdmin");
        Summary(s =>
        {
            s.Summary = "Suspend Tenant";
            s.Description = "Suspend a tenant's access to the system";
            s.Response<SuspendTenantResult>(200, "Tenant suspended successfully");
            s.Response(400, "Invalid request");
            s.Response(401, "Unauthorized");
            s.Response(403, "Forbidden - System admin access required");
            s.Response(404, "Tenant not found");
        });
    }

    public override async Task HandleAsync(SuspendTenantRequest req, CancellationToken ct)
    {
        var tenantId = Route<string>("tenantId")!;
        var command = new SuspendTenantCommand(tenantId, req.Reason);
        var result = await _mediator.Send(command, ct);
        
        if (!result.Success)
        {
            await SendAsync(result, 400, ct);
            return;
        }
        
        await SendOkAsync(result, ct);
    }
}

/// <summary>
/// Endpoint for activating a tenant
/// </summary>
public class ActivateTenantEndpoint : Endpoint<GetTenantRequest, ActivateTenantResult>
{
    private readonly IMediator _mediator;

    public ActivateTenantEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Post("/api/tenants/{tenantId}/activate");
        Policies("RequireSystemAdmin");
        Summary(s =>
        {
            s.Summary = "Activate Tenant";
            s.Description = "Activate a suspended tenant";
            s.Response<ActivateTenantResult>(200, "Tenant activated successfully");
            s.Response(401, "Unauthorized");
            s.Response(403, "Forbidden - System admin access required");
            s.Response(404, "Tenant not found");
        });
    }

    public override async Task HandleAsync(GetTenantRequest req, CancellationToken ct)
    {
        var command = new ActivateTenantCommand(req.TenantId);
        var result = await _mediator.Send(command, ct);
        
        if (!result.Success)
        {
            await SendAsync(result, 400, ct);
            return;
        }
        
        await SendOkAsync(result, ct);
    }
}
