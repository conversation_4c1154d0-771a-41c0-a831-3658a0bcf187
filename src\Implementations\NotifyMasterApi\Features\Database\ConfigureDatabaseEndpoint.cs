namespace NotifyMasterApi.Features.Database;

/// <summary>
/// Endpoint to configure database provider
/// </summary>
public class ConfigureDatabaseEndpoint : Endpoint<ConfigureDatabaseRequest, ConfigureDatabaseResponse>
{
    private readonly IConfiguration _configuration;
    private readonly IWebHostEnvironment _environment;

    public ConfigureDatabaseEndpoint(IConfiguration configuration, IWebHostEnvironment environment)
    {
        _configuration = configuration;
        _environment = environment;
    }

    public override void Configure()
    {
        Post("/api/database/configure");
        AllowAnonymous(); // TODO: Add proper authorization
        Summary(s =>
        {
            s.Summary = "Configure database provider";
            s.Description = "Updates the application configuration with the specified database provider settings";
            s.Responses[200] = "Database configuration updated successfully";
            s.Responses[400] = "Invalid request";
            s.Responses[500] = "Configuration update failed";
        });
        
        Tags("🔧 System Administration");
    }

    public override async Task HandleAsync(ConfigureDatabaseRequest req, CancellationToken ct)
    {
        try
        {
            // Validate the provider
            if (!DatabaseProviderService.IsProviderSupported(req.Provider))
            {
                await SendAsync(new ConfigureDatabaseResponse
                {
                    Success = false,
                    Error = $"Unsupported database provider: {req.Provider}"
                }, 400, ct);
                return;
            }

            // Build connection string
            var connectionString = BuildConnectionString(req);

            // Update configuration file
            var configPath = Path.Combine(_environment.ContentRootPath, "appsettings.json");
            var configJson = await File.ReadAllTextAsync(configPath, ct);
            
            using var document = JsonDocument.Parse(configJson);
            var root = document.RootElement;
            
            // Create updated configuration
            var updatedConfig = new Dictionary<string, object>();
            
            // Copy existing configuration
            foreach (var property in root.EnumerateObject())
            {
                if (property.Name == "ConnectionStrings")
                {
                    // Update connection strings
                    updatedConfig[property.Name] = new Dictionary<string, object>
                    {
                        ["DefaultConnection"] = connectionString,
                        ["Redis"] = GetExistingValue(property.Value, "Redis") ?? "localhost:6379"
                    };
                }
                else if (property.Name == "Database")
                {
                    // Update database configuration
                    updatedConfig[property.Name] = new Dictionary<string, object>
                    {
                        ["Provider"] = req.Provider,
                        ["MigrationsAssembly"] = "NotifyMasterApi"
                    };
                }
                else
                {
                    // Copy other properties as-is
                    updatedConfig[property.Name] = JsonSerializer.Deserialize<object>(property.Value.GetRawText())!;
                }
            }

            // Add Database section if it doesn't exist
            if (!updatedConfig.ContainsKey("Database"))
            {
                updatedConfig["Database"] = new Dictionary<string, object>
                {
                    ["Provider"] = req.Provider,
                    ["MigrationsAssembly"] = "NotifyMasterApi"
                };
            }

            // Write updated configuration
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            
            var updatedJson = JsonSerializer.Serialize(updatedConfig, options);
            await File.WriteAllTextAsync(configPath, updatedJson, ct);

            await SendOkAsync(new ConfigureDatabaseResponse
            {
                Success = true,
                Message = "Database configuration updated successfully. Please restart the application for changes to take effect.",
                Provider = req.Provider,
                ConnectionString = MaskConnectionString(connectionString)
            }, ct);
        }
        catch (Exception ex)
        {
            await SendAsync(new ConfigureDatabaseResponse
            {
                Success = false,
                Error = $"Failed to update configuration: {ex.Message}"
            }, 500, ct);
        }
    }

    private static string BuildConnectionString(ConfigureDatabaseRequest req)
    {
        return req.Provider.ToLower() switch
        {
            "sqlserver" => req.IntegratedSecurity
                ? $"Server={req.Host},{req.Port};Database={req.DatabaseName};Integrated Security=true;TrustServerCertificate=true;"
                : $"Server={req.Host},{req.Port};Database={req.DatabaseName};User Id={req.Username};Password={req.Password};TrustServerCertificate=true;",
            
            "mysql" => $"Server={req.Host};Port={req.Port};Database={req.DatabaseName};Uid={req.Username};Pwd={req.Password};",
            
            "postgresql" => $"Host={req.Host};Port={req.Port};Database={req.DatabaseName};Username={req.Username};Password={req.Password};",
            
            "sqlite" => $"Data Source={req.DatabaseName}.db;",
            
            "inmemory" => "InMemory",
            
            _ => throw new NotSupportedException($"Database provider '{req.Provider}' is not supported")
        };
    }

    private static string? GetExistingValue(JsonElement element, string propertyName)
    {
        if (element.TryGetProperty(propertyName, out var property))
        {
            return property.GetString();
        }
        return null;
    }

    private static string MaskConnectionString(string connectionString)
    {
        // Mask sensitive information in connection string for response
        return connectionString
            .Replace(connectionString.Split("Password=").LastOrDefault()?.Split(";").FirstOrDefault() ?? "", "***")
            .Replace(connectionString.Split("Pwd=").LastOrDefault()?.Split(";").FirstOrDefault() ?? "", "***");
    }
}

/// <summary>
/// Request model for configuring database
/// </summary>
public class ConfigureDatabaseRequest
{
    [Required]
    public string Provider { get; set; } = string.Empty;
    
    public string Host { get; set; } = "localhost";
    
    public int Port { get; set; }
    
    [Required]
    public string DatabaseName { get; set; } = string.Empty;
    
    public string Username { get; set; } = string.Empty;
    
    public string Password { get; set; } = string.Empty;
    
    public bool IntegratedSecurity { get; set; } = false;
}

/// <summary>
/// Response model for database configuration
/// </summary>
public class ConfigureDatabaseResponse
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public string? Error { get; set; }
    public string Provider { get; set; } = string.Empty;
    public string ConnectionString { get; set; } = string.Empty;
}
