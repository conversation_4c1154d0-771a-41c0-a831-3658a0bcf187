namespace NotifyMasterApi.Features.Plugins;

public class UpdatePluginConfigRequest
{
    public string PluginName { get; set; } = string.Empty;
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class UpdatePluginConfigResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string PluginName { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class UpdatePluginConfigEndpoint : Endpoint<UpdatePluginConfigRequest, UpdatePluginConfigResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<UpdatePluginConfigEndpoint> _logger;

    public UpdatePluginConfigEndpoint(IPluginManager pluginManager, ILogger<UpdatePluginConfigEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Put("/api/plugins/{pluginName}/config");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Update plugin configuration";
            s.Description = "Update the configuration settings for a specific plugin";
            s.Responses[200] = "Configuration updated successfully";
            s.Responses[400] = "Invalid configuration";
            s.Responses[404] = "Plugin not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("Plugin Management");
    }

    public override async Task HandleAsync(UpdatePluginConfigRequest req, CancellationToken ct)
    {
        try
        {
            // Check if plugin exists
            var plugin = await _pluginManager.GetPluginAsync(req.PluginName);
            if (plugin == null)
            {
                await SendNotFoundAsync(ct);
                return;
            }

            // Validate configuration first
            var isValid = await _pluginManager.ValidatePluginConfigurationAsync(req.PluginName, req.Configuration);
            if (!isValid)
            {
                await SendAsync(new UpdatePluginConfigResponse
                {
                    Success = false,
                    Message = "Invalid configuration provided",
                    PluginName = req.PluginName
                }, 400, ct);
                return;
            }

            // Update configuration
            var success = await _pluginManager.UpdatePluginConfigurationAsync(req.PluginName, req.Configuration);
            
            if (success)
            {
                _logger.LogInformation("Configuration updated successfully for plugin {PluginName}", req.PluginName);
                
                await SendOkAsync(new UpdatePluginConfigResponse
                {
                    Success = true,
                    Message = $"Configuration updated successfully for plugin '{req.PluginName}'",
                    PluginName = req.PluginName
                }, ct);
            }
            else
            {
                await SendAsync(new UpdatePluginConfigResponse
                {
                    Success = false,
                    Message = "Failed to update plugin configuration",
                    PluginName = req.PluginName
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating configuration for plugin {PluginName}", req.PluginName);
            await SendErrorsAsync(500, ct);
        }
    }
}

public class EnablePluginRequest
{
    public string PluginName { get; set; } = string.Empty;
}

public class EnablePluginResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string PluginName { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class EnablePluginEndpoint : Endpoint<EnablePluginRequest, EnablePluginResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<EnablePluginEndpoint> _logger;

    public EnablePluginEndpoint(IPluginManager pluginManager, ILogger<EnablePluginEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/plugins/{pluginName}/enable");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Enable a plugin";
            s.Description = "Enable a specific plugin to make it available for use";
            s.Responses[200] = "Plugin enabled successfully";
            s.Responses[404] = "Plugin not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("Plugin Management");
    }

    public override async Task HandleAsync(EnablePluginRequest req, CancellationToken ct)
    {
        try
        {
            var success = await _pluginManager.EnablePluginAsync(req.PluginName);
            
            if (success)
            {
                _logger.LogInformation("Plugin {PluginName} enabled successfully", req.PluginName);
                
                await SendOkAsync(new EnablePluginResponse
                {
                    Success = true,
                    Message = $"Plugin '{req.PluginName}' enabled successfully",
                    PluginName = req.PluginName
                }, ct);
            }
            else
            {
                await SendAsync(new EnablePluginResponse
                {
                    Success = false,
                    Message = $"Failed to enable plugin '{req.PluginName}'",
                    PluginName = req.PluginName
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling plugin {PluginName}", req.PluginName);
            await SendErrorsAsync(500, ct);
        }
    }
}

public class DisablePluginEndpoint : Endpoint<EnablePluginRequest, EnablePluginResponse>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<DisablePluginEndpoint> _logger;

    public DisablePluginEndpoint(IPluginManager pluginManager, ILogger<DisablePluginEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/plugins/{pluginName}/disable");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Disable a plugin";
            s.Description = "Disable a specific plugin to make it unavailable for use";
            s.Responses[200] = "Plugin disabled successfully";
            s.Responses[404] = "Plugin not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("Plugin Management");
    }

    public override async Task HandleAsync(EnablePluginRequest req, CancellationToken ct)
    {
        try
        {
            var success = await _pluginManager.DisablePluginAsync(req.PluginName);
            
            if (success)
            {
                _logger.LogInformation("Plugin {PluginName} disabled successfully", req.PluginName);
                
                await SendOkAsync(new EnablePluginResponse
                {
                    Success = true,
                    Message = $"Plugin '{req.PluginName}' disabled successfully",
                    PluginName = req.PluginName
                }, ct);
            }
            else
            {
                await SendAsync(new EnablePluginResponse
                {
                    Success = false,
                    Message = $"Failed to disable plugin '{req.PluginName}'",
                    PluginName = req.PluginName
                }, 400, ct);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling plugin {PluginName}", req.PluginName);
            await SendErrorsAsync(500, ct);
        }
    }
}
