{"sourceFile": "src/Implementations/NotifyMasterApi/Features/SMS/SendSms.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1751230953056, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751230996644, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -61,18 +61,18 @@\n /// </summary>\r\n public class SendSmsResponse\r\n {\r\n     /// <summary>\r\n-    /// Indicates whether the SMS was sent successfully\r\n+    /// Indicates whether the SMS was queued successfully\r\n     /// </summary>\r\n     /// <example>true</example>\r\n     public bool Success { get; set; }\r\n \r\n     /// <summary>\r\n-    /// Unique identifier for the sent message (available when successful)\r\n+    /// Correlation ID for tracking the request\r\n     /// </summary>\r\n-    /// <example>sms_xyz789abc123</example>\r\n-    public string? MessageId { get; set; }\r\n+    /// <example>corr_abc123def456</example>\r\n+    public string? CorrelationId { get; set; }\r\n \r\n     /// <summary>\r\n     /// Error message if the operation failed\r\n     /// </summary>\r\n"}, {"date": 1751231012300, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -79,26 +79,20 @@\n     /// <example>Invalid phone number format</example>\r\n     public string? Error { get; set; }\r\n \r\n     /// <summary>\r\n-    /// Timestamp when the operation was completed\r\n+    /// Timestamp when the request was queued\r\n     /// </summary>\r\n     /// <example>2024-01-15T10:30:00Z</example>\r\n     public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n \r\n     /// <summary>\r\n-    /// SMS provider used for sending\r\n+    /// Status of the request\r\n     /// </summary>\r\n-    /// <example>T<PERSON><PERSON>, Kavenegar, Nexmo</example>\r\n-    public string? Provider { get; set; }\r\n+    /// <example>Queued, Processing, Completed, Failed</example>\r\n+    public string Status { get; set; } = \"Queued\";\r\n \r\n     /// <summary>\r\n-    /// Delivery status information\r\n-    /// </summary>\r\n-    /// <example>queued, sent, delivered, failed</example>\r\n-    public string? DeliveryStatus { get; set; }\r\n-\r\n-    /// <summary>\r\n     /// Cost information for the SMS (if available)\r\n     /// </summary>\r\n     public decimal? Cost { get; set; }\r\n \r\n"}, {"date": 1751231026424, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -91,23 +91,11 @@\n     /// <example>Queued, Processing, Completed, Failed</example>\r\n     public string Status { get; set; } = \"Queued\";\r\n \r\n     /// <summary>\r\n-    /// Cost information for the SMS (if available)\r\n+    /// Additional metadata about the operation\r\n     /// </summary>\r\n-    public decimal? Cost { get; set; }\r\n-\r\n-    /// <summary>\r\n-    /// Currency for the cost (if available)\r\n-    /// </summary>\r\n-    /// <example>USD, EUR, IRR</example>\r\n-    public string? Currency { get; set; }\r\n-\r\n-    /// <summary>\r\n-    /// Number of SMS segments used\r\n-    /// </summary>\r\n-    /// <example>1</example>\r\n-    public int? SegmentCount { get; set; }\r\n+    public Dictionary<string, object>? Metadata { get; set; }\r\n }\r\n \r\n public class SendSmsEndpoint : Endpoint<SendSmsRequest, SendSmsResponse>\r\n {\r\n"}, {"date": 1751231047056, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -98,14 +98,16 @@\n }\r\n \r\n public class SendSmsEndpoint : Endpoint<SendSmsRequest, SendSmsResponse>\r\n {\r\n-    private readonly ISmsGateway _smsGateway;\r\n+    private readonly IQueueService _queueService;\r\n+    private readonly ITenantContext _tenantContext;\r\n     private readonly ILogger<SendSmsEndpoint> _logger;\r\n \r\n-    public SendSmsEndpoint(ISmsGateway smsGateway, ILogger<SendSmsEndpoint> logger)\r\n+    public SendSmsEndpoint(IQueueService queueService, ITenantContext tenantContext, ILogger<SendSmsEndpoint> logger)\r\n     {\r\n-        _smsGateway = smsGateway;\r\n+        _queueService = queueService;\r\n+        _tenantContext = tenantContext;\r\n         _logger = logger;\r\n     }\r\n \r\n     public override void Configure()\r\n"}, {"date": 1751231083158, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -144,44 +144,63 @@\n     public override async Task HandleAsync(SendSmsRequest req, CancellationToken ct)\r\n     {\r\n         try\r\n         {\r\n-            _logger.LogInformation(\"Sending SMS to {PhoneNumber}\", req.PhoneNumber);\r\n+            _logger.LogInformation(\"Queueing SMS to {PhoneNumber} for processing\", req.PhoneNumber);\r\n \r\n-            var smsRequest = new SmsMessageRequest\r\n+            // Create the message for the queue\r\n+            var smsMessage = new SendNotificationMessage\r\n             {\r\n-                PhoneNumber = req.PhoneNumber,\r\n-                Message = req.Message,\r\n+                TenantId = _tenantContext.TenantId ?? \"default\",\r\n+                UserId = HttpContext.User?.Identity?.Name ?? \"anonymous\",\r\n+                PluginName = \"sms\",\r\n+                Recipient = req.PhoneNumber,\r\n+                Content = req.Message,\r\n                 From = req.From,\r\n-                Metadata = req.Metadata\r\n+                Headers = new Dictionary<string, string>(),\r\n+                Metadata = new Dictionary<string, object>\r\n+                {\r\n+                    [\"MessageType\"] = req.MessageType ?? \"transactional\",\r\n+                    [\"OriginalMetadata\"] = req.Metadata ?? new Dictionary<string, object>()\r\n+                },\r\n+                RequestedAt = DateTime.UtcNow\r\n             };\r\n \r\n-            var result = await _smsGateway.SendAsync(smsRequest);\r\n+            // Enqueue the message\r\n+            var result = await _queueService.EnqueueAsync(QueueNames.SmsProcessing, smsMessage, ct);\r\n \r\n             if (result.IsSuccess)\r\n             {\r\n                 await SendOkAsync(new SendSmsResponse\r\n                 {\r\n                     Success = true,\r\n-                    MessageId = result.MessageId\r\n+                    CorrelationId = smsMessage.CorrelationId,\r\n+                    Status = \"Queued\",\r\n+                    Metadata = new Dictionary<string, object>\r\n+                    {\r\n+                        [\"QueueName\"] = QueueNames.SmsProcessing,\r\n+                        [\"JobId\"] = result.Data ?? \"unknown\"\r\n+                    }\r\n                 }, ct);\r\n             }\r\n             else\r\n             {\r\n                 await SendAsync(new SendSmsResponse\r\n                 {\r\n                     Success = false,\r\n-                    Error = result.ErrorMessage\r\n+                    Error = result.Message,\r\n+                    Status = \"Failed\"\r\n                 }, 400, ct);\r\n             }\r\n         }\r\n         catch (Exception ex)\r\n         {\r\n-            _logger.LogError(ex, \"Error sending SMS\");\r\n+            _logger.LogError(ex, \"Error queueing SMS\");\r\n             await SendAsync(new SendSmsResponse\r\n             {\r\n                 Success = false,\r\n-                Error = \"Internal server error\"\r\n+                Error = \"Internal server error\",\r\n+                Status = \"Failed\"\r\n             }, 500, ct);\r\n         }\r\n     }\r\n }\r\n"}, {"date": 1751231233189, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,9 +3,9 @@\n using NotificationContract.Models;\r\n using NotifyMasterApi.Documentation;\r\n using System.ComponentModel.DataAnnotations;\r\n using NotifyMaster.Core.Interfaces;\r\n-using NotifyMaster.Core.Interfaces.Messages;\r\n+using static NotifyMaster.Core.Interfaces.Messages;\r\n \r\n namespace NotifyMasterApi.Features.SMS;\r\n \r\n /// <summary>\r\n"}], "date": 1751230953056, "name": "Commit-0", "content": "using FastEndpoints;\r\nusing NotifyMasterApi.Gateways;\r\nusing NotificationContract.Models;\r\nusing NotifyMasterApi.Documentation;\r\nusing System.ComponentModel.DataAnnotations;\r\nusing NotifyMaster.Core.Interfaces;\r\nusing NotifyMaster.Core.Interfaces.Messages;\r\n\r\nnamespace NotifyMasterApi.Features.SMS;\r\n\r\n/// <summary>\r\n/// Request model for sending an SMS message\r\n/// </summary>\r\npublic class SendSmsRequest\r\n{\r\n    /// <summary>\r\n    /// Recipient phone number in international format (required)\r\n    /// </summary>\r\n    /// <example>+1234567890</example>\r\n    [Required(ErrorMessage = \"Phone number is required\")]\r\n    [Phone(ErrorMessage = \"Invalid phone number format\")]\r\n    [RegularExpression(@\"^\\+[1-9]\\d{1,14}$\", ErrorMessage = \"Phone number must be in international format (+1234567890)\")]\r\n    public string PhoneNumber { get; set; } = string.Empty;\r\n\r\n    /// <summary>\r\n    /// SMS message content (required)\r\n    /// </summary>\r\n    /// <example>Your verification code is: 123456</example>\r\n    [Required(ErrorMessage = \"Message content is required\")]\r\n    [StringLength(1600, MinimumLength = 1, ErrorMessage = \"Message must be between 1 and 1600 characters\")]\r\n    public string Message { get; set; } = string.Empty;\r\n\r\n    /// <summary>\r\n    /// Sender ID or phone number (optional - uses default if not specified)\r\n    /// </summary>\r\n    /// <example>YourService</example>\r\n    [StringLength(15, ErrorMessage = \"Sender ID cannot exceed 15 characters\")]\r\n    public string? From { get; set; }\r\n\r\n    /// <summary>\r\n    /// Additional metadata for tracking and analytics\r\n    /// </summary>\r\n    /// <example>{\"campaign\": \"verification\", \"userId\": \"12345\"}</example>\r\n    public Dictionary<string, object>? Metadata { get; set; }\r\n\r\n    /// <summary>\r\n    /// Message type for provider-specific handling\r\n    /// </summary>\r\n    /// <example>transactional, promotional, otp</example>\r\n    public string? MessageType { get; set; }\r\n\r\n    /// <summary>\r\n    /// Preferred SMS provider (optional - auto-selected if not specified)\r\n    /// </summary>\r\n    /// <example>Twilio, Kavenegar, Nexmo</example>\r\n    public string? PreferredProvider { get; set; }\r\n}\r\n\r\n/// <summary>\r\n/// Response model for SMS sending operation\r\n/// </summary>\r\npublic class SendSmsResponse\r\n{\r\n    /// <summary>\r\n    /// Indicates whether the SMS was sent successfully\r\n    /// </summary>\r\n    /// <example>true</example>\r\n    public bool Success { get; set; }\r\n\r\n    /// <summary>\r\n    /// Unique identifier for the sent message (available when successful)\r\n    /// </summary>\r\n    /// <example>sms_xyz789abc123</example>\r\n    public string? MessageId { get; set; }\r\n\r\n    /// <summary>\r\n    /// Error message if the operation failed\r\n    /// </summary>\r\n    /// <example>Invalid phone number format</example>\r\n    public string? Error { get; set; }\r\n\r\n    /// <summary>\r\n    /// Timestamp when the operation was completed\r\n    /// </summary>\r\n    /// <example>2024-01-15T10:30:00Z</example>\r\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n\r\n    /// <summary>\r\n    /// SMS provider used for sending\r\n    /// </summary>\r\n    /// <example>Twilio, Kavenegar, Nexmo</example>\r\n    public string? Provider { get; set; }\r\n\r\n    /// <summary>\r\n    /// Delivery status information\r\n    /// </summary>\r\n    /// <example>queued, sent, delivered, failed</example>\r\n    public string? DeliveryStatus { get; set; }\r\n\r\n    /// <summary>\r\n    /// Cost information for the SMS (if available)\r\n    /// </summary>\r\n    public decimal? Cost { get; set; }\r\n\r\n    /// <summary>\r\n    /// Currency for the cost (if available)\r\n    /// </summary>\r\n    /// <example>USD, EUR, IRR</example>\r\n    public string? Currency { get; set; }\r\n\r\n    /// <summary>\r\n    /// Number of SMS segments used\r\n    /// </summary>\r\n    /// <example>1</example>\r\n    public int? SegmentCount { get; set; }\r\n}\r\n\r\npublic class SendSmsEndpoint : Endpoint<SendSmsRequest, SendSmsResponse>\r\n{\r\n    private readonly ISmsGateway _smsGateway;\r\n    private readonly ILogger<SendSmsEndpoint> _logger;\r\n\r\n    public SendSmsEndpoint(ISmsGateway smsGateway, ILogger<SendSmsEndpoint> logger)\r\n    {\r\n        _smsGateway = smsGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        this.ConfigureNotificationEndpoint(\r\n            \"POST\",\r\n            \"/api/sms/send\",\r\n            \"Send SMS Message\",\r\n            \"Send an SMS message through the configured SMS providers with intelligent routing and delivery optimization.\\n\\n\" +\r\n            \"## 🎯 Features\\n\" +\r\n            \"- **Multi-Provider Support**: Automatic failover between SMS providers\\n\" +\r\n            \"- **International Delivery**: Support for global SMS delivery\\n\" +\r\n            \"- **Smart Routing**: Optimal provider selection based on destination\\n\" +\r\n            \"- **Delivery Tracking**: Real-time delivery status updates\\n\" +\r\n            \"- **Cost Optimization**: Automatic cost-effective provider selection\\n\\n\" +\r\n            \"## 📋 Provider Support\\n\" +\r\n            \"- Twilio (Global)\\n- Kavenegar (Iran)\\n- Nexmo/Vonage (Global)\\n- Amazon SNS (Global)\\n- Custom SMS Plugins\\n\\n\" +\r\n            \"## 📱 Message Types\\n\" +\r\n            \"- **Transactional**: OTP, verification codes, alerts\\n\" +\r\n            \"- **Promotional**: Marketing messages, offers\\n\" +\r\n            \"- **Informational**: Updates, notifications\\n\\n\" +\r\n            \"## ⚡ Rate Limits\\n\" +\r\n            \"- **Default**: 50 SMS/minute per API key\\n\" +\r\n            \"- **Burst**: Up to 200 SMS in 10 seconds\\n\" +\r\n            \"- **Daily**: 5,000 SMS per day (configurable)\\n\\n\" +\r\n            \"## 🌍 International Support\\n\" +\r\n            \"- 200+ countries supported\\n- Local number support where available\\n\" +\r\n            \"- Compliance with local regulations\\n- Automatic character encoding (GSM 7-bit, UCS-2)\",\r\n            \"SMS\",\r\n            new[] { \"Core Messaging\", \"Mobile Communication\" }\r\n        );\r\n    }\r\n\r\n    public override async Task HandleAsync(SendSmsRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            _logger.LogInformation(\"Sending SMS to {PhoneNumber}\", req.PhoneNumber);\r\n\r\n            var smsRequest = new SmsMessageRequest\r\n            {\r\n                PhoneNumber = req.PhoneNumber,\r\n                Message = req.Message,\r\n                From = req.From,\r\n                Metadata = req.Metadata\r\n            };\r\n\r\n            var result = await _smsGateway.SendAsync(smsRequest);\r\n\r\n            if (result.IsSuccess)\r\n            {\r\n                await SendOkAsync(new SendSmsResponse\r\n                {\r\n                    Success = true,\r\n                    MessageId = result.MessageId\r\n                }, ct);\r\n            }\r\n            else\r\n            {\r\n                await SendAsync(new SendSmsResponse\r\n                {\r\n                    Success = false,\r\n                    Error = result.ErrorMessage\r\n                }, 400, ct);\r\n            }\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error sending SMS\");\r\n            await SendAsync(new SendSmsResponse\r\n            {\r\n                Success = false,\r\n                Error = \"Internal server error\"\r\n            }, 500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class SendBulkSmsRequest\r\n{\r\n    public List<SendSmsRequest> Messages { get; set; } = new();\r\n}\r\n\r\npublic class SendBulkSmsResponse\r\n{\r\n    public bool Success { get; set; }\r\n    public int SuccessCount { get; set; }\r\n    public int FailureCount { get; set; }\r\n    public List<SendSmsResponse> Results { get; set; } = new();\r\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\r\n}\r\n\r\npublic class SendBulkSmsEndpoint : Endpoint<SendBulkSmsRequest, SendBulkSmsResponse>\r\n{\r\n    private readonly ISmsGateway _smsGateway;\r\n    private readonly ILogger<SendBulkSmsEndpoint> _logger;\r\n\r\n    public SendBulkSmsEndpoint(ISmsGateway smsGateway, ILogger<SendBulkSmsEndpoint> logger)\r\n    {\r\n        _smsGateway = smsGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Post(\"/api/sms/send/bulk\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Send bulk SMS\";\r\n            s.Description = \"Send multiple SMS messages through available SMS plugins\";\r\n            s.Responses[200] = \"Bulk SMS processed\";\r\n            s.Responses[400] = \"Invalid request\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"SMS\");\r\n    }\r\n\r\n    public override async Task HandleAsync(SendBulkSmsRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            _logger.LogInformation(\"Sending bulk SMS to {Count} recipients\", req.Messages.Count);\r\n\r\n            var bulkRequest = new BulkSmsRequest\r\n            {\r\n                Messages = req.Messages.Select(m => new SmsMessageRequest\r\n                {\r\n                    PhoneNumber = m.PhoneNumber,\r\n                    Message = m.Message,\r\n                    From = m.From,\r\n                    Metadata = m.Metadata\r\n                }).ToList()\r\n            };\r\n\r\n            var result = await _smsGateway.SendBulkAsync(bulkRequest);\r\n\r\n            var response = new SendBulkSmsResponse\r\n            {\r\n                Success = result.IsSuccess,\r\n                SuccessCount = result.SuccessCount,\r\n                FailureCount = result.FailureCount,\r\n                Results = result.Results?.Select(r => new SendSmsResponse\r\n                {\r\n                    Success = r.IsSuccess,\r\n                    MessageId = r.MessageId,\r\n                    Error = r.ErrorMessage\r\n                }).ToList() ?? new List<SendSmsResponse>()\r\n            };\r\n\r\n            await SendOkAsync(response, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error sending bulk SMS\");\r\n            await SendAsync(new SendBulkSmsResponse\r\n            {\r\n                Success = false,\r\n                FailureCount = req.Messages.Count\r\n            }, 500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetSmsStatusRequest\r\n{\r\n    public string MessageId { get; set; } = string.Empty;\r\n}\r\n\r\npublic class GetSmsStatusEndpoint : Endpoint<GetSmsStatusRequest, object>\r\n{\r\n    private readonly ISmsGateway _smsGateway;\r\n    private readonly ILogger<GetSmsStatusEndpoint> _logger;\r\n\r\n    public GetSmsStatusEndpoint(ISmsGateway smsGateway, ILogger<GetSmsStatusEndpoint> logger)\r\n    {\r\n        _smsGateway = smsGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/sms/status\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get SMS status\";\r\n            s.Description = \"Get the status of an SMS message\";\r\n            s.Responses[200] = \"SMS status retrieved successfully\";\r\n            s.Responses[404] = \"Message not found\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"SMS\");\r\n    }\r\n\r\n    public override async Task HandleAsync(GetSmsStatusRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var status = await _smsGateway.GetMessageStatusAsync(req.MessageId);\r\n            await SendOkAsync(status, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting SMS status for {MessageId}\", req.MessageId);\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetSmsProvidersEndpoint : Endpoint<EmptyRequest, object>\r\n{\r\n    private readonly ISmsGateway _smsGateway;\r\n    private readonly ILogger<GetSmsProvidersEndpoint> _logger;\r\n\r\n    public GetSmsProvidersEndpoint(ISmsGateway smsGateway, ILogger<GetSmsProvidersEndpoint> logger)\r\n    {\r\n        _smsGateway = smsGateway;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/sms/providers\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get SMS providers\";\r\n            s.Description = \"Get list of available SMS providers\";\r\n            s.Responses[200] = \"Providers retrieved successfully\";\r\n            s.Responses[500] = \"Internal server error\";\r\n        });\r\n        Tags(\"SMS\");\r\n    }\r\n\r\n    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var providers = _smsGateway.GetAvailableProviders();\r\n            await SendOkAsync(providers, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error getting SMS providers\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n"}]}