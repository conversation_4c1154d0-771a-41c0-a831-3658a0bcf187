// Using statements are handled by GlobalUsings.cs
namespace PluginCore.Base;

/// <summary>
/// Abstract base class for Push Message gateway plugins.
/// Provides common Push notification-specific functionality, validation, and helper methods.
/// Implements all three gateway interfaces: Message, Admin, and Metrics.
/// </summary>
public abstract class PushGatewayBase : IPushPlugin, IGatewayAdminPluginType, IGatewayMetricsPluginType
{
    protected readonly ILogger? _logger;
    protected readonly Dictionary<string, object> _configuration = new();

    protected PushGatewayBase(ILogger? logger)
    {
        _logger = logger;
    }

    #region IGatewayMessagePluginType Implementation

    public virtual async Task<MessageSendResult> SendMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        try
        {
            var validationResult = await ValidateMessageAsync(payload, cancellationToken);
            if (!validationResult.IsValid)
            {
                return new MessageSendResult(
                    MessageId: Guid.NewGuid().ToString(),
                    Timestamp: DateTimeOffset.UtcNow,
                    Status: "Failed",
                    CorrelationId: payload.CorrelationId
                );
            }

            var normalizedPayload = NormalizePushPayload(payload);
            return await SendPushInternalAsync(normalizedPayload, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send push notification to {Recipient}", payload.Recipient);
            return new MessageSendResult(
                MessageId: Guid.NewGuid().ToString(),
                Timestamp: DateTimeOffset.UtcNow,
                Status: "Failed",
                CorrelationId: payload.CorrelationId
            );
        }
    }

    public virtual async Task<IReadOnlyList<MessageSendResult>> SendBulkMessagesAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken = default)
    {
        var results = new List<MessageSendResult>();
        var validPayloads = new List<MessagePayload>();

        // Validate all payloads first
        foreach (var payload in payloads)
        {
            var validationResult = await ValidateMessageAsync(payload, cancellationToken);
            if (validationResult.IsValid)
            {
                validPayloads.Add(NormalizePushPayload(payload));
            }
            else
            {
                results.Add(new MessageSendResult(
                    MessageId: Guid.NewGuid().ToString(),
                    Timestamp: DateTimeOffset.UtcNow,
                    Status: "Failed",
                    CorrelationId: payload.CorrelationId
                ));
            }
        }

        if (validPayloads.Any())
        {
            var bulkResults = await SendBulkPushInternalAsync(validPayloads, cancellationToken);
            results.AddRange(bulkResults);
        }

        return results.AsReadOnly();
    }

    public virtual async Task<MessageSendResult> SendTemplatedMessageAsync(string templateId, IDictionary<string, string> templateData, Recipient recipient, CancellationToken cancellationToken = default)
    {
        var template = await GetPushTemplateAsync(templateId, cancellationToken);
        if (template == null)
        {
            return new MessageSendResult(
                MessageId: Guid.NewGuid().ToString(),
                Timestamp: DateTimeOffset.UtcNow,
                Status: "Failed"
            );
        }

        var title = ProcessPushTemplate(template.Title, templateData);
        var content = ProcessPushTemplate(template.Body, templateData);
        
        var payload = new MessagePayload
        {
            Recipient = new Recipient { DeviceToken = recipient.DeviceToken, Address = recipient.Address },
            Content = content,
            Subject = title,
            Headers = new Dictionary<string, string>
            {
                ["TemplateId"] = templateId,
                ["Platform"] = template.Platform ?? "all"
            }
        };

        return await SendMessageAsync(payload, cancellationToken);
    }

    public abstract Task<MessageScheduleResult> ScheduleMessageAsync(MessagePayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);

    public abstract Task<OperationResult> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);

    public abstract Task<MessageSendResult> ResendMessageAsync(string originalMessageId, CancellationToken cancellationToken = default);

    public abstract Task<MessageStatusInfo> GetMessageStatusAsync(string messageId, CancellationToken cancellationToken = default);

    public abstract Task<DeliveryReceipt> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);

    public virtual async Task<PluginCore.Models.ValidationResult> ValidateMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default)
    {
        var errors = new List<string>();

        // Validate device token or topic
        var deviceToken = payload.Recipient.DeviceToken ?? payload.Recipient.Address;
        var topic = payload.Recipient.Topic;
        if (!IsValidDeviceToken(deviceToken ?? "") && !IsValidTopic(topic ?? ""))
        {
            errors.Add("Invalid device token or topic format");
        }

        // Validate message content
        if (string.IsNullOrWhiteSpace(payload.Content))
        {
            errors.Add("Push notification body cannot be empty");
        }
        else if (payload.Content.Length > GetMaxBodyLength())
        {
            errors.Add($"Push notification body exceeds maximum length of {GetMaxBodyLength()} characters");
        }

        // Validate title
        if (!string.IsNullOrWhiteSpace(payload.Subject) && payload.Subject.Length > GetMaxTitleLength())
        {
            errors.Add($"Push notification title exceeds maximum length of {GetMaxTitleLength()} characters");
        }

        // Provider-specific validation
        var providerErrors = await ValidatePushSpecificAsync(payload, cancellationToken);
        errors.AddRange(providerErrors);

        return new PluginCore.Models.ValidationResult
        {
            IsValid = !errors.Any(),
            ErrorMessage = errors.Any() ? string.Join("; ", errors) : null
        };
    }

    public abstract Task<RawGatewayResponse> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default);

    public abstract Task<PreparedMessage> PrepareMessageAsync(MessagePayload payload, CancellationToken cancellationToken = default);

    public abstract Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    #endregion

    #region IGatewayAdminPluginType Implementation

    public virtual async Task<IReadOnlyList<GatewayConfiguration>> GetConfigurationAsync(CancellationToken cancellationToken = default)
    {
        var configs = new List<GatewayConfiguration>();
        
        // Add Push-specific configuration items
        configs.Add(new GatewayConfiguration("SupportedPlatforms", "Supported push platforms", GetConfiguration<string>("SupportedPlatforms") ?? "android,ios,web", "Comma-separated list of supported platforms", false, false));
        configs.Add(new GatewayConfiguration("MaxTitleLength", "Maximum title length", GetConfiguration<int>("MaxTitleLength").ToString() ?? "50", "Maximum length for push notification title", false, false, "int"));
        configs.Add(new GatewayConfiguration("MaxBodyLength", "Maximum body length", GetConfiguration<int>("MaxBodyLength").ToString() ?? "240", "Maximum length for push notification body", false, false, "int"));
        configs.Add(new GatewayConfiguration("EnableBadgeCount", "Enable badge count", GetConfiguration<bool>("EnableBadgeCount").ToString() ?? "true", "Enable or disable badge count for notifications", false, false, "bool"));
        configs.Add(new GatewayConfiguration("EnableSound", "Enable notification sound", GetConfiguration<bool>("EnableSound").ToString() ?? "true", "Enable or disable notification sound", false, false, "bool"));
        
        // Add provider-specific configurations
        var providerConfigs = await GetPushProviderConfigurationsAsync(cancellationToken);
        configs.AddRange(providerConfigs);
        
        return configs.AsReadOnly();
    }

    public virtual async Task<OperationResult> UpdateConfigurationAsync(IEnumerable<GatewayConfiguration> settingsToUpdate, CancellationToken cancellationToken = default)
    {
        try
        {
            foreach (var setting in settingsToUpdate)
            {
                switch (setting.Key)
                {
                    case "SupportedPlatforms":
                        if (setting.Value != null)
                            SetConfiguration("SupportedPlatforms", setting.Value);
                        break;
                    case "MaxTitleLength":
                        if (int.TryParse(setting.Value, out var maxTitleLength))
                            SetConfiguration("MaxTitleLength", maxTitleLength);
                        break;
                    case "MaxBodyLength":
                        if (int.TryParse(setting.Value, out var maxBodyLength))
                            SetConfiguration("MaxBodyLength", maxBodyLength);
                        break;
                    case "EnableBadgeCount":
                        if (bool.TryParse(setting.Value, out var enableBadge))
                            SetConfiguration("EnableBadgeCount", enableBadge);
                        break;
                    case "EnableSound":
                        if (bool.TryParse(setting.Value, out var enableSound))
                            SetConfiguration("EnableSound", enableSound);
                        break;
                    default:
                        await UpdatePushProviderConfigurationAsync(setting, cancellationToken);
                        break;
                }
            }

            return new OperationResult(true, "Configuration updated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update Push gateway configuration");
            return new OperationResult(false, "Failed to update configuration", new[] { new ErrorDetail("ConfigurationError", ex.Message) });
        }
    }

    public virtual async Task<OperationResult> TestConfigurationAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Test basic Push functionality
            var isAvailable = await IsAvailableAsync(cancellationToken);
            if (!isAvailable)
            {
                return new OperationResult(false, "Push gateway is not available");
            }

            // Perform Push-specific configuration tests
            var testResult = await TestPushConfigurationAsync(cancellationToken);
            return testResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Push configuration test failed");
            return new OperationResult(false, "Configuration test failed", new[] { new ErrorDetail("TestError", ex.Message) });
        }
    }

    public virtual async Task<PluginManifest> GetManifestAsync(CancellationToken cancellationToken = default)
    {
        return await GetPushManifestAsync(cancellationToken);
    }

    #endregion

    #region IGatewayMetricsPluginType Implementation

    public virtual async Task<GatewayStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default)
    {
        return await GetPushStatusReportAsync(cancellationToken);
    }

    public virtual async Task<IReadOnlyList<DeliveryResult>> GetDeliveryReportsAsync(int maxItems = 100, CancellationToken cancellationToken = default)
    {
        return await GetPushDeliveryReportsAsync(maxItems, cancellationToken);
    }

    public virtual async Task<UsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetPushUsageMetricsAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<GatewayErrorEntry>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetPushErrorLogAsync(from, to, cancellationToken);
    }

    public virtual async Task<PerformanceSnapshot> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default)
    {
        return await GetPushPerformanceSnapshotAsync(resolution, cancellationToken);
    }

    public virtual async Task<SlaReport> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetPushSlaReportAsync(from, to, cancellationToken);
    }

    public virtual async Task<LatencyMetrics> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetPushLatencyMetricsAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<TrafficTrend>> GetTrafficTrendsAsync(string granularity = "daily", CancellationToken cancellationToken = default)
    {
        return await GetPushTrafficTrendsAsync(granularity, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<AnomalyDetectionResult>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetPushAnomalyReportAsync(from, to, cancellationToken);
    }

    public virtual async Task<GeneratedReport> GenerateMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken = default)
    {
        return await GeneratePushMetricsReportAsync(options, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<RetryAttemptInfo>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default)
    {
        return await GetPushRetryHistoryAsync(from, to, cancellationToken);
    }

    public virtual async Task<IReadOnlyList<ConfigurationImpactRecord>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default)
    {
        return await GetPushChangeImpactHistoryAsync(cancellationToken);
    }

    #endregion

    #region Push-Specific Abstract Methods

    /// <summary>
    /// Sends a single push notification using the provider's API.
    /// </summary>
    protected abstract Task<MessageSendResult> SendPushInternalAsync(MessagePayload payload, CancellationToken cancellationToken);

    /// <summary>
    /// Sends multiple push notifications in a single operation.
    /// </summary>
    protected abstract Task<IReadOnlyList<MessageSendResult>> SendBulkPushInternalAsync(IEnumerable<MessagePayload> payloads, CancellationToken cancellationToken);

    /// <summary>
    /// Gets a push notification template by ID.
    /// </summary>
    protected abstract Task<PushTemplate?> GetPushTemplateAsync(string templateId, CancellationToken cancellationToken);

    /// <summary>
    /// Performs provider-specific push notification validation.
    /// </summary>
    protected abstract Task<IEnumerable<string>> ValidatePushSpecificAsync(MessagePayload payload, CancellationToken cancellationToken);

    #endregion

    #region Push-Specific Abstract Methods for Admin

    /// <summary>
    /// Gets push provider-specific configuration items.
    /// </summary>
    protected abstract Task<IEnumerable<GatewayConfiguration>> GetPushProviderConfigurationsAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Updates push provider-specific configuration.
    /// </summary>
    protected abstract Task UpdatePushProviderConfigurationAsync(GatewayConfiguration setting, CancellationToken cancellationToken);

    /// <summary>
    /// Tests push provider-specific configuration.
    /// </summary>
    protected abstract Task<OperationResult> TestPushConfigurationAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Gets the push plugin manifest.
    /// </summary>
    protected abstract Task<PluginManifest> GetPushManifestAsync(CancellationToken cancellationToken);

    #endregion

    #region Push-Specific Abstract Methods for Metrics

    /// <summary>
    /// Gets push-specific status report.
    /// </summary>
    protected abstract Task<GatewayStatusReport> GetPushStatusReportAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Gets push delivery reports.
    /// </summary>
    protected abstract Task<IReadOnlyList<DeliveryResult>> GetPushDeliveryReportsAsync(int maxItems, CancellationToken cancellationToken);

    /// <summary>
    /// Gets push usage metrics.
    /// </summary>
    protected abstract Task<UsageMetrics> GetPushUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets push error logs.
    /// </summary>
    protected abstract Task<IReadOnlyList<GatewayErrorEntry>> GetPushErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets push performance snapshot.
    /// </summary>
    protected abstract Task<PerformanceSnapshot> GetPushPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken);

    /// <summary>
    /// Gets push SLA report.
    /// </summary>
    protected abstract Task<SlaReport> GetPushSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets push latency metrics.
    /// </summary>
    protected abstract Task<LatencyMetrics> GetPushLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets push traffic trends.
    /// </summary>
    protected abstract Task<IReadOnlyList<TrafficTrend>> GetPushTrafficTrendsAsync(string granularity, CancellationToken cancellationToken);

    /// <summary>
    /// Gets push anomaly report.
    /// </summary>
    protected abstract Task<IReadOnlyList<AnomalyDetectionResult>> GetPushAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Generates push metrics report.
    /// </summary>
    protected abstract Task<GeneratedReport> GeneratePushMetricsReportAsync(MetricsReportOptions options, CancellationToken cancellationToken);

    /// <summary>
    /// Gets push retry history.
    /// </summary>
    protected abstract Task<IReadOnlyList<RetryAttemptInfo>> GetPushRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken);

    /// <summary>
    /// Gets push configuration change impact history.
    /// </summary>
    protected abstract Task<IReadOnlyList<ConfigurationImpactRecord>> GetPushChangeImpactHistoryAsync(CancellationToken cancellationToken);

    #endregion

    #region Push-Specific Helper Methods

    /// <summary>
    /// Validates device token format for push notifications.
    /// </summary>
    protected virtual bool IsValidDeviceToken(string deviceToken)
    {
        if (string.IsNullOrWhiteSpace(deviceToken))
            return false;

        // Basic validation: should be a reasonable length and contain valid characters
        return deviceToken.Length >= 10 && deviceToken.Length <= 4096 &&
               deviceToken.All(c => char.IsLetterOrDigit(c) || c == '-' || c == '_' || c == ':');
    }

    /// <summary>
    /// Validates topic format for push notifications.
    /// </summary>
    protected virtual bool IsValidTopic(string topic)
    {
        if (string.IsNullOrWhiteSpace(topic))
            return false;

        // Topic should start with /topics/ for FCM or be a valid topic name
        return topic.StartsWith("/topics/") ||
               (topic.Length >= 3 && topic.Length <= 900 &&
                topic.All(c => char.IsLetterOrDigit(c) || c == '-' || c == '_' || c == '.'));
    }

    /// <summary>
    /// Gets the maximum title length for push notifications (default 50 characters).
    /// </summary>
    protected virtual int GetMaxTitleLength() => GetConfiguration<int>("MaxTitleLength") > 0 ? GetConfiguration<int>("MaxTitleLength") : 50;

    /// <summary>
    /// Gets the maximum body length for push notifications (default 240 characters).
    /// </summary>
    protected virtual int GetMaxBodyLength() => GetConfiguration<int>("MaxBodyLength") > 0 ? GetConfiguration<int>("MaxBodyLength") : 240;

    /// <summary>
    /// Normalizes push payload for consistent processing.
    /// </summary>
    protected virtual MessagePayload NormalizePushPayload(MessagePayload payload)
    {
        var deviceToken = payload.Recipient.DeviceToken ?? payload.Recipient.Address;
        var normalizedTitle = payload.Subject?.Trim() ?? "";
        var normalizedContent = payload.Content.Trim();

        return new MessagePayload
        {
            Recipient = new Recipient
            {
                DeviceToken = deviceToken?.Trim(),
                Address = deviceToken?.Trim(),
                Topic = payload.Recipient.Topic,
                ReceptorName = payload.Recipient.ReceptorName,
                PersonalizationData = payload.Recipient.PersonalizationData
            },
            Content = normalizedContent,
            From = payload.From,
            Subject = normalizedTitle,
            Headers = payload.Headers,
            CorrelationId = payload.CorrelationId,
            Metadata = payload.Metadata,
            PushNotificationData = payload.PushNotificationData
        };
    }

    /// <summary>
    /// Processes push template with data substitution.
    /// </summary>
    protected virtual string ProcessPushTemplate(string template, IDictionary<string, string> templateData)
    {
        var result = template;
        foreach (var kvp in templateData)
        {
            result = result.Replace($"{{{kvp.Key}}}", kvp.Value);
        }
        return result;
    }

    /// <summary>
    /// Gets supported platforms for push notifications.
    /// </summary>
    protected virtual string[] GetSupportedPlatforms()
    {
        var platforms = GetConfiguration<string>("SupportedPlatforms") ?? "android,ios,web";
        return platforms.Split(',', StringSplitOptions.RemoveEmptyEntries)
                       .Select(p => p.Trim().ToLowerInvariant())
                       .ToArray();
    }

    /// <summary>
    /// Checks if badge count is enabled.
    /// </summary>
    protected virtual bool IsBadgeCountEnabled()
    {
        return GetConfiguration<bool>("EnableBadgeCount");
    }

    /// <summary>
    /// Checks if notification sound is enabled.
    /// </summary>
    protected virtual bool IsSoundEnabled()
    {
        return GetConfiguration<bool>("EnableSound");
    }

    /// <summary>
    /// Sets configuration value.
    /// </summary>
    protected virtual void SetConfiguration(string key, object value)
    {
        _configuration[key] = value;
    }

    /// <summary>
    /// Gets configuration value.
    /// </summary>
    protected virtual T? GetConfiguration<T>(string key)
    {
        if (_configuration.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    #endregion
}

/// <summary>
/// Represents a push notification template with title, body, and platform.
/// </summary>
public record PushTemplate(string Title, string Body, string? Platform = null);
