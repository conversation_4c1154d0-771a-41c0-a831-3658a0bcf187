{"sourceFile": "src/Core/NotifyMaster.Core/Interfaces/IQueueProcessor.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1************, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751230734556, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -95,9 +95,9 @@\n \n     /// <summary>\n     /// Message for user operations\n     /// </summary>\n-    public class UserOperationMessage\n+    public class UserOperationMessage : IHasTenantId, IHasCorrelationId\n     {\n         public string Id { get; set; } = Guid.NewGuid().ToString();\n         public string TenantId { get; set; } = string.Empty;\n         public string UserId { get; set; } = string.Empty;\n"}, {"date": 1751230759801, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -170,4 +170,17 @@\n         public DateTime? EndDate { get; set; }\n         public string? CorrelationId { get; set; }\n     }\n }\n+\n+/// <summary>\n+/// Interfaces for message properties\n+/// </summary>\n+public interface IHasTenantId\n+{\n+    string TenantId { get; set; }\n+}\n+\n+public interface IHasCorrelationId\n+{\n+    string? CorrelationId { get; set; }\n+}\n"}, {"date": 1751231953516, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -69,9 +69,11 @@\n \n /// <summary>\n /// Message types for different operations\n /// </summary>\n-namespace Messages\n+namespace NotifyMaster.Core.Interfaces.Messages;\n+\n+public static class MessageTypes\n {\n     /// <summary>\n     /// Message for sending notifications\n     /// </summary>\n"}, {"date": 1751232250140, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -69,10 +69,8 @@\n \n /// <summary>\n /// Message types for different operations\n /// </summary>\n-namespace NotifyMaster.Core.Interfaces.Messages;\n-\n public static class MessageTypes\n {\n     /// <summary>\n     /// Message for sending notifications\n"}], "date": 1************, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Core.Interfaces;\n\n/// <summary>\n/// Interface for processing queued messages\n/// </summary>\npublic interface IQueueProcessor\n{\n    /// <summary>\n    /// Processes a queued message\n    /// </summary>\n    Task ProcessMessageAsync<T>(QueueMessage<T> message, CancellationToken cancellationToken = default) where T : class;\n}\n\n/// <summary>\n/// Interface for message handlers\n/// </summary>\npublic interface IMessageHandler<T> where T : class\n{\n    /// <summary>\n    /// Handles a specific type of message\n    /// </summary>\n    Task<OperationResult> HandleAsync(T message, QueueMessage<T> queueMessage, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets the message type this handler supports\n    /// </summary>\n    Type MessageType { get; }\n    \n    /// <summary>\n    /// Gets the queue name this handler processes\n    /// </summary>\n    string QueueName { get; }\n}\n\n/// <summary>\n/// Base class for message handlers\n/// </summary>\npublic abstract class MessageHandlerBase<T> : IMessageHandler<T> where T : class\n{\n    protected readonly ILogger Logger;\n\n    protected MessageHandlerBase(ILogger logger)\n    {\n        Logger = logger;\n    }\n\n    public abstract Task<OperationResult> HandleAsync(T message, QueueMessage<T> queueMessage, CancellationToken cancellationToken = default);\n    \n    public virtual Type MessageType => typeof(T);\n    \n    public abstract string QueueName { get; }\n\n    protected virtual async Task SaveToDatabase<TEntity>(TEntity entity, CancellationToken cancellationToken = default) where TEntity : class\n    {\n        // Override in derived classes to implement database saving\n        Logger.LogDebug(\"Saving entity of type {EntityType} to database\", typeof(TEntity).Name);\n        await Task.CompletedTask;\n    }\n\n    protected virtual async Task<OperationResult> SendResponse<TResponse>(TResponse response, QueueMessage<T> queueMessage, CancellationToken cancellationToken = default) where TResponse : class\n    {\n        // Override in derived classes to implement response sending\n        Logger.LogDebug(\"Sending response of type {ResponseType} for message {MessageId}\", typeof(TResponse).Name, queueMessage.Id);\n        return OperationResult.Success(\"Response sent successfully\");\n    }\n}\n\n/// <summary>\n/// Message types for different operations\n/// </summary>\nnamespace Messages\n{\n    /// <summary>\n    /// Message for sending notifications\n    /// </summary>\n    public class SendNotificationMessage : IHasTenantId, IHasCorrelationId\n    {\n        public string Id { get; set; } = Guid.NewGuid().ToString();\n        public string TenantId { get; set; } = string.Empty;\n        public string UserId { get; set; } = string.Empty;\n        public string PluginName { get; set; } = string.Empty;\n        public string Recipient { get; set; } = string.Empty;\n        public string Content { get; set; } = string.Empty;\n        public string? Subject { get; set; }\n        public string? From { get; set; }\n        public Dictionary<string, string> Headers { get; set; } = new();\n        public Dictionary<string, object> Metadata { get; set; } = new();\n        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;\n        public DateTime? ScheduledAt { get; set; }\n        public string? CorrelationId { get; set; }\n        public int Priority { get; set; } = 0;\n    }\n\n    /// <summary>\n    /// Message for user operations\n    /// </summary>\n    public class UserOperationMessage\n    {\n        public string Id { get; set; } = Guid.NewGuid().ToString();\n        public string TenantId { get; set; } = string.Empty;\n        public string UserId { get; set; } = string.Empty;\n        public string Operation { get; set; } = string.Empty; // CREATE, UPDATE, DELETE, etc.\n        public Dictionary<string, object> Data { get; set; } = new();\n        public string? RequestedBy { get; set; }\n        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;\n        public string? CorrelationId { get; set; }\n    }\n\n    /// <summary>\n    /// Message for tenant operations\n    /// </summary>\n    public class TenantOperationMessage\n    {\n        public string Id { get; set; } = Guid.NewGuid().ToString();\n        public string TenantId { get; set; } = string.Empty;\n        public string Operation { get; set; } = string.Empty; // CREATE, UPDATE, DELETE, SUSPEND, etc.\n        public Dictionary<string, object> Data { get; set; } = new();\n        public string? RequestedBy { get; set; }\n        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;\n        public string? CorrelationId { get; set; }\n    }\n\n    /// <summary>\n    /// Message for plugin operations\n    /// </summary>\n    public class PluginOperationMessage\n    {\n        public string Id { get; set; } = Guid.NewGuid().ToString();\n        public string TenantId { get; set; } = string.Empty;\n        public string PluginName { get; set; } = string.Empty;\n        public string Operation { get; set; } = string.Empty; // CONFIGURE, ENABLE, DISABLE, etc.\n        public Dictionary<string, object> Configuration { get; set; } = new();\n        public string? RequestedBy { get; set; }\n        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;\n        public string? CorrelationId { get; set; }\n    }\n\n    /// <summary>\n    /// Message for webhook delivery\n    /// </summary>\n    public class WebhookDeliveryMessage\n    {\n        public string Id { get; set; } = Guid.NewGuid().ToString();\n        public string TenantId { get; set; } = string.Empty;\n        public string WebhookUrl { get; set; } = string.Empty;\n        public string Event { get; set; } = string.Empty;\n        public Dictionary<string, object> Payload { get; set; } = new();\n        public Dictionary<string, string> Headers { get; set; } = new();\n        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;\n        public int RetryCount { get; set; } = 0;\n        public int MaxRetries { get; set; } = 3;\n        public string? CorrelationId { get; set; }\n    }\n\n    /// <summary>\n    /// Message for report generation\n    /// </summary>\n    public class ReportGenerationMessage\n    {\n        public string Id { get; set; } = Guid.NewGuid().ToString();\n        public string TenantId { get; set; } = string.Empty;\n        public string UserId { get; set; } = string.Empty;\n        public string ReportType { get; set; } = string.Empty;\n        public Dictionary<string, object> Parameters { get; set; } = new();\n        public string Format { get; set; } = \"PDF\"; // PDF, CSV, XLSX, etc.\n        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;\n        public DateTime? StartDate { get; set; }\n        public DateTime? EndDate { get; set; }\n        public string? CorrelationId { get; set; }\n    }\n}\n"}]}