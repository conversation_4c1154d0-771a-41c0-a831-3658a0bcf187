{"sourceFile": "src/Core/NotifyMaster.Entities/GlobalUsings.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1751237442383, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751238131857, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,5 +9,8 @@\n \n // Entity Framework\n global using Microsoft.EntityFrameworkCore;\n \n+// NotifyMaster.Core namespaces\n+global using NotifyMaster.Core.Models;\n \n+\n"}, {"date": 1751238878821, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,8 +9,7 @@\n \n // Entity Framework\n global using Microsoft.EntityFrameworkCore;\n \n-// NotifyMaster.Core namespaces\n-global using NotifyMaster.Core.Models;\n \n \n+\n"}], "date": 1751237442383, "name": "Commit-0", "content": "// System namespaces\nglobal using System;\nglobal using System.Collections.Generic;\nglobal using System.ComponentModel.DataAnnotations;\nglobal using System.ComponentModel.DataAnnotations.Schema;\nglobal using System.Linq;\nglobal using System.Threading;\nglobal using System.Threading.Tasks;\n\n// Entity Framework\nglobal using Microsoft.EntityFrameworkCore;\n\n\n"}]}