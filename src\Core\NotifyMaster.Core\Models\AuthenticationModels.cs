// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Models;

/// <summary>
/// JWT configuration options
/// </summary>
public class JwtOptions
{
    public string SecretKey { get; set; } = string.Empty;
    public string Issuer { get; set; } = "NotifyMaster";
    public string Audience { get; set; } = "NotifyMaster";
    public int ExpirationMinutes { get; set; } = 60;
    public int RefreshTokenExpirationDays { get; set; } = 7;
}

/// <summary>
/// Authentication result
/// </summary>
public class AuthenticationResult
{
    public User User { get; set; } = null!;
    public string Token { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
    public string RefreshToken { get; set; } = string.Empty;
    public IReadOnlyList<Role> Roles { get; set; } = Array.Empty<Role>();
    public IReadOnlyList<Permission> Permissions { get; set; } = Array.Empty<Permission>();
}

/// <summary>
/// Request model for updating a user
/// </summary>
public class UpdateUserRequest
{
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Email { get; set; }
    public UserStatus? Status { get; set; }
    public Dictionary<string, object>? Profile { get; set; }
    public List<string>? RoleIds { get; set; }
    public string? UpdatedBy { get; set; }
}

/// <summary>
/// Request model for creating a user
/// </summary>
public class CreateUserRequest
{
    public string TenantId { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string Password { get; set; } = string.Empty;
    public UserStatus Status { get; set; } = UserStatus.Active;
    public Dictionary<string, object> Profile { get; set; } = new();
    public List<string> RoleIds { get; set; } = new();
    public string? CreatedBy { get; set; }
}
