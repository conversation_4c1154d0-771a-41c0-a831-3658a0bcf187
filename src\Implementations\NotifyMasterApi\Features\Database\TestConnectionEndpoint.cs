using System.Diagnostics;

namespace NotifyMasterApi.Features.Database;

/// <summary>
/// Endpoint to test database connection
/// </summary>
public class TestConnectionEndpoint : Endpoint<TestConnectionRequest, TestConnectionResponse>
{
    public override void Configure()
    {
        Post("/api/database/test-connection");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Test database connection";
            s.Description = "Tests connectivity to a database with the provided configuration";
            s.Responses[200] = "Connection test result";
            s.Responses[400] = "Invalid request";
        });
        
        Tags("🔧 System Administration");
    }

    public override async Task HandleAsync(TestConnectionRequest req, CancellationToken ct)
    {
        var stopwatch = Stopwatch.StartNew();
        var response = new TestConnectionResponse
        {
            Provider = req.Provider,
            Host = req.Host,
            Port = req.Port,
            DatabaseName = req.DatabaseName
        };

        try
        {
            var connectionString = BuildConnectionString(req);
            var optionsBuilder = new DbContextOptionsBuilder();

            // Configure the appropriate provider
            switch (req.Provider.ToLower())
            {
                case "sqlserver":
                    optionsBuilder.UseSqlServer(connectionString);
                    break;
                case "mysql":
                    optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
                    break;
                case "postgresql":
                    optionsBuilder.UseNpgsql(connectionString);
                    break;
                case "sqlite":
                    optionsBuilder.UseSqlite(connectionString);
                    break;
                case "inmemory":
                    response.Success = true;
                    response.Message = "In-memory database is always available";
                    response.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
                    await SendOkAsync(response, ct);
                    return;
                default:
                    response.Success = false;
                    response.Error = $"Unsupported database provider: {req.Provider}";
                    await SendOkAsync(response, ct);
                    return;
            }

            // Test the connection
            using var context = new DbContext(optionsBuilder.Options);
            await context.Database.CanConnectAsync(ct);

            stopwatch.Stop();
            response.Success = true;
            response.Message = "Connection successful";
            response.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            response.Success = false;
            response.Error = ex.Message;
            response.ResponseTimeMs = stopwatch.ElapsedMilliseconds;
        }

        await SendOkAsync(response, ct);
    }

    private static string BuildConnectionString(TestConnectionRequest req)
    {
        return req.Provider.ToLower() switch
        {
            "sqlserver" => req.IntegratedSecurity
                ? $"Server={req.Host},{req.Port};Database={req.DatabaseName};Integrated Security=true;TrustServerCertificate=true;"
                : $"Server={req.Host},{req.Port};Database={req.DatabaseName};User Id={req.Username};Password={req.Password};TrustServerCertificate=true;",
            
            "mysql" => $"Server={req.Host};Port={req.Port};Database={req.DatabaseName};Uid={req.Username};Pwd={req.Password};",
            
            "postgresql" => $"Host={req.Host};Port={req.Port};Database={req.DatabaseName};Username={req.Username};Password={req.Password};",
            
            "sqlite" => $"Data Source={req.DatabaseName}.db;",
            
            _ => throw new NotSupportedException($"Database provider '{req.Provider}' is not supported")
        };
    }
}

/// <summary>
/// Request model for testing database connection
/// </summary>
public class TestConnectionRequest
{
    [Required]
    public string Provider { get; set; } = string.Empty;
    
    public string Host { get; set; } = "localhost";
    
    public int Port { get; set; }
    
    public string DatabaseName { get; set; } = string.Empty;
    
    public string Username { get; set; } = string.Empty;
    
    public string Password { get; set; } = string.Empty;
    
    public bool IntegratedSecurity { get; set; } = false;
}

/// <summary>
/// Response model for database connection test
/// </summary>
public class TestConnectionResponse
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public string? Error { get; set; }
    public long ResponseTimeMs { get; set; }
    public string Provider { get; set; } = string.Empty;
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; }
    public string DatabaseName { get; set; } = string.Empty;
}
