using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Data.Common;
using System.Diagnostics;
using NotifyMaster.Database;

namespace NotifyMasterApi.Services.Setup;

/// <summary>
/// Service for database operations during setup
/// </summary>
public interface IDatabaseService
{
    Task<DatabaseTestResult> TestConnectionAsync(DatabaseConfiguration config);
    Task<MigrationResult> InitializeSchemaAsync(DatabaseConfiguration config);
    Task<bool> CreateDatabaseIfNotExistsAsync(DatabaseConfiguration config);
    Task<ValidationResult> ValidateConfigurationAsync(DatabaseConfiguration config);
}

/// <summary>
/// Database service implementation
/// </summary>
public class DatabaseService : IDatabaseService
{
    private readonly ILogger<DatabaseService> _logger;
    private readonly IServiceProvider _serviceProvider;

    public DatabaseService(ILogger<DatabaseService> logger, IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task<DatabaseTestResult> TestConnectionAsync(DatabaseConfiguration config)
    {
        var result = new DatabaseTestResult
        {
            Success = false,
            Provider = config.Provider,
            Message = "Connection test failed"
        };
        
        var stopwatch = Stopwatch.StartNew();

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<INotifyMasterDbContext>();
            
            // Test basic connectivity
            await context.Database.OpenConnectionAsync();
            result.Success = true;
            result.Message = "Database connection successful";
            
            // Get database version info
            try
            {
                var versionQuery = GetVersionQuery(config.Provider);
                if (!string.IsNullOrEmpty(versionQuery))
                {
                    var version = await context.Database.ExecuteScalarAsync<string>(versionQuery);
                    result.Version = version;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not retrieve database version");
            }
            
            stopwatch.Stop();
            result.ResponseTime = stopwatch.Elapsed;
            
            _logger.LogInformation("✅ Database connection test successful - {Provider} ({ResponseTime}ms)", 
                config.Provider, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            result.Success = false;
            result.Message = $"Connection failed: {ex.Message}";
            result.Error = ex.Message;
            result.ResponseTime = stopwatch.Elapsed;
            
            _logger.LogError(ex, "❌ Database connection test failed - {Provider}", config.Provider);
        }

        return result;
    }

    public async Task<MigrationResult> InitializeSchemaAsync(DatabaseConfiguration config)
    {
        var result = new MigrationResult
        {
            Success = false,
            Message = "Schema initialization failed"
        };

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<INotifyMasterDbContext>();
            
            _logger.LogInformation("🔧 Initializing database schema...");
            
            // Ensure database is created
            await context.Database.EnsureCreatedAsync();
            
            // Run migrations
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                _logger.LogInformation("📋 Applying {Count} pending migrations...", pendingMigrations.Count());
                await context.Database.MigrateAsync();
                result.MigrationsApplied = pendingMigrations.ToList();
            }
            else
            {
                _logger.LogInformation("✅ Database schema is up to date");
            }
            
            result.Success = true;
            result.Message = "Schema initialization completed successfully";
            
            _logger.LogInformation("✅ Database schema initialized successfully");
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = $"Schema initialization failed: {ex.Message}";
            result.Error = ex.Message;
            
            _logger.LogError(ex, "❌ Database schema initialization failed");
        }

        return result;
    }

    public async Task<bool> CreateDatabaseIfNotExistsAsync(DatabaseConfiguration config)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<INotifyMasterDbContext>();
            
            _logger.LogInformation("🔧 Ensuring database exists...");
            
            var created = await context.Database.EnsureCreatedAsync();
            if (created)
            {
                _logger.LogInformation("✅ Database created successfully");
            }
            else
            {
                _logger.LogInformation("✅ Database already exists");
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to create database");
            return false;
        }
    }

    public async Task<ValidationResult> ValidateConfigurationAsync(DatabaseConfiguration config)
    {
        var result = new ValidationResult { IsValid = true };

        try
        {
            // Validate connection string
            if (string.IsNullOrEmpty(config.ConnectionString))
            {
                result.Errors.Add("Connection string is required");
                result.IsValid = false;
            }

            // Validate provider
            var supportedProviders = new[] { "SqlServer", "PostgreSQL", "MySQL", "SQLite" };
            if (!supportedProviders.Contains(config.Provider))
            {
                result.Errors.Add($"Unsupported database provider: {config.Provider}. Supported providers: {string.Join(", ", supportedProviders)}");
                result.IsValid = false;
            }

            // Test connection if requested
            if (config.TestConnection && result.IsValid)
            {
                var testResult = await TestConnectionAsync(config);
                if (!testResult.Success)
                {
                    result.Errors.Add($"Database connection test failed: {testResult.Message}");
                    result.IsValid = false;
                }
                else
                {
                    result.ValidationData["ConnectionTest"] = "Success";
                    result.ValidationData["DatabaseVersion"] = testResult.Version ?? "Unknown";
                    result.ValidationData["ResponseTime"] = testResult.ResponseTime?.TotalMilliseconds.ToString("F2") + "ms";
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating database configuration");
            result.Errors.Add($"Validation error: {ex.Message}");
            result.IsValid = false;
            return result;
        }
    }

    private string GetVersionQuery(string provider)
    {
        return provider.ToLower() switch
        {
            "sqlserver" => "SELECT @@VERSION",
            "postgresql" => "SELECT version()",
            "mysql" => "SELECT VERSION()",
            "sqlite" => "SELECT sqlite_version()",
            _ => string.Empty
        };
    }
}

/// <summary>
/// Database migration result
/// </summary>
public class MigrationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Error { get; set; }
    public List<string> MigrationsApplied { get; set; } = new();
    public TimeSpan? Duration { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}
