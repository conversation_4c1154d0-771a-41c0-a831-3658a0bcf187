namespace NotifyMasterApi.Features.Plugins;

// ============================================================================
// Load Plugins from Directory
// ============================================================================

public class LoadPluginsRequest
{
    [Required]
    public string PluginDirectory { get; set; } = string.Empty;
}

public class LoadPluginsEndpoint : Endpoint<LoadPluginsRequest, OperationResult>
{
    private readonly IPluginManager _pluginManager;

    public LoadPluginsEndpoint(IPluginManager pluginManager)
    {
        _pluginManager = pluginManager;
    }

    public override void Configure()
    {
        Post("/api/plugins/load-directory");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Load plugins from directory";
            s.Description = "Load all plugins from the specified directory";
            s.Response<OperationResult>(200, "Plugins loaded successfully");
            s.Response(400, "Invalid directory or load failed");
        });
        Tags("🔌 Plugin Management");
    }

    public override async Task HandleAsync(LoadPluginsRequest req, CancellationToken ct)
    {
        var result = await _pluginManager.LoadPluginsAsync(req.PluginDirectory, ct);
        
        if (result.IsSuccess)
        {
            await SendOkAsync(result, ct);
        }
        else
        {
            await SendAsync(result, 400, ct);
        }
    }
}

// ============================================================================
// Load Single Plugin
// ============================================================================

public class LoadPluginRequest
{
    [Required]
    public string PluginPath { get; set; } = string.Empty;
}

public class LoadPluginEndpoint : Endpoint<LoadPluginRequest, OperationResult>
{
    private readonly IPluginManager _pluginManager;

    public LoadPluginEndpoint(IPluginManager pluginManager)
    {
        _pluginManager = pluginManager;
    }

    public override void Configure()
    {
        Post("/api/plugins/load");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Load single plugin";
            s.Description = "Load a specific plugin by file path";
            s.Response<OperationResult>(200, "Plugin loaded successfully");
            s.Response(400, "Invalid path or load failed");
        });
        Tags("🔌 Plugin Management");
    }

    public override async Task HandleAsync(LoadPluginRequest req, CancellationToken ct)
    {
        var result = await _pluginManager.LoadPluginAsync(req.PluginPath, ct);
        
        if (result.IsSuccess)
        {
            await SendOkAsync(result, ct);
        }
        else
        {
            await SendAsync(result, 400, ct);
        }
    }
}

// ============================================================================
// Load Plugin by Name
// ============================================================================

public class LoadPluginByNameRequest
{
    [Required]
    public string PluginName { get; set; } = string.Empty;
    
    [Required]
    public string PluginDirectory { get; set; } = string.Empty;
}

public class LoadPluginByNameEndpoint : Endpoint<LoadPluginByNameRequest, OperationResult>
{
    private readonly IPluginManager _pluginManager;

    public LoadPluginByNameEndpoint(IPluginManager pluginManager)
    {
        _pluginManager = pluginManager;
    }

    public override void Configure()
    {
        Post("/api/plugins/load-by-name");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Load plugin by name";
            s.Description = "Load a plugin by name from the specified directory";
            s.Response<OperationResult>(200, "Plugin loaded successfully");
            s.Response(400, "Plugin not found or load failed");
        });
        Tags("🔌 Plugin Management");
    }

    public override async Task HandleAsync(LoadPluginByNameRequest req, CancellationToken ct)
    {
        var result = await _pluginManager.LoadPluginByNameAsync(req.PluginName, req.PluginDirectory, ct);
        
        if (result.IsSuccess)
        {
            await SendOkAsync(result, ct);
        }
        else
        {
            await SendAsync(result, 400, ct);
        }
    }
}

// ============================================================================
// Get Plugin Manifests
// ============================================================================

public class GetPluginManifestsEndpoint : EndpointWithoutRequest<List<PluginManifest>>
{
    private readonly IPluginManager _pluginManager;

    public GetPluginManifestsEndpoint(IPluginManager pluginManager)
    {
        _pluginManager = pluginManager;
    }

    public override void Configure()
    {
        Get("/api/plugins/manifests");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get plugin manifests";
            s.Description = "Get all loaded plugin manifests with metadata";
            s.Response<List<PluginManifest>>(200, "Plugin manifests retrieved successfully");
        });
        Tags("🔌 Plugin Management");
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var manifests = _pluginManager.GetPluginManifests();
        await SendOkAsync(manifests.ToList(), ct);
    }
}

// ============================================================================
// Get Plugin Manifest by Name
// ============================================================================

public class GetPluginManifestRequest
{
    [Required]
    public string PluginName { get; set; } = string.Empty;
}

public class GetPluginManifestEndpoint : Endpoint<GetPluginManifestRequest, PluginManifest?>
{
    private readonly IPluginManager _pluginManager;

    public GetPluginManifestEndpoint(IPluginManager pluginManager)
    {
        _pluginManager = pluginManager;
    }

    public override void Configure()
    {
        Get("/api/plugins/manifests/{pluginName}");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get plugin manifest by name";
            s.Description = "Get the manifest for a specific plugin";
            s.Response<PluginManifest>(200, "Plugin manifest retrieved successfully");
            s.Response(404, "Plugin not found");
        });
        Tags("🔌 Plugin Management");
    }

    public override async Task HandleAsync(GetPluginManifestRequest req, CancellationToken ct)
    {
        var manifest = _pluginManager.GetPluginManifest(req.PluginName);
        
        if (manifest == null)
        {
            await SendNotFoundAsync(ct);
            return;
        }
        
        await SendOkAsync(manifest, ct);
    }
}

// ============================================================================
// Unload Plugin
// ============================================================================

public class UnloadPluginRequest
{
    [Required]
    public string PluginName { get; set; } = string.Empty;
}

public class UnloadPluginEndpoint : Endpoint<UnloadPluginRequest, OperationResult>
{
    private readonly IPluginManager _pluginManager;

    public UnloadPluginEndpoint(IPluginManager pluginManager)
    {
        _pluginManager = pluginManager;
    }

    public override void Configure()
    {
        Delete("/api/plugins/{pluginName}");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Unload plugin";
            s.Description = "Unload a specific plugin from memory";
            s.Response<OperationResult>(200, "Plugin unloaded successfully");
            s.Response(400, "Plugin not found or unload failed");
        });
        Tags("🔌 Plugin Management");
    }

    public override async Task HandleAsync(UnloadPluginRequest req, CancellationToken ct)
    {
        var result = await _pluginManager.UnloadPluginAsync(req.PluginName, ct);
        
        if (result.IsSuccess)
        {
            await SendOkAsync(result, ct);
        }
        else
        {
            await SendAsync(result, 400, ct);
        }
    }
}

// ============================================================================
// Reload Plugin
// ============================================================================

public class ReloadPluginRequest
{
    [Required]
    public string PluginName { get; set; } = string.Empty;
}

public class ReloadPluginEndpoint : Endpoint<ReloadPluginRequest, OperationResult>
{
    private readonly IPluginManager _pluginManager;

    public ReloadPluginEndpoint(IPluginManager pluginManager)
    {
        _pluginManager = pluginManager;
    }

    public override void Configure()
    {
        Put("/api/plugins/{pluginName}/reload");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Reload plugin";
            s.Description = "Reload a specific plugin (unload and load again)";
            s.Response<OperationResult>(200, "Plugin reloaded successfully");
            s.Response(400, "Plugin not found or reload failed");
        });
        Tags("🔌 Plugin Management");
    }

    public override async Task HandleAsync(ReloadPluginRequest req, CancellationToken ct)
    {
        var result = await _pluginManager.ReloadPluginAsync(req.PluginName, ct);

        if (result.IsSuccess)
        {
            await SendOkAsync(result, ct);
        }
        else
        {
            await SendAsync(result, 400, ct);
        }
    }
}
