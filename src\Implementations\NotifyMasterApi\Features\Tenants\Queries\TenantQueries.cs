namespace NotifyMasterApi.Features.Tenants.Queries;

/// <summary>
/// Query to get all tenants
/// </summary>
public record GetTenantsQuery(int Skip = 0, int Take = 50) : IRequest<GetTenantsResult>;

/// <summary>
/// Query to get a specific tenant
/// </summary>
public record GetTenantQuery(string TenantId) : IRequest<GetTenantResult>;

/// <summary>
/// Query to get tenant by domain
/// </summary>
public record GetTenantByDomainQuery(string Domain) : IRequest<GetTenantResult>;

/// <summary>
/// Query to get tenant plugins
/// </summary>
public record GetTenantPluginsQuery(string TenantId) : IRequest<GetTenantPluginsResult>;

/// <summary>
/// Query to get tenant users
/// </summary>
public record GetTenantUsersQuery(string TenantId, int Skip = 0, int Take = 50) : IRequest<GetTenantUsersResult>;

/// <summary>
/// Result of getting tenants
/// </summary>
public class GetTenantsResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public IReadOnlyList<Tenant> Tenants { get; set; } = Array.Empty<Tenant>();
    public int Total { get; set; }
}

/// <summary>
/// Result of getting a tenant
/// </summary>
public class GetTenantResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public Tenant? Tenant { get; set; }
}

/// <summary>
/// Result of getting tenant plugins
/// </summary>
public class GetTenantPluginsResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public IReadOnlyList<TenantPlugin> Plugins { get; set; } = Array.Empty<TenantPlugin>();
}

/// <summary>
/// Result of getting tenant users
/// </summary>
public class GetTenantUsersResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public IReadOnlyList<User> Users { get; set; } = Array.Empty<User>();
    public int Total { get; set; }
}

/// <summary>
/// Handler for getting tenants
/// </summary>
public class GetTenantsHandler : IRequestHandler<GetTenantsQuery, GetTenantsResult>
{
    private readonly ITenantService _tenantService;
    private readonly ILogger<GetTenantsHandler> _logger;

    public GetTenantsHandler(ITenantService tenantService, ILogger<GetTenantsHandler> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<GetTenantsResult> Handle(GetTenantsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var tenants = await _tenantService.GetTenantsAsync(request.Skip, request.Take, cancellationToken);

            return new GetTenantsResult
            {
                Success = true,
                Tenants = tenants,
                Total = tenants.Count
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenants");
            return new GetTenantsResult
            {
                Success = false,
                Message = "Failed to get tenants"
            };
        }
    }
}

/// <summary>
/// Handler for getting a tenant
/// </summary>
public class GetTenantHandler : IRequestHandler<GetTenantQuery, GetTenantResult>
{
    private readonly ITenantService _tenantService;
    private readonly ILogger<GetTenantHandler> _logger;

    public GetTenantHandler(ITenantService tenantService, ILogger<GetTenantHandler> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<GetTenantResult> Handle(GetTenantQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var tenant = await _tenantService.GetTenantAsync(request.TenantId, cancellationToken);

            if (tenant == null)
            {
                return new GetTenantResult
                {
                    Success = false,
                    Message = "Tenant not found"
                };
            }

            return new GetTenantResult
            {
                Success = true,
                Tenant = tenant
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant {TenantId}", request.TenantId);
            return new GetTenantResult
            {
                Success = false,
                Message = "Failed to get tenant"
            };
        }
    }
}

/// <summary>
/// Handler for getting tenant by domain
/// </summary>
public class GetTenantByDomainHandler : IRequestHandler<GetTenantByDomainQuery, GetTenantResult>
{
    private readonly ITenantService _tenantService;
    private readonly ILogger<GetTenantByDomainHandler> _logger;

    public GetTenantByDomainHandler(ITenantService tenantService, ILogger<GetTenantByDomainHandler> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<GetTenantResult> Handle(GetTenantByDomainQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var tenant = await _tenantService.GetTenantByDomainAsync(request.Domain, cancellationToken);

            if (tenant == null)
            {
                return new GetTenantResult
                {
                    Success = false,
                    Message = "Tenant not found"
                };
            }

            return new GetTenantResult
            {
                Success = true,
                Tenant = tenant
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant by domain {Domain}", request.Domain);
            return new GetTenantResult
            {
                Success = false,
                Message = "Failed to get tenant"
            };
        }
    }
}

/// <summary>
/// Handler for getting tenant plugins
/// </summary>
public class GetTenantPluginsHandler : IRequestHandler<GetTenantPluginsQuery, GetTenantPluginsResult>
{
    private readonly ITenantService _tenantService;
    private readonly ILogger<GetTenantPluginsHandler> _logger;

    public GetTenantPluginsHandler(ITenantService tenantService, ILogger<GetTenantPluginsHandler> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<GetTenantPluginsResult> Handle(GetTenantPluginsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var plugins = await _tenantService.GetTenantPluginsAsync(request.TenantId, cancellationToken);

            return new GetTenantPluginsResult
            {
                Success = true,
                Plugins = plugins
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting plugins for tenant {TenantId}", request.TenantId);
            return new GetTenantPluginsResult
            {
                Success = false,
                Message = "Failed to get tenant plugins"
            };
        }
    }
}

/// <summary>
/// Handler for getting tenant users
/// </summary>
public class GetTenantUsersHandler : IRequestHandler<GetTenantUsersQuery, GetTenantUsersResult>
{
    private readonly IUserService _userService;
    private readonly ILogger<GetTenantUsersHandler> _logger;

    public GetTenantUsersHandler(IUserService userService, ILogger<GetTenantUsersHandler> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    public async Task<GetTenantUsersResult> Handle(GetTenantUsersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var users = await _userService.GetTenantUsersAsync(request.TenantId, request.Skip, request.Take, cancellationToken);

            return new GetTenantUsersResult
            {
                Success = true,
                Users = users,
                Total = users.Count
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users for tenant {TenantId}", request.TenantId);
            return new GetTenantUsersResult
            {
                Success = false,
                Message = "Failed to get tenant users"
            };
        }
    }
}
