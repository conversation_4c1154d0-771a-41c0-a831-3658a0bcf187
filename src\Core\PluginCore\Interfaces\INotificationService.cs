namespace PluginCore.Interfaces;

/// <summary>
/// Core notification service interface for sending notifications across different channels
/// </summary>
public interface INotificationService
{
    /// <summary>
    /// Send a notification request through the appropriate channels
    /// </summary>
    /// <param name="request">The notification request containing message details and target channels</param>
    /// <returns>Task representing the async operation</returns>
    Task SendAsync(SendNotificationRequest request);
}