// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Services;

/// <summary>
/// Implementation of authentication service
/// </summary>
public class AuthenticationService : IAuthenticationService
{
    private readonly ILogger<AuthenticationService> _logger;
    private readonly IUserService _userService;
    private readonly IRoleService _roleService;
    private readonly IOptions<JwtOptions> _jwtOptions;
    private readonly TokenValidationParameters _tokenValidationParameters;

    public AuthenticationService(
        ILogger<AuthenticationService> logger,
        IUserService userService,
        IRoleService roleService,
        IOptions<JwtOptions> jwtOptions)
    {
        _logger = logger;
        _userService = userService;
        _roleService = roleService;
        _jwtOptions = jwtOptions;
        
        _tokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtOptions.Value.SecretKey)),
            ValidateIssuer = true,
            ValidIssuer = _jwtOptions.Value.Issuer,
            ValidateAudience = true,
            ValidAudience = _jwtOptions.Value.Audience,
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };
    }

    public async Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Authenticating user {Email} for tenant {TenantId}", email, tenantId);

            // Get user by email
            var user = await _userService.GetUserByEmailAsync(email, cancellationToken);
            if (user == null)
            {
                _logger.LogWarning("User not found: {Email}", email);
                return OperationResult<AuthenticationResult>.Failure("Invalid credentials");
            }

            // Check if user belongs to the tenant
            if (user.TenantId != tenantId)
            {
                _logger.LogWarning("User {Email} does not belong to tenant {TenantId}", email, tenantId);
                return OperationResult<AuthenticationResult>.Failure("Invalid credentials");
            }

            // Verify password
            if (!VerifyPassword(password, user.PasswordHash))
            {
                _logger.LogWarning("Invalid password for user {Email}", email);
                return OperationResult<AuthenticationResult>.Failure("Invalid credentials");
            }

            // Check user status
            if (user.Status != UserStatus.Active)
            {
                _logger.LogWarning("User {Email} is not active. Status: {Status}", email, user.Status);
                return OperationResult<AuthenticationResult>.Failure("User account is not active");
            }

            // Get user roles and permissions
            var roles = await _userService.GetUserRolesAsync(user.Id, tenantId, cancellationToken);
            var permissions = await _userService.GetUserPermissionsAsync(user.Id, tenantId, cancellationToken);

            // Generate tokens
            var token = await GenerateTokenAsync(user, roles, permissions);
            var refreshToken = GenerateRefreshToken();

            // Update last login time
            await _userService.UpdateUserAsync(user.Id, new UpdateUserRequest(), cancellationToken);

            var result = new AuthenticationResult
            {
                User = user,
                Token = token,
                ExpiresAt = DateTime.UtcNow.AddMinutes(_jwtOptions.Value.ExpirationMinutes),
                RefreshToken = refreshToken,
                Roles = roles,
                Permissions = permissions
            };

            _logger.LogInformation("User {Email} authenticated successfully", email);
            return OperationResult<AuthenticationResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error authenticating user {Email}", email);
            return OperationResult<AuthenticationResult>.Failure("Authentication failed");
        }
    }

    public async Task<OperationResult<AuthenticationResult>> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement refresh token validation and storage
            // For now, return failure
            return OperationResult<AuthenticationResult>.Failure("Refresh token not implemented");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing token");
            return OperationResult<AuthenticationResult>.Failure("Token refresh failed");
        }
    }

    public async Task<OperationResult<ClaimsPrincipal>> ValidateTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var principal = tokenHandler.ValidateToken(token, _tokenValidationParameters, out var validatedToken);
            
            return OperationResult<ClaimsPrincipal>.Success(principal);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token");
            return OperationResult<ClaimsPrincipal>.Failure("Token validation failed");
        }
    }

    public async Task<OperationResult> RevokeTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement refresh token revocation
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking token");
            return OperationResult.Failure("Token revocation failed");
        }
    }

    public async Task<string> GenerateTokenAsync(User user, IReadOnlyList<Role> roles, IReadOnlyList<Permission> permissions)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id),
            new(ClaimTypes.Name, user.Username),
            new(ClaimTypes.Email, user.Email),
            new("tenant_id", user.TenantId),
            new("user_id", user.Id),
            new("first_name", user.FirstName ?? string.Empty),
            new("last_name", user.LastName ?? string.Empty)
        };

        // Add role claims
        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role.Name));
        }

        // Add permission claims
        foreach (var permission in permissions)
        {
            claims.Add(new Claim("permission", $"{permission.Resource}:{permission.Action}"));
        }

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtOptions.Value.SecretKey));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var token = new JwtSecurityToken(
            issuer: _jwtOptions.Value.Issuer,
            audience: _jwtOptions.Value.Audience,
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(_jwtOptions.Value.ExpirationMinutes),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    public string GenerateRefreshToken()
    {
        var randomBytes = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        return Convert.ToBase64String(randomBytes);
    }

    public string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password);
    }

    public bool VerifyPassword(string password, string hash)
    {
        return BCrypt.Net.BCrypt.Verify(password, hash);
    }
}
