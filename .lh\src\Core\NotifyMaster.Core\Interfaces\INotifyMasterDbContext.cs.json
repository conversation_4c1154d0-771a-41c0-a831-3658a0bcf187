{"sourceFile": "src/Core/NotifyMaster.Core/Interfaces/INotifyMasterDbContext.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751238447657, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751238447657, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Database;\n\n/// <summary>\n/// Interface for the NotifyMaster database context\n/// </summary>\npublic interface INotifyMasterDbContext\n{\n    // Core Management\n    DbSet<Tenant> Tenants { get; set; }\n    DbSet<User> Users { get; set; }\n    DbSet<Role> Roles { get; set; }\n    DbSet<Permission> Permissions { get; set; }\n\n    // Junction Tables\n    DbSet<UserRole> UserRoles { get; set; }\n    DbSet<UserPermission> UserPermissions { get; set; }\n    DbSet<RolePermission> RolePermissions { get; set; }\n    DbSet<TenantPlugin> TenantPlugins { get; set; }\n\n    // Standard DbContext methods\n    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);\n    int SaveChanges();\n    \n    // Transaction support\n    Task<Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);\n}\n"}]}