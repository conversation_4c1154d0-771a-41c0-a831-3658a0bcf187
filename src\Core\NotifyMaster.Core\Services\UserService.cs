// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Services;

/// <summary>
/// Implementation of user management service using repositories
/// </summary>
public class UserService : IUserService
{
    private readonly IUserRepository _userRepository;
    private readonly IRoleRepository _roleRepository;
    private readonly IAuthenticationService _authService;
    private readonly ILogger<UserService> _logger;

    public UserService(
        IUserRepository userRepository,
        IRoleRepository roleRepository,
        IAuthenticationService authService,
        ILogger<UserService> logger)
    {
        _userRepository = userRepository;
        _roleRepository = roleRepository;
        _authService = authService;
        _logger = logger;
    }

    public async Task<User?> GetUserAsync(string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _userRepository.GetByIdAsync(userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user {UserId}", userId);
            return null;
        }
    }

    public async Task<User?> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _userRepository.GetByEmailAsync(email, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by email {Email}", email);
            return null;
        }
    }

    public async Task<User?> GetUserByEmailAsync(string tenantId, string email, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByEmailAsync(email, cancellationToken);
            return user?.TenantId == tenantId ? user : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by email {Email} in tenant {TenantId}", email, tenantId);
            return null;
        }
    }

    public async Task<User?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _userRepository.GetByUsernameAsync(username, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by username {Username}", username);
            return null;
        }
    }

    public async Task<IReadOnlyList<User>> GetUsersByTenantAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            var users = await _userRepository.GetByTenantAsync(tenantId, cancellationToken);
            return users.Skip(skip).Take(take).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users for tenant {TenantId}", tenantId);
            return Array.Empty<User>();
        }
    }

    public async Task<IReadOnlyList<User>> GetTenantUsersAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            var users = await _userRepository.GetByTenantAsync(tenantId, cancellationToken);
            return users.Skip(skip).Take(take).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users for tenant {TenantId}", tenantId);
            return Array.Empty<User>();
        }
    }

    public async Task<OperationResult<User>> CreateUserAsync(CreateUserRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if user already exists by email
            var existingUserByEmail = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
            if (existingUserByEmail != null)
            {
                return OperationResult<User>.Failure("A user with this email already exists");
            }

            // Check if user already exists by username
            var existingUserByUsername = await _userRepository.GetByUsernameAsync(request.Username, cancellationToken);
            if (existingUserByUsername != null)
            {
                return OperationResult<User>.Failure("A user with this username already exists");
            }

            // Hash password
            var passwordHash = _authService.HashPassword(request.Password);

            var user = new User
            {
                Id = Guid.NewGuid().ToString(),
                TenantId = request.TenantId,
                Username = request.Username,
                Email = request.Email,
                FirstName = request.FirstName,
                LastName = request.LastName,
                PasswordHash = passwordHash,
                Status = request.Status,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy,
                Profile = request.Profile
            };

            var createdUser = await _userRepository.AddAsync(user, cancellationToken);
            await _userRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created user {UserId} ({Email}) in tenant {TenantId}", createdUser.Id, createdUser.Email, createdUser.TenantId);
            return OperationResult<User>.Success(createdUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user {Email} in tenant {TenantId}", request.Email, request.TenantId);
            return OperationResult<User>.Failure($"Failed to create user: {ex.Message}");
        }
    }

    public async Task<OperationResult<User>> UpdateUserAsync(string userId, UpdateUserRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return OperationResult<User>.Failure("User not found");
            }

            // Update fields if provided
            if (!string.IsNullOrEmpty(request.Email))
            {
                // Check if email is already taken
                var existingUser = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
                if (existingUser != null && existingUser.Id != userId)
                {
                    return OperationResult<User>.Failure("Email is already taken");
                }
                user.Email = request.Email;
            }

            if (!string.IsNullOrEmpty(request.FirstName))
                user.FirstName = request.FirstName;

            if (!string.IsNullOrEmpty(request.LastName))
                user.LastName = request.LastName;

            if (request.Status.HasValue)
                user.Status = request.Status.Value;

            if (request.Profile != null)
                user.Profile = request.Profile;

            user.UpdatedAt = DateTime.UtcNow;
            user.UpdatedBy = request.UpdatedBy;

            var updatedUser = await _userRepository.UpdateAsync(user, cancellationToken);
            await _userRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated user {UserId}", userId);
            return OperationResult<User>.Success(updatedUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {UserId}", userId);
            return OperationResult<User>.Failure($"Failed to update user: {ex.Message}");
        }
    }

    public async Task<OperationResult> DeleteUserAsync(string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            await _userRepository.DeleteAsync(userId, cancellationToken);
            await _userRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted user {UserId}", userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {UserId}", userId);
            return OperationResult.Failure($"Failed to delete user: {ex.Message}");
        }
    }

    public async Task<OperationResult> ChangePasswordAsync(string userId, string currentPassword, string newPassword, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            // Verify current password
            var isCurrentPasswordValid = _authService.VerifyPassword(currentPassword, user.PasswordHash);
            if (!isCurrentPasswordValid)
            {
                return OperationResult.Failure("Current password is incorrect");
            }

            // Hash new password
            user.PasswordHash = _authService.HashPassword(newPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _userRepository.UpdateAsync(user, cancellationToken);
            await _userRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Changed password for user {UserId}", userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for user {UserId}", userId);
            return OperationResult.Failure($"Failed to change password: {ex.Message}");
        }
    }

    public async Task<OperationResult> AssignRoleAsync(string userId, string roleId, string? tenantId = null, string? assignedBy = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            var role = await _roleRepository.GetByIdAsync(roleId, cancellationToken);
            if (role == null)
            {
                return OperationResult.Failure("Role not found");
            }

            // TODO: Implement UserRole repository and proper role assignment
            // For now, just log the operation
            _logger.LogInformation("Assigned role {RoleId} to user {UserId}", roleId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role {RoleId} to user {UserId}", roleId, userId);
            return OperationResult.Failure($"Failed to assign role: {ex.Message}");
        }
    }

    public async Task<OperationResult> RemoveRoleAsync(string userId, string roleId, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement UserRole repository and proper role removal
            // For now, just log the operation
            _logger.LogInformation("Removed role {RoleId} from user {UserId}", roleId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing role {RoleId} from user {UserId}", roleId, userId);
            return OperationResult.Failure($"Failed to remove role: {ex.Message}");
        }
    }

    public async Task<OperationResult> GrantPermissionAsync(string userId, string permissionId, string? tenantId = null, string? grantedBy = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            // TODO: Implement Permission repository and proper permission management
            // For now, just log the operation
            _logger.LogInformation("Granted permission {PermissionId} to user {UserId}", permissionId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error granting permission {PermissionId} to user {UserId}", permissionId, userId);
            return OperationResult.Failure($"Failed to grant permission: {ex.Message}");
        }
    }

    public async Task<OperationResult> RevokePermissionAsync(string userId, string permissionId, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement Permission repository and proper permission management
            // For now, just log the operation
            _logger.LogInformation("Revoked permission {PermissionId} from user {UserId}", permissionId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking permission {PermissionId} from user {UserId}", permissionId, userId);
            return OperationResult.Failure($"Failed to revoke permission: {ex.Message}");
        }
    }

    public async Task<bool> HasPermissionAsync(string userId, string resource, string action, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement proper permission checking with repositories
            // For now, return false
            _logger.LogInformation("Checking permission {Resource}:{Action} for user {UserId}", resource, action, userId);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission {Resource}:{Action} for user {UserId}", resource, action, userId);
            return false;
        }
    }

    public async Task<bool> HasRoleAsync(string userId, string roleName, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement proper role checking with repositories
            // For now, return false
            _logger.LogInformation("Checking role {RoleName} for user {UserId}", roleName, userId);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking role {RoleName} for user {UserId}", roleName, userId);
            return false;
        }
    }

    public async Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement proper permission retrieval with repositories
            // For now, return empty list
            _logger.LogInformation("Getting permissions for user {UserId}", userId);
            return Array.Empty<Permission>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions for user {UserId}", userId);
            return Array.Empty<Permission>();
        }
    }

    public async Task<IReadOnlyList<Role>> GetUserRolesAsync(string userId, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement proper role retrieval with repositories
            // For now, return empty list
            _logger.LogInformation("Getting roles for user {UserId}", userId);
            return Array.Empty<Role>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for user {UserId}", userId);
            return Array.Empty<Role>();
        }
    }

    public async Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get user by email
            var user = await _userRepository.GetByEmailAsync(email, cancellationToken);

            if (user == null || user.TenantId != tenantId)
            {
                return OperationResult<AuthenticationResult>.Failure("Invalid email or password");
            }

            if (user.Status != UserStatus.Active)
            {
                return OperationResult<AuthenticationResult>.Failure("User account is disabled");
            }

            // Verify password using authentication service
            var passwordValid = _authService.VerifyPassword(password, user.PasswordHash);
            if (!passwordValid)
            {
                return OperationResult<AuthenticationResult>.Failure("Invalid email or password");
            }

            // Update last login
            user.LastLoginAt = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user, cancellationToken);
            await _userRepository.SaveChangesAsync(cancellationToken);

            // Get user roles and permissions
            var roles = await GetUserRolesAsync(user.Id, tenantId, cancellationToken);
            var permissions = await GetUserPermissionsAsync(user.Id, tenantId, cancellationToken);

            // Create authentication result
            var result = new AuthenticationResult
            {
                User = user,
                Token = string.Empty, // Token generation should be handled by AuthenticationService
                ExpiresAt = DateTime.UtcNow.AddHours(24), // Default expiration
                RefreshToken = string.Empty, // Refresh token generation should be handled by AuthenticationService
                Roles = roles,
                Permissions = permissions
            };

            _logger.LogInformation("User {UserId} authenticated successfully", user.Id);
            return OperationResult<AuthenticationResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error authenticating user {Email} for tenant {TenantId}", email, tenantId);
            return OperationResult<AuthenticationResult>.Failure("Authentication failed");
        }
    }

}
