// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Services;

/// <summary>
/// Implementation of user management service using repositories
/// </summary>
public class UserService : IUserService
{
    private readonly IUserRepository _userRepository;
    private readonly IAuthenticationService _authService;
    private readonly ILogger<UserService> _logger;

    public UserService(
        IUserRepository userRepository,
        IAuthenticationService authService,
        ILogger<UserService> logger)
    {
        _userRepository = userRepository;
        _authService = authService;
        _logger = logger;
    }

    public async Task<User?> GetUserAsync(string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Users
                .Include(u => u.Tenant)
                .Include(u => u.Roles).ThenInclude(ur => ur.Role)
                .Include(u => u.Permissions).ThenInclude(up => up.Permission)
                .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user {UserId}", userId);
            return null;
        }
    }

    public async Task<User?> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Users
                .Include(u => u.Tenant)
                .Include(u => u.Roles).ThenInclude(ur => ur.Role)
                .Include(u => u.Permissions).ThenInclude(up => up.Permission)
                .FirstOrDefaultAsync(u => u.Email == email, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by email {Email}", email);
            return null;
        }
    }

    public async Task<User?> GetUserByEmailAsync(string tenantId, string email, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Users
                .Include(u => u.Tenant)
                .Include(u => u.Roles).ThenInclude(ur => ur.Role)
                .Include(u => u.Permissions).ThenInclude(up => up.Permission)
                .FirstOrDefaultAsync(u => u.TenantId == tenantId && u.Email == email, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by email {Email} in tenant {TenantId}", email, tenantId);
            return null;
        }
    }

    public async Task<User?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Users
                .Include(u => u.Tenant)
                .Include(u => u.Roles).ThenInclude(ur => ur.Role)
                .Include(u => u.Permissions).ThenInclude(up => up.Permission)
                .FirstOrDefaultAsync(u => u.Username == username, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by username {Username}", username);
            return null;
        }
    }

    public async Task<IReadOnlyList<User>> GetUsersByTenantAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Users
                .Where(u => u.TenantId == tenantId)
                .Include(u => u.Roles).ThenInclude(ur => ur.Role)
                .OrderBy(u => u.Username)
                .Skip(skip)
                .Take(take)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users for tenant {TenantId}", tenantId);
            return Array.Empty<User>();
        }
    }

    public async Task<IReadOnlyList<User>> GetTenantUsersAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Users
                .Where(u => u.TenantId == tenantId)
                .Include(u => u.Roles).ThenInclude(ur => ur.Role)
                .OrderBy(u => u.Username)
                .Skip(skip)
                .Take(take)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users for tenant {TenantId}", tenantId);
            return Array.Empty<User>();
        }
    }

    public async Task<OperationResult<User>> CreateUserAsync(CreateUserRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if user already exists
            var existingUser = await _context.Users
                .FirstOrDefaultAsync(u => u.TenantId == request.TenantId && 
                                         (u.Email == request.Email || u.Username == request.Username), cancellationToken);

            if (existingUser != null)
            {
                return OperationResult<User>.Failure("A user with this email or username already exists");
            }

            // Hash password
            var passwordHash = await _authService.HashPasswordAsync(request.Password);

            var user = new User
            {
                Id = Guid.NewGuid().ToString(),
                TenantId = request.TenantId,
                Username = request.Username,
                Email = request.Email,
                FirstName = request.FirstName,
                LastName = request.LastName,
                PasswordHash = passwordHash,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created user {UserId} ({Email}) in tenant {TenantId}", user.Id, user.Email, user.TenantId);
            return OperationResult<User>.Success(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user {Email} in tenant {TenantId}", request.Email, request.TenantId);
            return OperationResult<User>.Failure($"Failed to create user: {ex.Message}");
        }
    }

    public async Task<OperationResult<User>> UpdateUserAsync(string userId, UpdateUserRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return OperationResult<User>.Failure("User not found");
            }

            // Update fields if provided
            if (!string.IsNullOrEmpty(request.Username))
            {
                // Check if username is already taken
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Id != userId && u.TenantId == user.TenantId && u.Username == request.Username, cancellationToken);
                if (existingUser != null)
                {
                    return OperationResult<User>.Failure("Username is already taken");
                }
                user.Username = request.Username;
            }

            if (!string.IsNullOrEmpty(request.Email))
            {
                // Check if email is already taken
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Id != userId && u.TenantId == user.TenantId && u.Email == request.Email, cancellationToken);
                if (existingUser != null)
                {
                    return OperationResult<User>.Failure("Email is already taken");
                }
                user.Email = request.Email;
            }

            if (!string.IsNullOrEmpty(request.FirstName))
                user.FirstName = request.FirstName;

            if (!string.IsNullOrEmpty(request.LastName))
                user.LastName = request.LastName;

            if (request.IsActive.HasValue)
                user.IsActive = request.IsActive.Value;

            user.UpdatedAt = DateTime.UtcNow;
            user.UpdatedBy = request.UpdatedBy;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated user {UserId}", userId);
            return OperationResult<User>.Success(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {UserId}", userId);
            return OperationResult<User>.Failure($"Failed to update user: {ex.Message}");
        }
    }

    public async Task<OperationResult> DeleteUserAsync(string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            _context.Users.Remove(user);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted user {UserId}", userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {UserId}", userId);
            return OperationResult.Failure($"Failed to delete user: {ex.Message}");
        }
    }

    public async Task<OperationResult> ChangePasswordAsync(string userId, string currentPassword, string newPassword, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            // Verify current password
            var isCurrentPasswordValid = await _authService.VerifyPasswordAsync(currentPassword, user.PasswordHash);
            if (!isCurrentPasswordValid)
            {
                return OperationResult.Failure("Current password is incorrect");
            }

            // Hash new password
            user.PasswordHash = await _authService.HashPasswordAsync(newPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Changed password for user {UserId}", userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password for user {UserId}", userId);
            return OperationResult.Failure($"Failed to change password: {ex.Message}");
        }
    }

    public async Task<OperationResult> AssignRoleAsync(string userId, string roleId, string? assignedBy = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            var role = await _context.Roles.FindAsync(roleId);
            if (role == null)
            {
                return OperationResult.Failure("Role not found");
            }

            var existingUserRole = await _context.UserRoles
                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId, cancellationToken);

            if (existingUserRole != null)
            {
                return OperationResult.Failure("User already has this role");
            }

            _context.UserRoles.Add(new UserRole
            {
                UserId = userId,
                RoleId = roleId,
                TenantId = user.TenantId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = assignedBy
            });

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Assigned role {RoleId} to user {UserId}", roleId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role {RoleId} to user {UserId}", roleId, userId);
            return OperationResult.Failure($"Failed to assign role: {ex.Message}");
        }
    }

    public async Task<OperationResult> RemoveRoleAsync(string userId, string roleId, CancellationToken cancellationToken = default)
    {
        try
        {
            var userRole = await _context.UserRoles
                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId, cancellationToken);

            if (userRole == null)
            {
                return OperationResult.Failure("User does not have this role");
            }

            _context.UserRoles.Remove(userRole);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Removed role {RoleId} from user {UserId}", roleId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing role {RoleId} from user {UserId}", roleId, userId);
            return OperationResult.Failure($"Failed to remove role: {ex.Message}");
        }
    }

    public async Task<OperationResult> GrantPermissionAsync(string userId, string permissionId, string? grantedBy = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return OperationResult.Failure("User not found");
            }

            var permission = await _context.Permissions.FindAsync(permissionId);
            if (permission == null)
            {
                return OperationResult.Failure("Permission not found");
            }

            var existingUserPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId, cancellationToken);

            if (existingUserPermission != null)
            {
                return OperationResult.Failure("User already has this permission");
            }

            _context.UserPermissions.Add(new UserPermission
            {
                UserId = userId,
                PermissionId = permissionId,
                TenantId = user.TenantId,
                GrantedAt = DateTime.UtcNow,
                GrantedBy = grantedBy
            });

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Granted permission {PermissionId} to user {UserId}", permissionId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error granting permission {PermissionId} to user {UserId}", permissionId, userId);
            return OperationResult.Failure($"Failed to grant permission: {ex.Message}");
        }
    }

    public async Task<OperationResult> RevokePermissionAsync(string userId, string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var userPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.PermissionId == permissionId, cancellationToken);

            if (userPermission == null)
            {
                return OperationResult.Failure("User does not have this permission");
            }

            _context.UserPermissions.Remove(userPermission);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Revoked permission {PermissionId} from user {UserId}", permissionId, userId);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking permission {PermissionId} from user {UserId}", permissionId, userId);
            return OperationResult.Failure($"Failed to revoke permission: {ex.Message}");
        }
    }

    public async Task<bool> HasPermissionAsync(string userId, string resource, string action, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check direct user permissions
            var hasDirectPermission = await _context.UserPermissions
                .Include(up => up.Permission)
                .AnyAsync(up => up.UserId == userId &&
                               up.Permission.Resource == resource &&
                               up.Permission.Action == action, cancellationToken);

            if (hasDirectPermission) return true;

            // Check role-based permissions
            var hasRolePermission = await _context.UserRoles
                .Include(ur => ur.Role)
                .ThenInclude(r => r.Permissions)
                .ThenInclude(rp => rp.Permission)
                .AnyAsync(ur => ur.UserId == userId &&
                               ur.Role.Permissions.Any(rp => rp.Permission.Resource == resource &&
                                                             rp.Permission.Action == action), cancellationToken);

            return hasRolePermission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission {Resource}:{Action} for user {UserId}", resource, action, userId);
            return false;
        }
    }

    public async Task<bool> HasRoleAsync(string userId, string roleName, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.UserRoles
                .Include(ur => ur.Role)
                .AnyAsync(ur => ur.UserId == userId && ur.Role.Name == roleName, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking role {RoleName} for user {UserId}", roleName, userId);
            return false;
        }
    }

    public async Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = new List<Permission>();

            // Get direct user permissions
            var directPermissions = await _context.UserPermissions
                .Include(up => up.Permission)
                .Where(up => up.UserId == userId)
                .Select(up => up.Permission)
                .ToListAsync(cancellationToken);

            permissions.AddRange(directPermissions);

            // Get role-based permissions
            var rolePermissions = await _context.UserRoles
                .Include(ur => ur.Role)
                .ThenInclude(r => r.Permissions)
                .ThenInclude(rp => rp.Permission)
                .Where(ur => ur.UserId == userId)
                .SelectMany(ur => ur.Role.Permissions.Select(rp => rp.Permission))
                .ToListAsync(cancellationToken);

            permissions.AddRange(rolePermissions);

            // Remove duplicates
            return permissions.DistinctBy(p => p.Id).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions for user {UserId}", userId);
            return Array.Empty<Permission>();
        }
    }

    public async Task<IReadOnlyList<Role>> GetUserRolesAsync(string userId, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.UserRoles
                .Include(ur => ur.Role)
                .Where(ur => ur.UserId == userId)
                .Select(ur => ur.Role)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for user {UserId}", userId);
            return Array.Empty<Role>();
        }
    }

    public async Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get user by email and tenant
            var user = await _context.Users
                .Include(u => u.Tenant)
                .Include(u => u.Roles).ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.Email == email && u.TenantId == tenantId, cancellationToken);

            if (user == null)
            {
                return OperationResult<AuthenticationResult>.Failure("Invalid email or password");
            }

            if (!user.IsActive)
            {
                return OperationResult<AuthenticationResult>.Failure("User account is disabled");
            }

            // Verify password using authentication service
            var passwordValid = _authService.VerifyPassword(password, user.PasswordHash);
            if (!passwordValid)
            {
                return OperationResult<AuthenticationResult>.Failure("Invalid email or password");
            }

            // Update last login
            user.LastLoginAt = DateTime.UtcNow;
            await _context.SaveChangesAsync(cancellationToken);

            // Get user roles and permissions
            var roles = user.Roles.Select(ur => ur.Role).ToList();
            var permissions = await GetUserPermissionsAsync(user.Id, tenantId, cancellationToken);

            // Create authentication result
            var result = new AuthenticationResult
            {
                User = user,
                Token = string.Empty, // Token generation should be handled by AuthenticationService
                ExpiresAt = DateTime.UtcNow.AddHours(24), // Default expiration
                RefreshToken = string.Empty, // Refresh token generation should be handled by AuthenticationService
                Roles = roles,
                Permissions = permissions
            };

            _logger.LogInformation("User {UserId} authenticated successfully", user.Id);
            return OperationResult<AuthenticationResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error authenticating user {Email} for tenant {TenantId}", email, tenantId);
            return OperationResult<AuthenticationResult>.Failure("Authentication failed");
        }
    }

    // Interface-compliant overloads with tenantId parameter
    public async Task<OperationResult> AssignRoleAsync(string userId, string roleId, string? tenantId = null, string? assignedBy = null, CancellationToken cancellationToken = default)
    {
        return await AssignRoleAsync(userId, roleId, assignedBy, cancellationToken);
    }

    public async Task<OperationResult> RemoveRoleAsync(string userId, string roleId, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        return await RemoveRoleAsync(userId, roleId, cancellationToken);
    }

    public async Task<OperationResult> GrantPermissionAsync(string userId, string permissionId, string? tenantId = null, string? grantedBy = null, CancellationToken cancellationToken = default)
    {
        return await GrantPermissionAsync(userId, permissionId, grantedBy, cancellationToken);
    }

    public async Task<OperationResult> RevokePermissionAsync(string userId, string permissionId, string? tenantId = null, CancellationToken cancellationToken = default)
    {
        return await RevokePermissionAsync(userId, permissionId, cancellationToken);
    }
}
