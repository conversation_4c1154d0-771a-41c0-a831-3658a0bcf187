using System.Security.Claims;
using NotifyMaster.Core.Models;

namespace NotifyMasterApi.Authorization;

/// <summary>
/// Authorization policy names
/// </summary>
public static class PolicyNames
{
    public const string RequireSystemAdmin = "RequireSystemAdmin";
    public const string RequireTenantAdmin = "RequireTenantAdmin";
    public const string RequireTenantAccess = "RequireTenantAccess";
    public const string RequireUser = "RequireUser";
    public const string RequireViewer = "RequireViewer";
}

/// <summary>
/// Authorization requirements
/// </summary>
public class SystemAdminRequirement : IAuthorizationRequirement { }
public class TenantAdminRequirement : IAuthorizationRequirement { }
public class TenantAccessRequirement : IAuthorizationRequirement { }
public class UserRequirement : IAuthorizationRequirement { }
public class ViewerRequirement : IAuthorizationRequirement { }

/// <summary>
/// Permission-based authorization requirement
/// </summary>
public class PermissionRequirement : IAuthorizationRequirement
{
    public string Resource { get; }
    public string Action { get; }

    public PermissionRequirement(string resource, string action)
    {
        Resource = resource;
        Action = action;
    }
}

/// <summary>
/// System admin authorization handler
/// </summary>
public class SystemAdminHandler : AuthorizationHandler<SystemAdminRequirement>
{
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, SystemAdminRequirement requirement)
    {
        if (context.User.IsInRole(SystemRoles.SuperAdmin))
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}

/// <summary>
/// Tenant admin authorization handler
/// </summary>
public class TenantAdminHandler : AuthorizationHandler<TenantAdminRequirement>
{
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, TenantAdminRequirement requirement)
    {
        if (context.User.IsInRole(SystemRoles.SuperAdmin) || 
            context.User.IsInRole(SystemRoles.TenantAdmin))
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}

/// <summary>
/// Tenant access authorization handler
/// </summary>
public class TenantAccessHandler : AuthorizationHandler<TenantAccessRequirement>
{
    private readonly ITenantContext _tenantContext;

    public TenantAccessHandler(ITenantContext tenantContext)
    {
        _tenantContext = tenantContext;
    }

    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, TenantAccessRequirement requirement)
    {
        // System admin has access to all tenants
        if (context.User.IsInRole(SystemRoles.SuperAdmin))
        {
            context.Succeed(requirement);
            return Task.CompletedTask;
        }

        // Check if user belongs to the current tenant
        var userTenantId = context.User.FindFirst("tenant_id")?.Value;
        var currentTenantId = _tenantContext.TenantId;

        if (!string.IsNullOrEmpty(userTenantId) && 
            !string.IsNullOrEmpty(currentTenantId) && 
            userTenantId == currentTenantId)
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}

/// <summary>
/// User authorization handler
/// </summary>
public class UserHandler : AuthorizationHandler<UserRequirement>
{
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, UserRequirement requirement)
    {
        if (context.User.IsInRole(SystemRoles.SuperAdmin) ||
            context.User.IsInRole(SystemRoles.TenantAdmin) ||
            context.User.IsInRole(SystemRoles.User))
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}

/// <summary>
/// Viewer authorization handler
/// </summary>
public class ViewerHandler : AuthorizationHandler<ViewerRequirement>
{
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, ViewerRequirement requirement)
    {
        if (context.User.IsInRole(SystemRoles.SuperAdmin) ||
            context.User.IsInRole(SystemRoles.TenantAdmin) ||
            context.User.IsInRole(SystemRoles.User) ||
            context.User.IsInRole(SystemRoles.Viewer))
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}

/// <summary>
/// Permission-based authorization handler
/// </summary>
public class PermissionHandler : AuthorizationHandler<PermissionRequirement>
{
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<PermissionHandler> _logger;

    public PermissionHandler(ITenantContext tenantContext, ILogger<PermissionHandler> logger)
    {
        _tenantContext = tenantContext;
        _logger = logger;
    }

    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, PermissionRequirement requirement)
    {
        try
        {
            // System admin has all permissions
            if (context.User.IsInRole(SystemRoles.SuperAdmin))
            {
                context.Succeed(requirement);
                return;
            }

            // Check if user has the specific permission
            var hasPermission = await _tenantContext.HasPermissionAsync(requirement.Resource, requirement.Action);
            if (hasPermission)
            {
                context.Succeed(requirement);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission {Resource}:{Action}", requirement.Resource, requirement.Action);
        }
    }
}

/// <summary>
/// Custom authorization attribute for permissions
/// </summary>
public class RequirePermissionAttribute : AuthorizeAttribute
{
    public RequirePermissionAttribute(string resource, string action)
    {
        Policy = $"Permission:{resource}:{action}";
    }
}

/// <summary>
/// Extension methods for authorization
/// </summary>
public static class AuthorizationExtensions
{
    /// <summary>
    /// Adds authorization policies to the service collection
    /// </summary>
    public static IServiceCollection AddAuthorizationPolicies(this IServiceCollection services)
    {
        services.AddAuthorization(options =>
        {
            // Role-based policies
            options.AddPolicy(PolicyNames.RequireSystemAdmin, policy =>
                policy.Requirements.Add(new SystemAdminRequirement()));

            options.AddPolicy(PolicyNames.RequireTenantAdmin, policy =>
                policy.Requirements.Add(new TenantAdminRequirement()));

            options.AddPolicy(PolicyNames.RequireTenantAccess, policy =>
                policy.Requirements.Add(new TenantAccessRequirement()));

            options.AddPolicy(PolicyNames.RequireUser, policy =>
                policy.Requirements.Add(new UserRequirement()));

            options.AddPolicy(PolicyNames.RequireViewer, policy =>
                policy.Requirements.Add(new ViewerRequirement()));

            // Permission-based policies
            foreach (var permission in SystemPermissions.GetDefaultPermissions())
            {
                options.AddPolicy($"Permission:{permission.Resource}:{permission.Action}", policy =>
                    policy.Requirements.Add(new PermissionRequirement(permission.Resource, permission.Action)));
            }
        });

        // Register authorization handlers
        services.AddScoped<IAuthorizationHandler, SystemAdminHandler>();
        services.AddScoped<IAuthorizationHandler, TenantAdminHandler>();
        services.AddScoped<IAuthorizationHandler, TenantAccessHandler>();
        services.AddScoped<IAuthorizationHandler, UserHandler>();
        services.AddScoped<IAuthorizationHandler, ViewerHandler>();
        services.AddScoped<IAuthorizationHandler, PermissionHandler>();

        return services;
    }

    /// <summary>
    /// Checks if a user has a specific permission
    /// </summary>
    public static bool HasPermission(this ClaimsPrincipal user, string resource, string action)
    {
        if (user.IsInRole(SystemRoles.SuperAdmin))
        {
            return true;
        }

        var permissionClaim = $"{resource}:{action}";
        return user.HasClaim("permission", permissionClaim);
    }

    /// <summary>
    /// Gets the tenant ID from user claims
    /// </summary>
    public static string? GetTenantId(this ClaimsPrincipal user)
    {
        return user.FindFirst("tenant_id")?.Value;
    }

    /// <summary>
    /// Gets the user ID from user claims
    /// </summary>
    public static string? GetUserId(this ClaimsPrincipal user)
    {
        return user.FindFirst("user_id")?.Value ?? user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }

    /// <summary>
    /// Checks if user is a system admin
    /// </summary>
    public static bool IsSystemAdmin(this ClaimsPrincipal user)
    {
        return user.IsInRole(SystemRoles.SuperAdmin);
    }

    /// <summary>
    /// Checks if user is a tenant admin
    /// </summary>
    public static bool IsTenantAdmin(this ClaimsPrincipal user)
    {
        return user.IsInRole(SystemRoles.SuperAdmin) || user.IsInRole(SystemRoles.TenantAdmin);
    }
}

