﻿"restore":{"projectUniqueName":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotificationPortal\\NotificationPortal.csproj","projectName":"NotificationPortal","projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotificationPortal\\NotificationPortal.csproj","outputPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotificationPortal\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\NotifyMasterApi.csproj":{"projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\NotifyMasterApi.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"10.0.100"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Blazor-ApexCharts":{"target":"Package","version":"[6.0.1, )"},"BlazorMonaco":{"target":"Package","version":"[3.3.0, )"},"Blazored.LocalStorage":{"target":"Package","version":"[4.5.0, )"},"Blazored.Toast":{"target":"Package","version":"[4.2.1, )"},"Markdig":{"target":"Package","version":"[0.41.3, )"},"Microsoft.AspNetCore.Authentication.JwtBearer":{"target":"Package","version":"[9.0.6, )"},"Microsoft.AspNetCore.Authorization":{"target":"Package","version":"[9.0.6, )"},"Microsoft.AspNetCore.Components.DataAnnotations.Validation":{"target":"Package","version":"[3.2.0-rc1.20223.4, )"},"Microsoft.AspNetCore.Mvc.Localization":{"target":"Package","version":"[2.3.0, )"},"Microsoft.AspNetCore.SignalR":{"target":"Package","version":"[1.2.0, )"},"Microsoft.AspNetCore.SignalR.Client":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.InMemory":{"target":"Package","version":"[9.0.6, )"},"Microsoft.Extensions.Http":{"target":"Package","version":"[9.0.6, )"},"Microsoft.Extensions.Localization":{"target":"Package","version":"[9.0.6, )"},"MudBlazor":{"target":"Package","version":"[7.8.0, )"},"System.Text.Json":{"target":"Package","version":"[9.0.6, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.NETCore.App.Host.win-x64","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.NETCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[9.0.5, 9.0.5]"}],"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json"}}