// Using statements are handled by GlobalUsings.cs

namespace PluginCore.Base;

/// <summary>
/// Service interface for storing and retrieving messages for resending capabilities.
/// </summary>
public interface IMessageStorageService
{
    /// <summary>
    /// Stores a message for potential resending.
    /// </summary>
    Task<OperationResult> StoreMessageAsync(
        string messageId,
        string gatewayId,
        MessagePayload payload,
        MessageSendResult sendResult,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves a stored message by ID.
    /// </summary>
    Task<StoredMessage?> GetStoredMessageAsync(
        string messageId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all stored messages for a gateway.
    /// </summary>
    Task<IReadOnlyList<StoredMessage>> GetStoredMessagesAsync(
        string gatewayId,
        DateTimeOffset? fromDate = null,
        DateTimeOffset? toDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates the send result for a stored message.
    /// </summary>
    Task<OperationResult> UpdateSendResultAsync(
        string messageId,
        MessageSendResult sendResult,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes old stored messages based on retention policy.
    /// </summary>
    Task<OperationResult> CleanupOldMessagesAsync(
        TimeSpan retentionPeriod,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets message statistics for a gateway.
    /// </summary>
    Task<MessageStatistics> GetMessageStatisticsAsync(
        string gatewayId,
        DateTimeOffset fromDate,
        DateTimeOffset toDate,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents a stored message with its send history.
/// </summary>
public record StoredMessage(
    string MessageId,
    string GatewayId,
    MessagePayload Payload,
    MessageSendResult OriginalSendResult,
    DateTimeOffset StoredAt,
    List<MessageSendResult> ResendHistory);

/// <summary>
/// Message statistics for a gateway.
/// </summary>
public record MessageStatistics(
    int TotalMessages,
    int SuccessfulMessages,
    int FailedMessages,
    int ResentMessages,
    double SuccessRate);

/// <summary>
/// Default implementation of the message storage service.
/// Uses in-memory storage by default, but can be extended for persistent storage.
/// </summary>
public class DefaultMessageStorageService : IMessageStorageService
{
    private readonly Dictionary<string, StoredMessage> _storedMessages = new();
    private readonly object _lock = new();
    private readonly ILogger? _logger;

    public DefaultMessageStorageService(ILogger? logger = null)
    {
        _logger = logger;
    }

    public Task<OperationResult> StoreMessageAsync(
        string messageId,
        string gatewayId,
        MessagePayload payload,
        MessageSendResult sendResult,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var storedMessage = new StoredMessage(
                MessageId: messageId,
                GatewayId: gatewayId,
                Payload: payload,
                OriginalSendResult: sendResult,
                StoredAt: DateTimeOffset.UtcNow,
                ResendHistory: new List<MessageSendResult>()
            );

            _storedMessages[messageId] = storedMessage;
            
            _logger?.LogDebug("Message {MessageId} stored for gateway {GatewayId}", messageId, gatewayId);
            
            return Task.FromResult(new OperationResult(true, "Message stored successfully"));
        }
    }

    public Task<StoredMessage?> GetStoredMessageAsync(
        string messageId,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            _storedMessages.TryGetValue(messageId, out var message);
            return Task.FromResult(message);
        }
    }

    public Task<IReadOnlyList<StoredMessage>> GetStoredMessagesAsync(
        string gatewayId,
        DateTimeOffset? fromDate = null,
        DateTimeOffset? toDate = null,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var query = _storedMessages.Values.Where(m => m.GatewayId == gatewayId);

            if (fromDate.HasValue)
                query = query.Where(m => m.StoredAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(m => m.StoredAt <= toDate.Value);

            var messages = query.OrderByDescending(m => m.StoredAt)
                               .ToList()
                               .AsReadOnly();

            return Task.FromResult<IReadOnlyList<StoredMessage>>(messages);
        }
    }

    public Task<OperationResult> UpdateSendResultAsync(
        string messageId,
        MessageSendResult sendResult,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            if (!_storedMessages.TryGetValue(messageId, out var storedMessage))
            {
                return Task.FromResult(new OperationResult(false, "Stored message not found"));
            }

            // Add to resend history
            storedMessage.ResendHistory.Add(sendResult);
            
            _logger?.LogDebug("Send result updated for message {MessageId}", messageId);
            
            return Task.FromResult(new OperationResult(true, "Send result updated"));
        }
    }

    public Task<OperationResult> CleanupOldMessagesAsync(
        TimeSpan retentionPeriod,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var cutoffDate = DateTimeOffset.UtcNow - retentionPeriod;
            var messagesToRemove = _storedMessages.Values
                .Where(m => m.StoredAt < cutoffDate)
                .Select(m => m.MessageId)
                .ToList();

            foreach (var messageId in messagesToRemove)
            {
                _storedMessages.Remove(messageId);
            }

            _logger?.LogInformation("Cleaned up {Count} old messages", messagesToRemove.Count);
            
            return Task.FromResult(new OperationResult(true, $"Cleaned up {messagesToRemove.Count} old messages"));
        }
    }

    public Task<MessageStatistics> GetMessageStatisticsAsync(
        string gatewayId,
        DateTimeOffset fromDate,
        DateTimeOffset toDate,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var messages = _storedMessages.Values
                .Where(m => m.GatewayId == gatewayId && 
                           m.StoredAt >= fromDate && 
                           m.StoredAt <= toDate)
                .ToList();

            var totalMessages = messages.Count;
            var successfulMessages = messages.Count(m => m.OriginalSendResult.Status == "Sent" || m.OriginalSendResult.Status == "Delivered");
            var failedMessages = messages.Count(m => m.OriginalSendResult.Status == "Failed");
            var resentMessages = messages.Count(m => m.ResendHistory.Any());
            var successRate = totalMessages > 0 ? (double)successfulMessages / totalMessages * 100 : 0;

            return Task.FromResult(new MessageStatistics(
                TotalMessages: totalMessages,
                SuccessfulMessages: successfulMessages,
                FailedMessages: failedMessages,
                ResentMessages: resentMessages,
                SuccessRate: successRate
            ));
        }
    }
}

/// <summary>
/// Base class that provides scheduling and storage capabilities to gateway implementations.
/// </summary>
public abstract class GatewayWithSchedulingBase
{
    protected readonly IMessageSchedulingService _schedulingService;
    protected readonly IMessageStorageService _storageService;
    protected readonly ILogger? _logger;
    protected readonly string _gatewayId;

    protected GatewayWithSchedulingBase(
        ILogger? logger = null,
        IMessageSchedulingService? schedulingService = null,
        IMessageStorageService? storageService = null)
    {
        _logger = logger;
        _schedulingService = schedulingService ?? new DefaultMessageSchedulingService(logger);
        _storageService = storageService ?? new DefaultMessageStorageService(logger);
        _gatewayId = GetType().Name;
    }

    /// <summary>
    /// Implements message scheduling using the scheduling service.
    /// </summary>
    protected virtual async Task<MessageScheduleResult> ScheduleMessageWithServiceAsync(
        MessagePayload payload,
        DateTimeOffset scheduledTime,
        CancellationToken cancellationToken = default)
    {
        return await _schedulingService.ScheduleMessageAsync(_gatewayId, payload, scheduledTime, cancellationToken);
    }

    /// <summary>
    /// Implements message cancellation using the scheduling service.
    /// </summary>
    protected virtual async Task<OperationResult> CancelScheduledMessageWithServiceAsync(
        string scheduledMessageId,
        CancellationToken cancellationToken = default)
    {
        return await _schedulingService.CancelScheduledMessageAsync(scheduledMessageId, cancellationToken);
    }

    /// <summary>
    /// Implements message resending using the storage service.
    /// </summary>
    protected virtual async Task<MessageSendResult> ResendMessageWithServiceAsync(
        string originalMessageId,
        CancellationToken cancellationToken = default)
    {
        var storedMessage = await _storageService.GetStoredMessageAsync(originalMessageId, cancellationToken);
        if (storedMessage == null)
        {
            return new MessageSendResult(
                MessageId: Guid.NewGuid().ToString(),
                Timestamp: DateTimeOffset.UtcNow,
                Status: "Failed",
                CorrelationId: null
            );
        }

        // Create a new correlation ID for the resend
        var resendPayload = new MessagePayload
        {
            Recipient = storedMessage.Payload.Recipient,
            Content = storedMessage.Payload.Content,
            From = storedMessage.Payload.From,
            Subject = storedMessage.Payload.Subject,
            Headers = storedMessage.Payload.Headers,
            CorrelationId = $"resend_{originalMessageId}_{DateTimeOffset.UtcNow:yyyyMMddHHmmss}",
            Metadata = storedMessage.Payload.Metadata,
            PushNotificationData = storedMessage.Payload.PushNotificationData,
            WebAppNotificationData = storedMessage.Payload.WebAppNotificationData
        };

        // Call the actual send method (must be implemented by derived class)
        var sendResult = await SendMessageInternalAsync(resendPayload, cancellationToken);
        
        // Update the storage with the resend result
        await _storageService.UpdateSendResultAsync(originalMessageId, sendResult, cancellationToken);
        
        return sendResult;
    }

    /// <summary>
    /// Stores a message after sending for potential resending.
    /// </summary>
    protected virtual async Task StoreMessageAsync(
        string messageId,
        MessagePayload payload,
        MessageSendResult sendResult,
        CancellationToken cancellationToken = default)
    {
        await _storageService.StoreMessageAsync(messageId, _gatewayId, payload, sendResult, cancellationToken);
    }

    /// <summary>
    /// Abstract method that derived classes must implement for actual message sending.
    /// </summary>
    protected abstract Task<MessageSendResult> SendMessageInternalAsync(
        MessagePayload payload,
        CancellationToken cancellationToken);
}
