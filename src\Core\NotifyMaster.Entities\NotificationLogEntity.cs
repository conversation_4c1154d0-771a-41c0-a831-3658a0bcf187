// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Entities;

/// <summary>
/// Entity representing a notification log entry
/// </summary>
[Table("NotificationLogs")]
public class NotificationLog
{
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required, MaxLength(100)]
    public string TenantId { get; set; } = string.Empty;
    
    [Required, MaxLength(50)]
    public string MessageType { get; set; } = string.Empty; // SMS, Email, Push, WebApp
    
    [Required, MaxLength(500)]
    public string Recipient { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Sender { get; set; }
    
    [MaxLength(200)]
    public string? Subject { get; set; }
    
    [Required]
    public string Content { get; set; } = string.Empty;
    
    [Required, MaxLength(50)]
    public string Status { get; set; } = string.Empty; // Pending, Sent, Failed, Delivered
    
    public string? ErrorMessage { get; set; }
    
    [MaxLength(100)]
    public string? CorrelationId { get; set; }
    
    [MaxLength(100)]
    public string? ExternalId { get; set; }
    
    [MaxLength(100)]
    public string? GatewayName { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? SentAt { get; set; }
    
    public DateTime? DeliveredAt { get; set; }
    
    public DateTime? FailedAt { get; set; }
    
    [MaxLength(100)]
    public string? CreatedBy { get; set; }
    
    public int RetryCount { get; set; } = 0;
    
    public DateTime? NextRetryAt { get; set; }
    
    // Additional metadata as JSON
    public string? Metadata { get; set; }
    
    // Navigation properties
    public TenantEntity? Tenant { get; set; }
}

/// <summary>
/// Entity representing a message template
/// </summary>
[Table("MessageTemplates")]
public class MessageTemplate
{
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required, MaxLength(100)]
    public string TenantId { get; set; } = string.Empty;
    
    [Required, MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required, MaxLength(50)]
    public string MessageType { get; set; } = string.Empty; // SMS, Email, Push, WebApp
    
    [MaxLength(200)]
    public string? Subject { get; set; }
    
    [Required]
    public string Content { get; set; } = string.Empty;
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    [MaxLength(100)]
    public string? CreatedBy { get; set; }
    
    [MaxLength(100)]
    public string? UpdatedBy { get; set; }
    
    // Template variables as JSON
    public string? Variables { get; set; }
    
    // Navigation properties
    public TenantEntity? Tenant { get; set; }
}

/// <summary>
/// Entity representing a scheduled message
/// </summary>
[Table("ScheduledMessages")]
public class ScheduledMessage
{
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required, MaxLength(100)]
    public string TenantId { get; set; } = string.Empty;
    
    [Required, MaxLength(50)]
    public string MessageType { get; set; } = string.Empty; // SMS, Email, Push, WebApp
    
    [Required, MaxLength(500)]
    public string Recipient { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Sender { get; set; }
    
    [MaxLength(200)]
    public string? Subject { get; set; }
    
    [Required]
    public string Content { get; set; } = string.Empty;
    
    [Required]
    public DateTime ScheduledAt { get; set; }
    
    [Required, MaxLength(50)]
    public string Status { get; set; } = "Scheduled"; // Scheduled, Sent, Failed, Cancelled
    
    public string? ErrorMessage { get; set; }
    
    [MaxLength(100)]
    public string? CorrelationId { get; set; }
    
    [MaxLength(100)]
    public string? TemplateId { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? SentAt { get; set; }
    
    public DateTime? CancelledAt { get; set; }
    
    [MaxLength(100)]
    public string? CreatedBy { get; set; }
    
    // Template variables as JSON
    public string? Variables { get; set; }
    
    // Additional metadata as JSON
    public string? Metadata { get; set; }
    
    // Navigation properties
    public TenantEntity? Tenant { get; set; }
    public MessageTemplate? Template { get; set; }
}

/// <summary>
/// Entity representing a webhook endpoint
/// </summary>
[Table("WebhookEndpoints")]
public class WebhookEndpoint
{
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required, MaxLength(100)]
    public string TenantId { get; set; } = string.Empty;
    
    [Required, MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required, MaxLength(2000)]
    public string Url { get; set; } = string.Empty;
    
    [Required, MaxLength(50)]
    public string Method { get; set; } = "POST"; // GET, POST, PUT, PATCH, DELETE
    
    public string? Headers { get; set; } // JSON
    
    public string? AuthenticationConfig { get; set; } // JSON
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    [MaxLength(100)]
    public string? CreatedBy { get; set; }
    
    [MaxLength(100)]
    public string? UpdatedBy { get; set; }
    
    // Event types this webhook subscribes to
    public string? EventTypes { get; set; } // JSON array
    
    // Retry configuration
    public int MaxRetries { get; set; } = 3;
    public int TimeoutSeconds { get; set; } = 30;
    
    // Navigation properties
    public TenantEntity? Tenant { get; set; }
}
