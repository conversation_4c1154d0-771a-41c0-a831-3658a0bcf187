using NotifyMasterApi.Features.Plugins.Commands;
using NotifyMasterApi.Features.Plugins.Queries;

namespace NotifyMasterApi.Features.Plugins;

// Simple endpoint request models (using the models from NotifyMasterApi.Models)

/// <summary>
/// Load plugin endpoint
/// </summary>
public class LoadPluginEndpoint : Endpoint<LoadPluginRequest, PluginOperationResult>
{
    private readonly IMediator _mediator;

    public LoadPluginEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Post("/api/plugins/load");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Load Plugin";
            s.Description = "Load a plugin by name or path";
            s.Response<PluginOperationResult>(200, "Plugin loaded successfully");
        });
    }

    public override async Task HandleAsync(LoadPluginRequest req, CancellationToken ct)
    {
        var command = new LoadPluginCommand(req.Name, req.Path);
        var result = await _mediator.Send(command, ct);
        await SendOkAsync(result, ct);
    }
}

/// <summary>
/// Unload plugin endpoint
/// </summary>
public class UnloadPluginEndpoint : Endpoint<UnloadPluginRequest, PluginOperationResult>
{
    private readonly IMediator _mediator;

    public UnloadPluginEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Post("/api/plugins/unload");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Unload Plugin";
            s.Description = "Unload a plugin by name";
            s.Response<PluginOperationResult>(200, "Plugin unloaded successfully");
        });
    }

    public override async Task HandleAsync(UnloadPluginRequest req, CancellationToken ct)
    {
        var command = new UnloadPluginCommand(req.Name);
        var result = await _mediator.Send(command, ct);
        await SendOkAsync(result, ct);
    }
}

/// <summary>
/// Load directory endpoint
/// </summary>
public class LoadDirectoryEndpoint : Endpoint<LoadDirectoryRequest, PluginOperationResult>
{
    private readonly IMediator _mediator;

    public LoadDirectoryEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Post("/api/plugins/load-directory");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Load Directory";
            s.Description = "Load all plugins from a directory";
            s.Response<PluginOperationResult>(200, "Plugins loaded successfully");
        });
    }

    public override async Task HandleAsync(LoadDirectoryRequest req, CancellationToken ct)
    {
        var command = new LoadDirectoryCommand(req.Directory);
        var result = await _mediator.Send(command, ct);
        await SendOkAsync(result, ct);
    }
}

/// <summary>
/// Plugin health endpoint
/// </summary>
public class GetPluginHealthEndpoint : EndpointWithoutRequest<PluginHealthResponse>
{
    private readonly IMediator _mediator;

    public GetPluginHealthEndpoint(IMediator mediator)
    {
        _mediator = mediator;
    }

    public override void Configure()
    {
        Get("/api/plugins/health");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get Plugin Health";
            s.Description = "Get health status of all plugins";
            s.Response<PluginHealthResponse>(200, "Plugin health retrieved successfully");
        });
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var query = new GetPluginHealthQuery();
        var result = await _mediator.Send(query, ct);
        await SendOkAsync(result, ct);
    }
}
