{"sourceFile": "src/Core/NotifyMaster.Entities/RoleEntity.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751238944955, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751238944955, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Entities;\n\n/// <summary>\n/// Database entity for Role\n/// </summary>\n[Table(\"Roles\")]\npublic class RoleEntity\n{\n    [Key]\n    [MaxLength(36)]\n    public string Id { get; set; } = string.Empty;\n    \n    [Required]\n    [MaxLength(100)]\n    public string Name { get; set; } = string.Empty;\n    \n    [MaxLength(500)]\n    public string? Description { get; set; }\n    \n    [MaxLength(36)]\n    public string? TenantId { get; set; }\n    \n    public int Scope { get; set; } = 1; // RoleScope enum as int (Tenant = 1)\n    \n    public bool IsSystemRole { get; set; } = false;\n    \n    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;\n    \n    public DateTime? UpdatedAt { get; set; }\n    \n    [MaxLength(36)]\n    public string? CreatedBy { get; set; }\n    \n    [MaxLength(36)]\n    public string? UpdatedBy { get; set; }\n    \n    // Navigation properties\n    public virtual ICollection<UserRoleEntity> Users { get; set; } = new List<UserRoleEntity>();\n    public virtual ICollection<RolePermissionEntity> Permissions { get; set; } = new List<RolePermissionEntity>();\n\n}\n"}]}