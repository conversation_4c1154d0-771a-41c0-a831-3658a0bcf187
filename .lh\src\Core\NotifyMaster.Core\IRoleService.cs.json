{"sourceFile": "src/Core/NotifyMaster.Core/IRoleService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1751231998075, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751232070343, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,4 @@\n-using CoreModels = NotifyMaster.Core.Models;\n-using PluginModels = PluginCore.Models;\n-\n namespace NotifyMaster.Core.Interfaces;\n \n /// <summary>\n /// Interface for role management service\n"}, {"date": 1751232085448, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,19 +7,19 @@\n {\n     /// <summary>\n     /// Gets a role by ID\n     /// </summary>\n-    Task<Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default);\n-    \n+    Task<CoreModels.Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default);\n+\n     /// <summary>\n     /// Gets a role by name\n     /// </summary>\n-    Task<Role?> GetRoleByNameAsync(string roleName, RoleScope scope = RoleScope.Tenant, CancellationToken cancellationToken = default);\n-    \n+    Task<CoreModels.Role?> GetRoleByNameAsync(string roleName, CoreModels.RoleScope scope = CoreModels.RoleScope.Tenant, CancellationToken cancellationToken = default);\n+\n     /// <summary>\n     /// Gets all roles with pagination\n     /// </summary>\n-    Task<IReadOnlyList<Role>> GetRolesAsync(RoleScope? scope = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);\n+    Task<IReadOnlyList<CoreModels.Role>> GetRolesAsync(CoreModels.RoleScope? scope = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);\n     \n     /// <summary>\n     /// Creates a new role\n     /// </summary>\n"}, {"date": 1751232099395, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -22,19 +22,19 @@\n     \n     /// <summary>\n     /// Creates a new role\n     /// </summary>\n-    Task<OperationResult<Role>> CreateRoleAsync(CreateRoleRequest request, CancellationToken cancellationToken = default);\n-    \n+    Task<CoreModels.OperationResult<CoreModels.Role>> CreateRoleAsync(CoreModels.CreateRoleRequest request, CancellationToken cancellationToken = default);\n+\n     /// <summary>\n     /// Updates an existing role\n     /// </summary>\n-    Task<OperationResult<Role>> UpdateRoleAsync(string roleId, UpdateRoleRequest request, CancellationToken cancellationToken = default);\n-    \n+    Task<CoreModels.OperationResult<CoreModels.Role>> UpdateRoleAsync(string roleId, CoreModels.UpdateRoleRequest request, CancellationToken cancellationToken = default);\n+\n     /// <summary>\n     /// Deletes a role\n     /// </summary>\n-    Task<OperationResult> DeleteRoleAsync(string roleId, CancellationToken cancellationToken = default);\n+    Task<CoreModels.OperationResult> DeleteRoleAsync(string roleId, CancellationToken cancellationToken = default);\n     \n     /// <summary>\n     /// Assigns a permission to a role\n     /// </summary>\n"}], "date": 1751231998075, "name": "Commit-0", "content": "using CoreModels = NotifyMaster.Core.Models;\nusing PluginModels = PluginCore.Models;\n\nnamespace NotifyMaster.Core.Interfaces;\n\n/// <summary>\n/// Interface for role management service\n/// </summary>\npublic interface IRoleService\n{\n    /// <summary>\n    /// Gets a role by ID\n    /// </summary>\n    Task<Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets a role by name\n    /// </summary>\n    Task<Role?> GetRoleByNameAsync(string roleName, RoleScope scope = RoleScope.Tenant, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets all roles with pagination\n    /// </summary>\n    Task<IReadOnlyList<Role>> GetRolesAsync(RoleScope? scope = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Creates a new role\n    /// </summary>\n    Task<OperationResult<Role>> CreateRoleAsync(CreateRoleRequest request, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Updates an existing role\n    /// </summary>\n    Task<OperationResult<Role>> UpdateRoleAsync(string roleId, UpdateRoleRequest request, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Deletes a role\n    /// </summary>\n    Task<OperationResult> DeleteRoleAsync(string roleId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Assigns a permission to a role\n    /// </summary>\n    Task<OperationResult> AssignPermissionAsync(string roleId, string permissionId, string? assignedBy = null, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Removes a permission from a role\n    /// </summary>\n    Task<OperationResult> RemovePermissionAsync(string roleId, string permissionId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets all permissions for a role\n    /// </summary>\n    Task<IReadOnlyList<Permission>> GetRolePermissionsAsync(string roleId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets all users assigned to a role\n    /// </summary>\n    Task<IReadOnlyList<User>> GetRoleUsersAsync(string roleId, CancellationToken cancellationToken = default);\n}\n\n/// <summary>\n/// Interface for permission management service\n/// </summary>\npublic interface IPermissionService\n{\n    /// <summary>\n    /// Gets a permission by ID\n    /// </summary>\n    Task<Permission?> GetPermissionAsync(string permissionId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets a permission by resource and action\n    /// </summary>\n    Task<Permission?> GetPermissionAsync(string resource, string action, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets all permissions with pagination\n    /// </summary>\n    Task<IReadOnlyList<Permission>> GetPermissionsAsync(string? resource = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Creates a new permission\n    /// </summary>\n    Task<OperationResult<Permission>> CreatePermissionAsync(CreatePermissionRequest request, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Updates an existing permission\n    /// </summary>\n    Task<OperationResult<Permission>> UpdatePermissionAsync(string permissionId, UpdatePermissionRequest request, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Deletes a permission\n    /// </summary>\n    Task<OperationResult> DeletePermissionAsync(string permissionId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets all roles that have a specific permission\n    /// </summary>\n    Task<IReadOnlyList<Role>> GetPermissionRolesAsync(string permissionId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets all users that have a specific permission (directly or through roles)\n    /// </summary>\n    Task<IReadOnlyList<User>> GetPermissionUsersAsync(string permissionId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Seeds default system permissions\n    /// </summary>\n    Task<OperationResult> SeedDefaultPermissionsAsync(CancellationToken cancellationToken = default);\n}\n\n/// <summary>\n/// Request model for creating a role\n/// </summary>\npublic class CreateRoleRequest\n{\n    public string Name { get; set; } = string.Empty;\n    public string? Description { get; set; }\n    public RoleScope Scope { get; set; } = RoleScope.Tenant;\n    public bool IsSystemRole { get; set; } = false;\n    public List<string> PermissionIds { get; set; } = new();\n}\n\n/// <summary>\n/// Request model for updating a role\n/// </summary>\npublic class UpdateRoleRequest\n{\n    public string? Name { get; set; }\n    public string? Description { get; set; }\n    public RoleScope? Scope { get; set; }\n}\n\n/// <summary>\n/// Request model for creating a permission\n/// </summary>\npublic class CreatePermissionRequest\n{\n    public string Name { get; set; } = string.Empty;\n    public string? Description { get; set; }\n    public string Resource { get; set; } = string.Empty;\n    public string Action { get; set; } = string.Empty;\n    public bool IsSystemPermission { get; set; } = false;\n}\n\n/// <summary>\n/// Request model for updating a permission\n/// </summary>\npublic class UpdatePermissionRequest\n{\n    public string? Name { get; set; }\n    public string? Description { get; set; }\n    public string? Resource { get; set; }\n    public string? Action { get; set; }\n}\n\n/// <summary>\n/// Predefined system permissions\n/// </summary>\npublic static class SystemPermissions\n{\n    // Tenant management\n    public const string TenantCreate = \"tenant:create\";\n    public const string TenantRead = \"tenant:read\";\n    public const string TenantUpdate = \"tenant:update\";\n    public const string TenantDelete = \"tenant:delete\";\n    public const string TenantSuspend = \"tenant:suspend\";\n    \n    // User management\n    public const string UserCreate = \"user:create\";\n    public const string UserRead = \"user:read\";\n    public const string UserUpdate = \"user:update\";\n    public const string UserDelete = \"user:delete\";\n    public const string UserSuspend = \"user:suspend\";\n    \n    // Role management\n    public const string RoleCreate = \"role:create\";\n    public const string RoleRead = \"role:read\";\n    public const string RoleUpdate = \"role:update\";\n    public const string RoleDelete = \"role:delete\";\n    public const string RoleAssign = \"role:assign\";\n    \n    // Permission management\n    public const string PermissionCreate = \"permission:create\";\n    public const string PermissionRead = \"permission:read\";\n    public const string PermissionUpdate = \"permission:update\";\n    public const string PermissionDelete = \"permission:delete\";\n    public const string PermissionGrant = \"permission:grant\";\n    \n    // Plugin management\n    public const string PluginRead = \"plugin:read\";\n    public const string PluginConfigure = \"plugin:configure\";\n    public const string PluginInstall = \"plugin:install\";\n    public const string PluginUninstall = \"plugin:uninstall\";\n    public const string PluginEnable = \"plugin:enable\";\n    public const string PluginDisable = \"plugin:disable\";\n    \n    // Message management\n    public const string MessageSend = \"message:send\";\n    public const string MessageRead = \"message:read\";\n    public const string MessageDelete = \"message:delete\";\n    public const string MessageSchedule = \"message:schedule\";\n    \n    // Template management\n    public const string TemplateCreate = \"template:create\";\n    public const string TemplateRead = \"template:read\";\n    public const string TemplateUpdate = \"template:update\";\n    public const string TemplateDelete = \"template:delete\";\n    \n    // Webhook management\n    public const string WebhookCreate = \"webhook:create\";\n    public const string WebhookRead = \"webhook:read\";\n    public const string WebhookUpdate = \"webhook:update\";\n    public const string WebhookDelete = \"webhook:delete\";\n    \n    // System administration\n    public const string SystemRead = \"system:read\";\n    public const string SystemConfigure = \"system:configure\";\n    public const string SystemMaintenance = \"system:maintenance\";\n    \n    // Metrics and monitoring\n    public const string MetricsRead = \"metrics:read\";\n    public const string MetricsExport = \"metrics:export\";\n    public const string LogsRead = \"logs:read\";\n    \n    /// <summary>\n    /// Gets all default system permissions\n    /// </summary>\n    public static IReadOnlyList<(string Name, string Description, string Resource, string Action)> GetDefaultPermissions()\n    {\n        return new List<(string, string, string, string)>\n        {\n            // Tenant permissions\n            (TenantCreate, \"Create new tenants\", \"tenant\", \"create\"),\n            (TenantRead, \"View tenant information\", \"tenant\", \"read\"),\n            (TenantUpdate, \"Update tenant settings\", \"tenant\", \"update\"),\n            (TenantDelete, \"Delete tenants\", \"tenant\", \"delete\"),\n            (TenantSuspend, \"Suspend/activate tenants\", \"tenant\", \"suspend\"),\n            \n            // User permissions\n            (UserCreate, \"Create new users\", \"user\", \"create\"),\n            (UserRead, \"View user information\", \"user\", \"read\"),\n            (UserUpdate, \"Update user profiles\", \"user\", \"update\"),\n            (UserDelete, \"Delete users\", \"user\", \"delete\"),\n            (UserSuspend, \"Suspend/activate users\", \"user\", \"suspend\"),\n            \n            // Role permissions\n            (RoleCreate, \"Create new roles\", \"role\", \"create\"),\n            (RoleRead, \"View role information\", \"role\", \"read\"),\n            (RoleUpdate, \"Update role definitions\", \"role\", \"update\"),\n            (RoleDelete, \"Delete roles\", \"role\", \"delete\"),\n            (RoleAssign, \"Assign roles to users\", \"role\", \"assign\"),\n            \n            // Permission permissions\n            (PermissionCreate, \"Create new permissions\", \"permission\", \"create\"),\n            (PermissionRead, \"View permission information\", \"permission\", \"read\"),\n            (PermissionUpdate, \"Update permission definitions\", \"permission\", \"update\"),\n            (PermissionDelete, \"Delete permissions\", \"permission\", \"delete\"),\n            (PermissionGrant, \"Grant permissions to users/roles\", \"permission\", \"grant\"),\n            \n            // Plugin permissions\n            (PluginRead, \"View plugin information\", \"plugin\", \"read\"),\n            (PluginConfigure, \"Configure plugin settings\", \"plugin\", \"configure\"),\n            (PluginInstall, \"Install new plugins\", \"plugin\", \"install\"),\n            (PluginUninstall, \"Uninstall plugins\", \"plugin\", \"uninstall\"),\n            (PluginEnable, \"Enable plugins\", \"plugin\", \"enable\"),\n            (PluginDisable, \"Disable plugins\", \"plugin\", \"disable\"),\n            \n            // Message permissions\n            (MessageSend, \"Send messages\", \"message\", \"send\"),\n            (MessageRead, \"View message history\", \"message\", \"read\"),\n            (MessageDelete, \"Delete messages\", \"message\", \"delete\"),\n            (MessageSchedule, \"Schedule messages\", \"message\", \"schedule\"),\n            \n            // Template permissions\n            (TemplateCreate, \"Create message templates\", \"template\", \"create\"),\n            (TemplateRead, \"View message templates\", \"template\", \"read\"),\n            (TemplateUpdate, \"Update message templates\", \"template\", \"update\"),\n            (TemplateDelete, \"Delete message templates\", \"template\", \"delete\"),\n            \n            // Webhook permissions\n            (WebhookCreate, \"Create webhooks\", \"webhook\", \"create\"),\n            (WebhookRead, \"View webhook configurations\", \"webhook\", \"read\"),\n            (WebhookUpdate, \"Update webhook settings\", \"webhook\", \"update\"),\n            (WebhookDelete, \"Delete webhooks\", \"webhook\", \"delete\"),\n            \n            // System permissions\n            (SystemRead, \"View system information\", \"system\", \"read\"),\n            (SystemConfigure, \"Configure system settings\", \"system\", \"configure\"),\n            (SystemMaintenance, \"Perform system maintenance\", \"system\", \"maintenance\"),\n            \n            // Monitoring permissions\n            (MetricsRead, \"View system metrics\", \"metrics\", \"read\"),\n            (MetricsExport, \"Export metrics data\", \"metrics\", \"export\"),\n            (LogsRead, \"View system logs\", \"logs\", \"read\")\n        };\n    }\n}\n\n/// <summary>\n/// Predefined system roles\n/// </summary>\npublic static class SystemRoles\n{\n    public const string SuperAdmin = \"SuperAdmin\";\n    public const string TenantAdmin = \"TenantAdmin\";\n    public const string User = \"User\";\n    public const string Viewer = \"Viewer\";\n    \n    /// <summary>\n    /// Gets default role definitions with their permissions\n    /// </summary>\n    public static IReadOnlyList<(string Name, string Description, RoleScope Scope, string[] Permissions)> GetDefaultRoles()\n    {\n        return new List<(string, string, RoleScope, string[])>\n        {\n            (SuperAdmin, \"System administrator with full access\", RoleScope.System, new[]\n            {\n                SystemPermissions.TenantCreate, SystemPermissions.TenantRead, SystemPermissions.TenantUpdate, \n                SystemPermissions.TenantDelete, SystemPermissions.TenantSuspend,\n                SystemPermissions.UserCreate, SystemPermissions.UserRead, SystemPermissions.UserUpdate, \n                SystemPermissions.UserDelete, SystemPermissions.UserSuspend,\n                SystemPermissions.RoleCreate, SystemPermissions.RoleRead, SystemPermissions.RoleUpdate, \n                SystemPermissions.RoleDelete, SystemPermissions.RoleAssign,\n                SystemPermissions.PermissionCreate, SystemPermissions.PermissionRead, SystemPermissions.PermissionUpdate, \n                SystemPermissions.PermissionDelete, SystemPermissions.PermissionGrant,\n                SystemPermissions.PluginRead, SystemPermissions.PluginConfigure, SystemPermissions.PluginInstall, \n                SystemPermissions.PluginUninstall, SystemPermissions.PluginEnable, SystemPermissions.PluginDisable,\n                SystemPermissions.SystemRead, SystemPermissions.SystemConfigure, SystemPermissions.SystemMaintenance,\n                SystemPermissions.MetricsRead, SystemPermissions.MetricsExport, SystemPermissions.LogsRead\n            }),\n            \n            (TenantAdmin, \"Tenant administrator with full tenant access\", RoleScope.Tenant, new[]\n            {\n                SystemPermissions.UserCreate, SystemPermissions.UserRead, SystemPermissions.UserUpdate, \n                SystemPermissions.UserDelete, SystemPermissions.UserSuspend,\n                SystemPermissions.RoleRead, SystemPermissions.RoleAssign,\n                SystemPermissions.PluginRead, SystemPermissions.PluginConfigure, SystemPermissions.PluginEnable, SystemPermissions.PluginDisable,\n                SystemPermissions.MessageSend, SystemPermissions.MessageRead, SystemPermissions.MessageDelete, SystemPermissions.MessageSchedule,\n                SystemPermissions.TemplateCreate, SystemPermissions.TemplateRead, SystemPermissions.TemplateUpdate, SystemPermissions.TemplateDelete,\n                SystemPermissions.WebhookCreate, SystemPermissions.WebhookRead, SystemPermissions.WebhookUpdate, SystemPermissions.WebhookDelete,\n                SystemPermissions.MetricsRead, SystemPermissions.MetricsExport\n            }),\n            \n            (User, \"Regular user with message sending capabilities\", RoleScope.Tenant, new[]\n            {\n                SystemPermissions.MessageSend, SystemPermissions.MessageRead, SystemPermissions.MessageSchedule,\n                SystemPermissions.TemplateRead, SystemPermissions.TemplateCreate, SystemPermissions.TemplateUpdate,\n                SystemPermissions.WebhookRead\n            }),\n            \n            (Viewer, \"Read-only access to tenant resources\", RoleScope.Tenant, new[]\n            {\n                SystemPermissions.MessageRead, SystemPermissions.TemplateRead, SystemPermissions.WebhookRead, \n                SystemPermissions.MetricsRead\n            })\n        };\n    }\n}\n"}]}