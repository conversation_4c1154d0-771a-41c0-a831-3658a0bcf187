namespace NotifyMasterApi.Infrastructure;

public class OutboundWebhookJob
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public string Method { get; set; } = "POST";
    public Dictionary<string, string> Headers { get; set; } = new();
    public string Payload { get; set; } = string.Empty;
    public int RetryCount { get; set; } = 0;
    public int MaxRetries { get; set; } = 3;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? NextRetryAt { get; set; }
    public string? LastError { get; set; }
    public string Status { get; set; } = "Pending"; // Pending, Processing, Completed, Failed
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class SystemEvent
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // sent, failed, delivered, webhook, system
    public string Channel { get; set; } = string.Empty; // email, sms, push, webhook
    public string MessageId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? Error { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> Data { get; set; } = new();
    public string? UserId { get; set; }
    public string? PluginName { get; set; }
    public string? Provider { get; set; }
}

public class DeadLetterMessage
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string OriginalMessageId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string MessageType { get; set; } = string.Empty;
    public string Payload { get; set; } = string.Empty;
    public string LastError { get; set; } = string.Empty;
    public int FailureCount { get; set; }
    public DateTime FirstFailedAt { get; set; } = DateTime.UtcNow;
    public DateTime LastFailedAt { get; set; } = DateTime.UtcNow;
    public string Status { get; set; } = "Failed"; // Failed, Reprocessing, Archived
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ValidationRule
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string RuleType { get; set; } = string.Empty; // schema, regex, custom
    public string RuleDefinition { get; set; } = string.Empty; // JSON schema, regex pattern, etc.
    public bool IsActive { get; set; } = true;
    public int Priority { get; set; } = 0;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedBy { get; set; }
}

public class ScheduledMessage
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string Payload { get; set; } = string.Empty;
    public DateTime ScheduledFor { get; set; }
    public string? CronExpression { get; set; }
    public string Status { get; set; } = "Scheduled"; // Scheduled, Sent, Cancelled, Failed
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedBy { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class RetryProfile
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Strategy { get; set; } = "exponential"; // linear, exponential, capped
    public int MaxRetries { get; set; } = 3;
    public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(30);
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromHours(1);
    public double BackoffMultiplier { get; set; } = 2.0;
    public bool IsDefault { get; set; } = false;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

public class UsageMetrics
{
    public string TenantId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public DateTime Period { get; set; }
    public string PeriodType { get; set; } = "hour"; // hour, day, month
    public long MessageCount { get; set; }
    public long SuccessCount { get; set; }
    public long FailureCount { get; set; }
    public double AverageLatencyMs { get; set; }
    public decimal Cost { get; set; }
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}

public class RegionConfig
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string Region { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public Dictionary<string, string> ProviderPreferences { get; set; } = new(); // channel -> provider
    public int Priority { get; set; } = 0;
    public bool IsActive { get; set; } = true;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class AccessAuditEntry
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string TenantId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public string Method { get; set; } = string.Empty;
    public string IpAddress { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public int StatusCode { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public TimeSpan Duration { get; set; }
    public string? RequestId { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class StorageConfig
{
    public string Provider { get; set; } = "InMemory"; // InMemory, SqlServer, PostgreSQL, MongoDB
    public string ConnectionString { get; set; } = string.Empty;
    public Dictionary<string, string> Settings { get; set; } = new();
    public bool IsActive { get; set; } = true;
}

public class ArchivalConfig
{
    public string TenantId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public int RetentionDays { get; set; } = 90;
    public string ArchivalProvider { get; set; } = "None"; // None, S3, AzureBlob, GoogleCloud
    public string ArchivalPath { get; set; } = string.Empty;
    public bool AutoArchive { get; set; } = true;
    public Dictionary<string, string> ProviderSettings { get; set; } = new();
}

public class ThrottleConfig
{
    public string TenantId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string Behavior { get; set; } = "queue"; // reject, queue, delay, defer
    public int RequestsPerSecond { get; set; } = 10;
    public int RequestsPerMinute { get; set; } = 600;
    public int RequestsPerHour { get; set; } = 36000;
    public int QueueCapacity { get; set; } = 1000;
    public TimeSpan DelayDuration { get; set; } = TimeSpan.FromSeconds(1);
    public bool IsActive { get; set; } = true;
}

public class SetupStatus
{
    public bool IsInitialized { get; set; } = false;
    public string Version { get; set; } = "2.0.0";
    public string? RootTenantName { get; set; }
    public DateTime? InitializedAt { get; set; }
    public string? InitializedBy { get; set; }
    public Dictionary<string, bool> ComponentStatus { get; set; } = new();
}

public class WebhookRetryResult
{
    public bool Success { get; set; }
    public int StatusCode { get; set; }
    public string? Response { get; set; }
    public string? Error { get; set; }
    public TimeSpan Duration { get; set; }
    public DateTime AttemptedAt { get; set; } = DateTime.UtcNow;
}
