using Blazored.LocalStorage;
using NotificationPortal.Models;

namespace NotificationPortal.Services;

public interface IAuthService
{
    Task<bool> LoginAsync(string email, string password);
    Task LogoutAsync();
    Task<User?> GetCurrentUserAsync();
    Task<bool> IsAuthenticatedAsync();
    Task<List<string>> GetUserPermissionsAsync();
    Task<bool> HasPermissionAsync(string permission);
    event Action<User?> OnAuthStateChanged;
}

public class AuthService : IAuthService
{
    private readonly ILocalStorageService _localStorage;
    private readonly IApiService _apiService;
    private readonly ILogger<AuthService> _logger;
    private User? _currentUser;

    public event Action<User?>? OnAuthStateChanged;

    public AuthService(
        ILocalStorageService localStorage,
        IApiService apiService,
        ILogger<AuthService> logger)
    {
        _localStorage = localStorage;
        _apiService = apiService;
        _logger = logger;
    }

    public async Task<bool> LoginAsync(string email, string password)
    {
        try
        {
            // Simulate login - in real implementation, call your auth API
            var loginRequest = new { Email = email, Password = password };
            var response = await _apiService.PostAsync<dynamic>("api/auth/login", loginRequest);

            if (response != null)
            {
                // Extract token and user info from response
                var token = "simulated_jwt_token"; // response.token
                var user = new User
                {
                    Id = "user_123",
                    Email = email,
                    FirstName = "John",
                    LastName = "Doe",
                    TenantId = "default",
                    Roles = new List<string> { "Admin" },
                    IsActive = true,
                    LastLoginAt = DateTime.UtcNow
                };

                // Store token and user info
                await _localStorage.SetItemAsync("auth_token", token);
                await _localStorage.SetItemAsync("current_user", user);

                _apiService.SetAuthToken(token);
                _currentUser = user;

                OnAuthStateChanged?.Invoke(_currentUser);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Login failed for {Email}", email);
            return false;
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            await _localStorage.RemoveItemAsync("auth_token");
            await _localStorage.RemoveItemAsync("current_user");
            
            _apiService.SetAuthToken(string.Empty);
            _currentUser = null;

            OnAuthStateChanged?.Invoke(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Logout failed");
        }
    }

    public async Task<User?> GetCurrentUserAsync()
    {
        if (_currentUser != null)
            return _currentUser;

        try
        {
            var token = await _localStorage.GetItemAsync<string>("auth_token");
            if (!string.IsNullOrEmpty(token))
            {
                _currentUser = await _localStorage.GetItemAsync<User>("current_user");
                if (_currentUser != null)
                {
                    _apiService.SetAuthToken(token);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get current user");
        }

        return _currentUser;
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        var user = await GetCurrentUserAsync();
        return user != null;
    }

    public async Task<List<string>> GetUserPermissionsAsync()
    {
        var user = await GetCurrentUserAsync();
        if (user == null) return new List<string>();

        // Map roles to permissions - simplified implementation
        var permissions = new List<string>();
        
        if (user.Roles.Contains("Admin"))
        {
            permissions.AddRange(new[]
            {
                "tenants.view", "tenants.create", "tenants.edit", "tenants.delete",
                "users.view", "users.create", "users.edit", "users.delete",
                "roles.view", "roles.create", "roles.edit", "roles.delete",
                "plugins.view", "plugins.configure", "plugins.enable", "plugins.disable",
                "settings.view", "settings.edit",
                "templates.view", "templates.create", "templates.edit", "templates.delete",
                "logs.view", "audit.view", "apikeys.view", "apikeys.create", "apikeys.delete",
                "events.view", "dashboard.view"
            });
        }
        else if (user.Roles.Contains("User"))
        {
            permissions.AddRange(new[]
            {
                "dashboard.view", "templates.view", "templates.create", "templates.edit",
                "apikeys.view", "apikeys.create", "events.view"
            });
        }

        return permissions;
    }

    public async Task<bool> HasPermissionAsync(string permission)
    {
        var permissions = await GetUserPermissionsAsync();
        return permissions.Contains(permission);
    }
}

public interface IAppStateService
{
    string CurrentTenantId { get; }
    Tenant? CurrentTenant { get; }
    bool IsDarkMode { get; }
    string CurrentLanguage { get; }
    
    Task SetTenantAsync(string tenantId);
    Task SetThemeAsync(bool isDarkMode);
    Task SetLanguageAsync(string language);
    Task LoadStateAsync();
    
    event Action OnStateChanged;
}

public class AppStateService : IAppStateService
{
    private readonly ILocalStorageService _localStorage;
    private readonly ITenantService _tenantService;
    private readonly IApiService _apiService;
    private readonly ILogger<AppStateService> _logger;

    public string CurrentTenantId { get; private set; } = "default";
    public Tenant? CurrentTenant { get; private set; }
    public bool IsDarkMode { get; private set; } = false;
    public string CurrentLanguage { get; private set; } = "en";

    public event Action? OnStateChanged;

    public AppStateService(
        ILocalStorageService localStorage,
        ITenantService tenantService,
        IApiService apiService,
        ILogger<AppStateService> logger)
    {
        _localStorage = localStorage;
        _tenantService = tenantService;
        _apiService = apiService;
        _logger = logger;
    }

    public async Task SetTenantAsync(string tenantId)
    {
        try
        {
            CurrentTenantId = tenantId;
            CurrentTenant = await _tenantService.GetTenantAsync(tenantId);
            
            await _localStorage.SetItemAsync("current_tenant_id", tenantId);
            _apiService.SetTenant(tenantId);
            
            OnStateChanged?.Invoke();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set tenant {TenantId}", tenantId);
        }
    }

    public async Task SetThemeAsync(bool isDarkMode)
    {
        try
        {
            IsDarkMode = isDarkMode;
            await _localStorage.SetItemAsync("is_dark_mode", isDarkMode);
            OnStateChanged?.Invoke();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set theme");
        }
    }

    public async Task SetLanguageAsync(string language)
    {
        try
        {
            CurrentLanguage = language;
            await _localStorage.SetItemAsync("current_language", language);
            OnStateChanged?.Invoke();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set language {Language}", language);
        }
    }

    public async Task LoadStateAsync()
    {
        try
        {
            var tenantId = await _localStorage.GetItemAsync<string>("current_tenant_id");
            if (!string.IsNullOrEmpty(tenantId))
            {
                CurrentTenantId = tenantId;
                CurrentTenant = await _tenantService.GetTenantAsync(tenantId);
                _apiService.SetTenant(tenantId);
            }

            IsDarkMode = await _localStorage.GetItemAsync<bool>("is_dark_mode");
            
            var language = await _localStorage.GetItemAsync<string>("current_language");
            if (!string.IsNullOrEmpty(language))
            {
                CurrentLanguage = language;
            }

            OnStateChanged?.Invoke();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load app state");
        }
    }
}
