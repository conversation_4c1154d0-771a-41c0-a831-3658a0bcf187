﻿"restore":{"projectUniqueName":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj","projectName":"NotifyMaster.Database","projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj","outputPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Entities\\NotifyMaster.Entities.csproj":{"projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Entities\\NotifyMaster.Entities.csproj"},"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj":{"projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"10.0.100"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.EntityFrameworkCore":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.Design":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.InMemory":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.Tools":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[9.0.6, )"},"Npgsql.EntityFrameworkCore.PostgreSQL":{"target":"Package","version":"[9.0.4, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.NETCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[9.0.5, 9.0.5]"}],"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json"}}