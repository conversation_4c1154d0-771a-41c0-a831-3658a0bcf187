{"sourceFile": "src/Core/NotifyMaster.Core/Models/UserModels.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1751232526123, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751232686562, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -240,4 +240,15 @@\n \n     [MaxLength(500)]\n     public string? Description { get; set; }\n }\n+\n+/// <summary>\n+/// System role constants\n+/// </summary>\n+public static class SystemRoles\n+{\n+    public const string SuperAdmin = \"SuperAdmin\";\n+    public const string SystemAdmin = \"SystemAdmin\";\n+    public const string TenantAdmin = \"TenantAdmin\";\n+    public const string User = \"User\";\n+}\n"}], "date": 1751232526123, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Core.Models;\n\n/// <summary>\n/// Represents a user in the system\n/// </summary>\npublic class User\n{\n    public string Id { get; set; } = Guid.NewGuid().ToString();\n    \n    [Required]\n    public string TenantId { get; set; } = string.Empty;\n    \n    [Required, MaxLength(100)]\n    public string Username { get; set; } = string.Empty;\n    \n    [Required, Max<PERSON>ength(255), EmailAddress]\n    public string Email { get; set; } = string.Empty;\n    \n    [MaxLength(100)]\n    public string? FirstName { get; set; }\n    \n    [MaxLength(100)]\n    public string? LastName { get; set; }\n    \n    [Required]\n    public string PasswordHash { get; set; } = string.Empty;\n    \n    public UserStatus Status { get; set; } = UserStatus.Active;\n    \n    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;\n    \n    public DateTime? UpdatedAt { get; set; }\n    \n    public DateTime? LastLoginAt { get; set; }\n    \n    public string? CreatedBy { get; set; }\n    \n    public string? UpdatedBy { get; set; }\n    \n    public Dictionary<string, object> Profile { get; set; } = new();\n    \n    // Navigation properties\n    public Tenant Tenant { get; set; } = null!;\n    public List<UserRole> Roles { get; set; } = new();\n    public List<UserPermission> Permissions { get; set; } = new();\n}\n\n/// <summary>\n/// User status enumeration\n/// </summary>\npublic enum UserStatus\n{\n    Active = 0,\n    Inactive = 1,\n    Suspended = 2,\n    PendingActivation = 3,\n    Deleted = 4\n}\n\n/// <summary>\n/// Represents a role in the system\n/// </summary>\npublic class Role\n{\n    public string Id { get; set; } = Guid.NewGuid().ToString();\n    \n    [Required, MaxLength(100)]\n    public string Name { get; set; } = string.Empty;\n    \n    [MaxLength(500)]\n    public string? Description { get; set; }\n    \n    public string? TenantId { get; set; }\n    \n    public RoleScope Scope { get; set; } = RoleScope.Tenant;\n    \n    public bool IsSystemRole { get; set; } = false;\n    \n    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;\n    \n    public DateTime? UpdatedAt { get; set; }\n    \n    public string? CreatedBy { get; set; }\n    \n    public string? UpdatedBy { get; set; }\n    \n    // Navigation properties\n    public List<UserRole> Users { get; set; } = new();\n    public List<RolePermission> Permissions { get; set; } = new();\n}\n\n/// <summary>\n/// Role scope enumeration\n/// </summary>\npublic enum RoleScope\n{\n    System = 0,\n    Tenant = 1\n}\n\n/// <summary>\n/// Represents a permission in the system\n/// </summary>\npublic class Permission\n{\n    public string Id { get; set; } = Guid.NewGuid().ToString();\n    \n    [Required, MaxLength(100)]\n    public string Resource { get; set; } = string.Empty;\n    \n    [Required, MaxLength(100)]\n    public string Action { get; set; } = string.Empty;\n    \n    [MaxLength(500)]\n    public string? Description { get; set; }\n    \n    public bool IsSystemPermission { get; set; } = false;\n    \n    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;\n    \n    public string? CreatedBy { get; set; }\n    \n    // Navigation properties\n    public List<UserPermission> Users { get; set; } = new();\n    public List<RolePermission> Roles { get; set; } = new();\n}\n\n/// <summary>\n/// Many-to-many relationship between users and roles\n/// </summary>\npublic class UserRole\n{\n    public string UserId { get; set; } = string.Empty;\n    public string RoleId { get; set; } = string.Empty;\n    public string? TenantId { get; set; }\n    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;\n    public string? AssignedBy { get; set; }\n    \n    // Navigation properties\n    public User User { get; set; } = null!;\n    public Role Role { get; set; } = null!;\n}\n\n/// <summary>\n/// Many-to-many relationship between users and permissions\n/// </summary>\npublic class UserPermission\n{\n    public string UserId { get; set; } = string.Empty;\n    public string PermissionId { get; set; } = string.Empty;\n    public string? TenantId { get; set; }\n    public DateTime GrantedAt { get; set; } = DateTime.UtcNow;\n    public string? GrantedBy { get; set; }\n    \n    // Navigation properties\n    public User User { get; set; } = null!;\n    public Permission Permission { get; set; } = null!;\n}\n\n/// <summary>\n/// Many-to-many relationship between roles and permissions\n/// </summary>\npublic class RolePermission\n{\n    public string RoleId { get; set; } = string.Empty;\n    public string PermissionId { get; set; } = string.Empty;\n    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;\n    public string? AssignedBy { get; set; }\n\n    // Navigation properties\n    public Role Role { get; set; } = null!;\n    public Permission Permission { get; set; } = null!;\n}\n\n/// <summary>\n/// Request model for creating a role\n/// </summary>\npublic class CreateRoleRequest\n{\n    [Required, MaxLength(100)]\n    public string Name { get; set; } = string.Empty;\n\n    [MaxLength(500)]\n    public string? Description { get; set; }\n\n    public string? TenantId { get; set; }\n\n    public RoleScope Scope { get; set; } = RoleScope.Tenant;\n\n    public List<string> PermissionIds { get; set; } = new();\n\n    public string? CreatedBy { get; set; }\n}\n\n/// <summary>\n/// Request model for updating a role\n/// </summary>\npublic class UpdateRoleRequest\n{\n    [MaxLength(100)]\n    public string? Name { get; set; }\n\n    [MaxLength(500)]\n    public string? Description { get; set; }\n\n    public List<string>? PermissionIds { get; set; }\n\n    public string? UpdatedBy { get; set; }\n}\n\n/// <summary>\n/// Request model for creating a permission\n/// </summary>\npublic class CreatePermissionRequest\n{\n    [Required, MaxLength(100)]\n    public string Resource { get; set; } = string.Empty;\n\n    [Required, MaxLength(100)]\n    public string Action { get; set; } = string.Empty;\n\n    [MaxLength(500)]\n    public string? Description { get; set; }\n\n    public string? CreatedBy { get; set; }\n}\n\n/// <summary>\n/// Request model for updating a permission\n/// </summary>\npublic class UpdatePermissionRequest\n{\n    [MaxLength(100)]\n    public string? Resource { get; set; }\n\n    [MaxLength(100)]\n    public string? Action { get; set; }\n\n    [MaxLength(500)]\n    public string? Description { get; set; }\n}\n"}]}