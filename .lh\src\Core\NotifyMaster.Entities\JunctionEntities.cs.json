{"sourceFile": "src/Core/NotifyMaster.Entities/JunctionEntities.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1751237907432, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751237920621, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -38,9 +38,9 @@\n             TenantId = userRole.TenantId,\n             AssignedAt = userRole.AssignedAt,\n             AssignedBy = userRole.AssignedBy\n         };\n-    }0\n+    }\n     \n     // Implicit conversion from Entity to Core model\n     public static implicit operator UserRole(UserRoleEntity entity)\n     {\n"}, {"date": 1751239147428, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,34 +26,9 @@\n     \n     // Navigation properties\n     public virtual UserEntity User { get; set; } = null!;\n     public virtual RoleEntity Role { get; set; } = null!;\n-    \n-    // Implicit conversion from Core model to Entity\n-    public static implicit operator UserRoleEntity(UserRole userRole)\n-    {\n-        return new UserRoleEntity\n-        {\n-            UserId = userRole.UserId,\n-            RoleId = userRole.RoleId,\n-            TenantId = userRole.TenantId,\n-            AssignedAt = userRole.AssignedAt,\n-            AssignedBy = userRole.AssignedBy\n-        };\n-    }\n-    \n-    // Implicit conversion from Entity to Core model\n-    public static implicit operator UserRole(UserRoleEntity entity)\n-    {\n-        return new UserRole\n-        {\n-            UserId = entity.UserId,\n-            RoleId = entity.RoleId,\n-            TenantId = entity.TenantId,\n-            AssignedAt = entity.AssignedAt,\n-            AssignedBy = entity.AssignedBy\n-        };\n-    }\n+\n }\n \n /// <summary>\n /// Database entity for UserPermission junction table\n"}, {"date": 1751239165992, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -54,34 +54,9 @@\n     \n     // Navigation properties\n     public virtual UserEntity User { get; set; } = null!;\n     public virtual PermissionEntity Permission { get; set; } = null!;\n-    \n-    // Implicit conversion from Core model to Entity\n-    public static implicit operator UserPermissionEntity(UserPermission userPermission)\n-    {\n-        return new UserPermissionEntity\n-        {\n-            UserId = userPermission.UserId,\n-            PermissionId = userPermission.PermissionId,\n-            TenantId = userPermission.TenantId,\n-            GrantedAt = userPermission.GrantedAt,\n-            GrantedBy = userPermission.GrantedBy\n-        };\n-    }\n-    \n-    // Implicit conversion from Entity to Core model\n-    public static implicit operator UserPermission(UserPermissionEntity entity)\n-    {\n-        return new UserPermission\n-        {\n-            UserId = entity.UserId,\n-            PermissionId = entity.PermissionId,\n-            TenantId = entity.TenantId,\n-            GrantedAt = entity.GrantedAt,\n-            GrantedBy = entity.GrantedBy\n-        };\n-    }\n+\n }\n \n /// <summary>\n /// Database entity for RolePermission junction table\n"}, {"date": 1751239186711, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -79,32 +79,9 @@\n     \n     // Navigation properties\n     public virtual RoleEntity Role { get; set; } = null!;\n     public virtual PermissionEntity Permission { get; set; } = null!;\n-    \n-    // Implicit conversion from Core model to Entity\n-    public static implicit operator RolePermissionEntity(RolePermission rolePermission)\n-    {\n-        return new RolePermissionEntity\n-        {\n-            RoleId = rolePermission.RoleId,\n-            PermissionId = rolePermission.PermissionId,\n-            AssignedAt = rolePermission.AssignedAt,\n-            AssignedBy = rolePermission.AssignedBy\n-        };\n-    }\n-    \n-    // Implicit conversion from Entity to Core model\n-    public static implicit operator RolePermission(RolePermissionEntity entity)\n-    {\n-        return new RolePermission\n-        {\n-            RoleId = entity.RoleId,\n-            PermissionId = entity.PermissionId,\n-            AssignedAt = entity.AssignedAt,\n-            AssignedBy = entity.AssignedBy\n-        };\n-    }\n+\n }\n \n /// <summary>\n /// Database entity for TenantPlugin\n"}, {"date": 1751239220617, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -114,41 +114,6 @@\n     public string? ConfiguredBy { get; set; }\n     \n     // Navigation properties\n     public virtual TenantEntity Tenant { get; set; } = null!;\n-    \n-    // Implicit conversion from Core model to Entity\n-    public static implicit operator TenantPluginEntity(TenantPlugin tenantPlugin)\n-    {\n-        return new TenantPluginEntity\n-        {\n-            Id = tenantPlugin.Id,\n-            TenantId = tenantPlugin.TenantId,\n-            PluginName = tenantPlugin.PluginName,\n-            IsEnabled = tenantPlugin.IsEnabled,\n-            Configuration = System.Text.Json.JsonSerializer.Serialize(tenantPlugin.Configuration),\n-            ConfiguredAt = tenantPlugin.ConfiguredAt,\n-            UpdatedAt = tenantPlugin.UpdatedAt,\n-            ConfiguredBy = tenantPlugin.ConfiguredBy\n-        };\n-    }\n-    \n-    // Implicit conversion from Entity to Core model\n-    public static implicit operator TenantPlugin(TenantPluginEntity entity)\n-    {\n-        var configuration = string.IsNullOrEmpty(entity.Configuration) ? \n-            new Dictionary<string, object>() : \n-            System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(entity.Configuration) ?? new Dictionary<string, object>();\n-        \n-        return new TenantPlugin\n-        {\n-            Id = entity.Id,\n-            TenantId = entity.TenantId,\n-            PluginName = entity.PluginName,\n-            IsEnabled = entity.IsEnabled,\n-            Configuration = configuration,\n-            ConfiguredAt = entity.ConfiguredAt,\n-            UpdatedAt = entity.UpdatedAt,\n-            ConfiguredBy = entity.ConfiguredBy\n-        };\n-    }\n+\n }\n"}], "date": 1751237907432, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Entities;\n\n/// <summary>\n/// Database entity for UserRole junction table\n/// </summary>\n[Table(\"UserRoles\")]\npublic class UserRoleEntity\n{\n    [Key]\n    [MaxLength(36)]\n    public string UserId { get; set; } = string.Empty;\n    \n    [Key]\n    [MaxLength(36)]\n    public string RoleId { get; set; } = string.Empty;\n    \n    [MaxLength(36)]\n    public string? TenantId { get; set; }\n    \n    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;\n    \n    [MaxLength(36)]\n    public string? AssignedBy { get; set; }\n    \n    // Navigation properties\n    public virtual UserEntity User { get; set; } = null!;\n    public virtual RoleEntity Role { get; set; } = null!;\n    \n    // Implicit conversion from Core model to Entity\n    public static implicit operator UserRoleEntity(UserRole userRole)\n    {\n        return new UserRoleEntity\n        {\n            UserId = userRole.UserId,\n            RoleId = userRole.RoleId,\n            TenantId = userRole.TenantId,\n            AssignedAt = userRole.AssignedAt,\n            AssignedBy = userRole.AssignedBy\n        };\n    }0\n    \n    // Implicit conversion from Entity to Core model\n    public static implicit operator UserRole(UserRoleEntity entity)\n    {\n        return new UserRole\n        {\n            UserId = entity.UserId,\n            RoleId = entity.RoleId,\n            TenantId = entity.TenantId,\n            AssignedAt = entity.AssignedAt,\n            AssignedBy = entity.AssignedBy\n        };\n    }\n}\n\n/// <summary>\n/// Database entity for UserPermission junction table\n/// </summary>\n[Table(\"UserPermissions\")]\npublic class UserPermissionEntity\n{\n    [Key]\n    [MaxLength(36)]\n    public string UserId { get; set; } = string.Empty;\n    \n    [Key]\n    [MaxLength(36)]\n    public string PermissionId { get; set; } = string.Empty;\n    \n    [MaxLength(36)]\n    public string? TenantId { get; set; }\n    \n    public DateTime GrantedAt { get; set; } = DateTime.UtcNow;\n    \n    [MaxLength(36)]\n    public string? GrantedBy { get; set; }\n    \n    // Navigation properties\n    public virtual UserEntity User { get; set; } = null!;\n    public virtual PermissionEntity Permission { get; set; } = null!;\n    \n    // Implicit conversion from Core model to Entity\n    public static implicit operator UserPermissionEntity(UserPermission userPermission)\n    {\n        return new UserPermissionEntity\n        {\n            UserId = userPermission.UserId,\n            PermissionId = userPermission.PermissionId,\n            TenantId = userPermission.TenantId,\n            GrantedAt = userPermission.GrantedAt,\n            GrantedBy = userPermission.GrantedBy\n        };\n    }\n    \n    // Implicit conversion from Entity to Core model\n    public static implicit operator UserPermission(UserPermissionEntity entity)\n    {\n        return new UserPermission\n        {\n            UserId = entity.UserId,\n            PermissionId = entity.PermissionId,\n            TenantId = entity.TenantId,\n            GrantedAt = entity.GrantedAt,\n            GrantedBy = entity.GrantedBy\n        };\n    }\n}\n\n/// <summary>\n/// Database entity for RolePermission junction table\n/// </summary>\n[Table(\"RolePermissions\")]\npublic class RolePermissionEntity\n{\n    [Key]\n    [MaxLength(36)]\n    public string RoleId { get; set; } = string.Empty;\n    \n    [Key]\n    [MaxLength(36)]\n    public string PermissionId { get; set; } = string.Empty;\n    \n    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;\n    \n    [MaxLength(36)]\n    public string? AssignedBy { get; set; }\n    \n    // Navigation properties\n    public virtual RoleEntity Role { get; set; } = null!;\n    public virtual PermissionEntity Permission { get; set; } = null!;\n    \n    // Implicit conversion from Core model to Entity\n    public static implicit operator RolePermissionEntity(RolePermission rolePermission)\n    {\n        return new RolePermissionEntity\n        {\n            RoleId = rolePermission.RoleId,\n            PermissionId = rolePermission.PermissionId,\n            AssignedAt = rolePermission.AssignedAt,\n            AssignedBy = rolePermission.AssignedBy\n        };\n    }\n    \n    // Implicit conversion from Entity to Core model\n    public static implicit operator RolePermission(RolePermissionEntity entity)\n    {\n        return new RolePermission\n        {\n            RoleId = entity.RoleId,\n            PermissionId = entity.PermissionId,\n            AssignedAt = entity.AssignedAt,\n            AssignedBy = entity.AssignedBy\n        };\n    }\n}\n\n/// <summary>\n/// Database entity for TenantPlugin\n/// </summary>\n[Table(\"TenantPlugins\")]\npublic class TenantPluginEntity\n{\n    [Key]\n    [MaxLength(36)]\n    public string Id { get; set; } = string.Empty;\n    \n    [Required]\n    [MaxLength(36)]\n    public string TenantId { get; set; } = string.Empty;\n    \n    [Required]\n    [MaxLength(100)]\n    public string PluginName { get; set; } = string.Empty;\n    \n    public bool IsEnabled { get; set; } = true;\n    \n    [Column(TypeName = \"jsonb\")]\n    public string Configuration { get; set; } = \"{}\";\n    \n    public DateTime ConfiguredAt { get; set; } = DateTime.UtcNow;\n    \n    public DateTime? UpdatedAt { get; set; }\n    \n    [MaxLength(36)]\n    public string? ConfiguredBy { get; set; }\n    \n    // Navigation properties\n    public virtual TenantEntity Tenant { get; set; } = null!;\n    \n    // Implicit conversion from Core model to Entity\n    public static implicit operator TenantPluginEntity(TenantPlugin tenantPlugin)\n    {\n        return new TenantPluginEntity\n        {\n            Id = tenantPlugin.Id,\n            TenantId = tenantPlugin.TenantId,\n            PluginName = tenantPlugin.PluginName,\n            IsEnabled = tenantPlugin.IsEnabled,\n            Configuration = System.Text.Json.JsonSerializer.Serialize(tenantPlugin.Configuration),\n            ConfiguredAt = tenantPlugin.ConfiguredAt,\n            UpdatedAt = tenantPlugin.UpdatedAt,\n            ConfiguredBy = tenantPlugin.ConfiguredBy\n        };\n    }\n    \n    // Implicit conversion from Entity to Core model\n    public static implicit operator TenantPlugin(TenantPluginEntity entity)\n    {\n        var configuration = string.IsNullOrEmpty(entity.Configuration) ? \n            new Dictionary<string, object>() : \n            System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(entity.Configuration) ?? new Dictionary<string, object>();\n        \n        return new TenantPlugin\n        {\n            Id = entity.Id,\n            TenantId = entity.TenantId,\n            PluginName = entity.PluginName,\n            IsEnabled = entity.IsEnabled,\n            Configuration = configuration,\n            ConfiguredAt = entity.ConfiguredAt,\n            UpdatedAt = entity.UpdatedAt,\n            ConfiguredBy = entity.ConfiguredBy\n        };\n    }\n}\n"}]}