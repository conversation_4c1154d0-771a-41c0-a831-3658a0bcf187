// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Service for managing notification queue operations
/// </summary>
public interface INotificationQueueService
{
    /// <summary>
    /// Add a notification to the queue
    /// </summary>
    /// <param name="item">The notification item to queue</param>
    /// <returns>Queue ID for tracking</returns>
    Task<string> QueueNotificationAsync(NotificationQueueItem item);
    
    /// <summary>
    /// Dequeue the next notification for processing
    /// </summary>
    /// <returns>Next notification item or null if queue is empty</returns>
    Task<NotificationQueueItem?> DequeueNotificationAsync();
    
    /// <summary>
    /// Get pending notifications from the queue
    /// </summary>
    /// <param name="count">Maximum number of items to retrieve</param>
    /// <returns>List of pending notifications</returns>
    Task<List<NotificationQueueItem>> GetPendingNotificationsAsync(int count = 10);
    
    /// <summary>
    /// Mark a notification as successfully processed
    /// </summary>
    /// <param name="queueId">Queue ID of the processed notification</param>
    Task MarkNotificationProcessedAsync(string queueId);
    
    /// <summary>
    /// Mark a notification as failed
    /// </summary>
    /// <param name="queueId">Queue ID of the failed notification</param>
    /// <param name="errorMessage">Error message describing the failure</param>
    /// <param name="retryCount">Current retry count</param>
    Task MarkNotificationFailedAsync(string queueId, string errorMessage, int retryCount = 0);
    
    /// <summary>
    /// Get the current queue length
    /// </summary>
    /// <returns>Number of items in queue</returns>
    Task<int> GetQueueLengthAsync();
    
    /// <summary>
    /// Get the number of items currently being processed
    /// </summary>
    /// <returns>Number of items being processed</returns>
    Task<int> GetProcessingCountAsync();
    
    /// <summary>
    /// Get the number of failed items
    /// </summary>
    /// <returns>Number of failed items</returns>
    Task<int> GetFailedCountAsync();
    
    /// <summary>
    /// Get failed notifications
    /// </summary>
    /// <param name="count">Maximum number of failed items to retrieve</param>
    /// <returns>List of failed notifications</returns>
    Task<List<NotificationQueueItem>> GetFailedNotificationsAsync(int count = 10);
    
    /// <summary>
    /// Requeue a failed notification for retry
    /// </summary>
    /// <param name="queueId">Queue ID of the failed notification</param>
    Task RequeueFailedNotificationAsync(string queueId);
    
    /// <summary>
    /// Retry all failed notifications
    /// </summary>
    /// <returns>Number of notifications requeued</returns>
    Task<int> RetryFailedNotificationsAsync();
    
    /// <summary>
    /// Clear all failed notifications
    /// </summary>
    /// <returns>Number of notifications cleared</returns>
    Task<int> ClearFailedNotificationsAsync();
}
