// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Interface for queue service operations
/// </summary>
public interface IQueueService
{
    /// <summary>
    /// Enqueues a message for processing
    /// </summary>
    Task<OperationResult<string>> EnqueueAsync<T>(string queueName, T message, CancellationToken cancellationToken = default) where T : class;
    
    /// <summary>
    /// Enqueues a message with delay
    /// </summary>
    Task<OperationResult<string>> EnqueueDelayedAsync<T>(string queueName, T message, TimeSpan delay, CancellationToken cancellationToken = default) where T : class;
    
    /// <summary>
    /// Enqueues a message to be processed at a specific time
    /// </summary>
    Task<OperationResult<string>> EnqueueScheduledAsync<T>(string queueName, T message, DateTimeOffset scheduleAt, CancellationToken cancellationToken = default) where T : class;
    
    /// <summary>
    /// Dequeues and processes the next message from a queue
    /// </summary>
    Task<OperationResult<T?>> DequeueAsync<T>(string queueName, CancellationToken cancellationToken = default) where T : class;
    
    /// <summary>
    /// Gets the count of messages in a queue
    /// </summary>
    Task<OperationResult<int>> GetQueueCountAsync(string queueName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Purges all messages from a queue
    /// </summary>
    Task<OperationResult> PurgeQueueAsync(string queueName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Cancels a scheduled message
    /// </summary>
    Task<OperationResult> CancelScheduledMessageAsync(string messageId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets queue statistics
    /// </summary>
    Task<OperationResult<QueueStatistics>> GetQueueStatisticsAsync(string queueName, CancellationToken cancellationToken = default);
}

/// <summary>
/// Queue statistics model
/// </summary>
public class QueueStatistics
{
    public string QueueName { get; set; } = string.Empty;
    public int PendingMessages { get; set; }
    public int ProcessingMessages { get; set; }
    public int CompletedMessages { get; set; }
    public int FailedMessages { get; set; }
    public DateTime LastActivity { get; set; }
    public double AverageProcessingTime { get; set; }
    public Dictionary<string, object> CustomMetrics { get; set; } = new();
}

/// <summary>
/// Queue message wrapper
/// </summary>
public class QueueMessage<T> where T : class
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string QueueName { get; set; } = string.Empty;
    public T Payload { get; set; } = null!;
    public DateTime EnqueuedAt { get; set; } = DateTime.UtcNow;
    public DateTime? ScheduledAt { get; set; }
    public int RetryCount { get; set; }
    public int MaxRetries { get; set; } = 3;
    public string? CorrelationId { get; set; }
    public string? TenantId { get; set; }
    public string? UserId { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
    public QueueMessageStatus Status { get; set; } = QueueMessageStatus.Pending;
    public DateTime? ProcessedAt { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Queue message status enumeration
/// </summary>
public enum QueueMessageStatus
{
    Pending = 0,
    Processing = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4,
    Scheduled = 5
}

/// <summary>
/// Queue names constants
/// </summary>
public static class QueueNames
{
    public const string MessageSending = "message-sending";
    public const string MessageProcessing = "message-processing";
    public const string UserOperations = "user-operations";
    public const string TenantOperations = "tenant-operations";
    public const string PluginOperations = "plugin-operations";
    public const string NotificationDelivery = "notification-delivery";
    public const string WebhookDelivery = "webhook-delivery";
    public const string EmailProcessing = "email-processing";
    public const string SmsProcessing = "sms-processing";
    public const string PushProcessing = "push-processing";
    public const string ReportGeneration = "report-generation";
    public const string DataExport = "data-export";
    public const string SystemMaintenance = "system-maintenance";
}
