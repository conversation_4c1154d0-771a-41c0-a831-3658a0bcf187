namespace PluginCore.Models;

/// <summary>
/// Result of template rendering operation with validation details
/// </summary>
public class TemplateRenderResult
{
    /// <summary>
    /// Whether the rendering was successful
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// The rendered content after variable substitution
    /// </summary>
    public string RenderedContent { get; set; } = string.Empty;
    
    /// <summary>
    /// List of errors encountered during rendering
    /// </summary>
    public List<string> Errors { get; set; } = new();
    
    /// <summary>
    /// List of warnings generated during rendering
    /// </summary>
    public List<string> Warnings { get; set; } = new();
    
    /// <summary>
    /// List of variables that were successfully substituted
    /// </summary>
    public List<string> UsedVariables { get; set; } = new();
    
    /// <summary>
    /// List of variables referenced in template but not provided
    /// </summary>
    public List<string> MissingVariables { get; set; } = new();
}
