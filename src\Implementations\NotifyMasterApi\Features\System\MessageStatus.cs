using NotifyMaster.Core.Interfaces;
using NotifyMasterApi.Documentation;

namespace NotifyMasterApi.Features.System;

/// <summary>
/// Request model for checking message status
/// </summary>
public class MessageStatusRequest
{
    /// <summary>
    /// Correlation ID of the message to check
    /// </summary>
    /// <example>corr_abc123def456</example>
    [Required(ErrorMessage = "Correlation ID is required")]
    public string CorrelationId { get; set; } = string.Empty;
}

/// <summary>
/// Response model for message status
/// </summary>
public class MessageStatusResponse
{
    /// <summary>
    /// Correlation ID of the message
    /// </summary>
    /// <example>corr_abc123def456</example>
    public string CorrelationId { get; set; } = string.Empty;

    /// <summary>
    /// Current status of the message
    /// </summary>
    /// <example>Queued, Processing, Completed, Failed</example>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Message type (Email, SMS, Push, etc.)
    /// </summary>
    /// <example>Email</example>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Recipient of the message
    /// </summary>
    /// <example><EMAIL></example>
    public string Recipient { get; set; } = string.Empty;

    /// <summary>
    /// When the message was queued
    /// </summary>
    /// <example>2024-01-15T10:30:00Z</example>
    public DateTime QueuedAt { get; set; }

    /// <summary>
    /// When processing started (if applicable)
    /// </summary>
    /// <example>2024-01-15T10:30:05Z</example>
    public DateTime? ProcessingStartedAt { get; set; }

    /// <summary>
    /// When processing completed (if applicable)
    /// </summary>
    /// <example>2024-01-15T10:30:10Z</example>
    public DateTime? ProcessingCompletedAt { get; set; }

    /// <summary>
    /// Error message if the operation failed
    /// </summary>
    /// <example>Invalid email address format</example>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Additional metadata about the message
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Endpoint for checking message status by correlation ID
/// </summary>
public class MessageStatusEndpoint : Endpoint<MessageStatusRequest, MessageStatusResponse>
{
    private readonly ILogger<MessageStatusEndpoint> _logger;
    // Note: In a real implementation, you'd inject a service to query the database

    public MessageStatusEndpoint(ILogger<MessageStatusEndpoint> logger)
    {
        _logger = logger;
    }

    public override void Configure()
    {
        this.ConfigureNotificationEndpoint(
            "GET",
            "/api/messages/{CorrelationId}/status",
            "Get Message Status",
            "Check the status of a queued message using its correlation ID.\n\n" +
            "## 🎯 Features\n" +
            "- **Real-time Status**: Get current processing status\n" +
            "- **Detailed Timeline**: See when each stage was completed\n" +
            "- **Error Information**: Get detailed error messages if processing failed\n" +
            "- **Metadata Access**: View additional message information\n\n" +
            "## 📋 Status Values\n" +
            "- **Queued**: Message is waiting to be processed\n" +
            "- **Processing**: Message is currently being processed\n" +
            "- **Completed**: Message was successfully sent\n" +
            "- **Failed**: Message processing failed\n" +
            "- **Cancelled**: Message was cancelled before processing\n\n" +
            "## 🔍 Use Cases\n" +
            "- Track delivery status of important messages\n" +
            "- Debug failed message deliveries\n" +
            "- Monitor processing times and performance\n" +
            "- Implement retry logic based on status",
            "System",
            new[] { "Monitoring", "Status Tracking" }
        );
    }

    public override async Task HandleAsync(MessageStatusRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Checking status for message {CorrelationId}", req.CorrelationId);

            // TODO: Implement actual database query to get message status
            // For now, return a mock response
            var response = new MessageStatusResponse
            {
                CorrelationId = req.CorrelationId,
                Status = "Processing",
                Type = "Email",
                Recipient = "<EMAIL>",
                QueuedAt = DateTime.UtcNow.AddMinutes(-5),
                ProcessingStartedAt = DateTime.UtcNow.AddMinutes(-2),
                Metadata = new Dictionary<string, object>
                {
                    ["QueueName"] = "email-processing",
                    ["RetryCount"] = 0,
                    ["EstimatedCompletion"] = DateTime.UtcNow.AddMinutes(1)
                }
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking message status for {CorrelationId}", req.CorrelationId);
            await SendErrorsAsync(500, ct);
        }
    }
}

/// <summary>
/// Request model for getting queue statistics
/// </summary>
public class QueueStatsRequest
{
    /// <summary>
    /// Queue name to get statistics for (optional - gets all queues if not specified)
    /// </summary>
    /// <example>email-processing</example>
    public string? QueueName { get; set; }
}

/// <summary>
/// Response model for queue statistics
/// </summary>
public class QueueStatsResponse
{
    /// <summary>
    /// List of queue statistics
    /// </summary>
    public List<QueueStatInfo> Queues { get; set; } = new();

    /// <summary>
    /// Overall system statistics
    /// </summary>
    public SystemStats System { get; set; } = new();
}

/// <summary>
/// Statistics for a specific queue
/// </summary>
public class QueueStatInfo
{
    /// <summary>
    /// Name of the queue
    /// </summary>
    /// <example>email-processing</example>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Number of pending messages
    /// </summary>
    /// <example>15</example>
    public int PendingMessages { get; set; }

    /// <summary>
    /// Number of messages currently being processed
    /// </summary>
    /// <example>3</example>
    public int ProcessingMessages { get; set; }

    /// <summary>
    /// Number of completed messages (last 24 hours)
    /// </summary>
    /// <example>1250</example>
    public int CompletedMessages { get; set; }

    /// <summary>
    /// Number of failed messages (last 24 hours)
    /// </summary>
    /// <example>12</example>
    public int FailedMessages { get; set; }

    /// <summary>
    /// Average processing time in seconds
    /// </summary>
    /// <example>2.5</example>
    public double AverageProcessingTime { get; set; }

    /// <summary>
    /// Last activity timestamp
    /// </summary>
    /// <example>2024-01-15T10:30:00Z</example>
    public DateTime LastActivity { get; set; }
}

/// <summary>
/// Overall system statistics
/// </summary>
public class SystemStats
{
    /// <summary>
    /// Total messages processed today
    /// </summary>
    /// <example>5420</example>
    public int TotalMessagesToday { get; set; }

    /// <summary>
    /// Total messages in all queues
    /// </summary>
    /// <example>45</example>
    public int TotalPendingMessages { get; set; }

    /// <summary>
    /// System uptime
    /// </summary>
    /// <example>2024-01-15T08:00:00Z</example>
    public DateTime SystemStartTime { get; set; }

    /// <summary>
    /// Current system load (0.0 to 1.0)
    /// </summary>
    /// <example>0.65</example>
    public double SystemLoad { get; set; }
}

/// <summary>
/// Endpoint for getting queue statistics
/// </summary>
public class QueueStatsEndpoint : Endpoint<QueueStatsRequest, QueueStatsResponse>
{
    private readonly IQueueService _queueService;
    private readonly ILogger<QueueStatsEndpoint> _logger;

    public QueueStatsEndpoint(IQueueService queueService, ILogger<QueueStatsEndpoint> logger)
    {
        _queueService = queueService;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/system/queues/stats");
        AllowAnonymous(); // TODO: Add proper authorization
        Summary(s =>
        {
            s.Summary = "Get queue statistics";
            s.Description = "Get statistics for message queues";
            s.Responses[200] = "Statistics retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("System", "Monitoring");
    }

    public override async Task HandleAsync(QueueStatsRequest req, CancellationToken ct)
    {
        try
        {
            _logger.LogInformation("Getting queue statistics");

            var response = new QueueStatsResponse
            {
                System = new SystemStats
                {
                    TotalMessagesToday = 5420,
                    TotalPendingMessages = 45,
                    SystemStartTime = DateTime.UtcNow.AddHours(-8),
                    SystemLoad = 0.65
                }
            };

            // Get statistics for specific queue or all queues
            var queueNames = string.IsNullOrEmpty(req.QueueName) 
                ? new[] { QueueNames.EmailProcessing, QueueNames.SmsProcessing, QueueNames.PushProcessing }
                : new[] { req.QueueName };

            foreach (var queueName in queueNames)
            {
                var stats = await _queueService.GetQueueStatisticsAsync(queueName, ct);
                if (stats.IsSuccess)
                {
                    response.Queues.Add(new QueueStatInfo
                    {
                        Name = stats.Data!.QueueName,
                        PendingMessages = stats.Data.PendingMessages,
                        ProcessingMessages = stats.Data.ProcessingMessages,
                        CompletedMessages = stats.Data.CompletedMessages,
                        FailedMessages = stats.Data.FailedMessages,
                        AverageProcessingTime = stats.Data.AverageProcessingTime,
                        LastActivity = stats.Data.LastActivity
                    });
                }
            }

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting queue statistics");
            await SendErrorsAsync(500, ct);
        }
    }
}
