namespace NotifyMasterApi.Infrastructure;

/// <summary>
/// Base class for endpoints that implement queue-first architecture
/// Automatically applies queue-first interceptors and provides common functionality
/// </summary>
/// <typeparam name="TRequest">Request type</typeparam>
/// <typeparam name="TResponse">Response type</typeparam>
public abstract class QueueFirstEndpointBase<TRequest, TResponse> : Endpoint<TRequest, TResponse>
    where TRequest : notnull, new()
{
    protected readonly ILogger Logger;

    protected QueueFirstEndpointBase(ILogger logger)
    {
        Logger = logger;
    }

    public override void Configure()
    {
        // Apply queue-first interceptors
        PreProcessor<QueueFirstInterceptor>();
        PostProcessor<QueueFirstPostProcessor>();
        
        // Configure the specific endpoint
        ConfigureEndpoint();
    }

    /// <summary>
    /// Configure the specific endpoint (route, policies, documentation, etc.)
    /// </summary>
    protected abstract void ConfigureEndpoint();

    /// <summary>
    /// Get queue information from the current request context
    /// </summary>
    protected QueueInfo GetQueueInfo()
    {
        return new QueueInfo
        {
            QueueId = HttpContext.Items["QueueId"] as string,
            QueuedAt = HttpContext.Items["QueuedAt"] as DateTime?,
            CorrelationId = HttpContext.Request.Headers["X-Correlation-ID"].FirstOrDefault()
        };
    }

    /// <summary>
    /// Create a standardized success response with queue information
    /// </summary>
    protected object CreateQueuedResponse(object data, string status = "Queued")
    {
        var queueInfo = GetQueueInfo();
        
        return new
        {
            Success = true,
            Status = status,
            Data = data,
            QueueId = queueInfo.QueueId,
            QueuedAt = queueInfo.QueuedAt,
            CorrelationId = queueInfo.CorrelationId,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Create a standardized error response
    /// </summary>
    protected object CreateErrorResponse(string message, string? details = null)
    {
        var queueInfo = GetQueueInfo();
        
        return new
        {
            Success = false,
            Status = "Failed",
            Error = message,
            Details = details,
            QueueId = queueInfo.QueueId,
            CorrelationId = queueInfo.CorrelationId,
            Timestamp = DateTime.UtcNow
        };
    }
}

/// <summary>
/// Base class for endpoints that don't need queue processing (like read-only operations)
/// </summary>
/// <typeparam name="TRequest">Request type</typeparam>
/// <typeparam name="TResponse">Response type</typeparam>
public abstract class StandardEndpointBase<TRequest, TResponse> : Endpoint<TRequest, TResponse>
    where TRequest : notnull, new()
{
    protected readonly ILogger Logger;

    protected StandardEndpointBase(ILogger logger)
    {
        Logger = logger;
    }

    public override void Configure()
    {
        // Configure the specific endpoint without queue processing
        ConfigureEndpoint();
    }

    /// <summary>
    /// Configure the specific endpoint (route, policies, documentation, etc.)
    /// </summary>
    protected abstract void ConfigureEndpoint();

    /// <summary>
    /// Create a standardized success response
    /// </summary>
    protected object CreateSuccessResponse(object data, string? message = null)
    {
        return new
        {
            Success = true,
            Message = message,
            Data = data,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Create a standardized error response
    /// </summary>
    protected object CreateErrorResponse(string message, string? details = null)
    {
        return new
        {
            Success = false,
            Error = message,
            Details = details,
            Timestamp = DateTime.UtcNow
        };
    }
}

/// <summary>
/// Queue information for tracking requests
/// </summary>
public class QueueInfo
{
    public string? QueueId { get; set; }
    public DateTime? QueuedAt { get; set; }
    public string? CorrelationId { get; set; }
}

/// <summary>
/// Extension methods for endpoint configuration
/// </summary>
public static class EndpointConfigurationExtensions
{
    /// <summary>
    /// Configure a notification endpoint with standard settings
    /// </summary>
    public static void ConfigureNotificationEndpoint(
        this IEndpointDefinition endpoint,
        string method,
        string route,
        string summary,
        string description,
        string[]? tags = null)
    {
        switch (method.ToUpperInvariant())
        {
            case "GET":
                endpoint.Get(route);
                break;
            case "POST":
                endpoint.Post(route);
                break;
            case "PUT":
                endpoint.Put(route);
                break;
            case "DELETE":
                endpoint.Delete(route);
                break;
            case "PATCH":
                endpoint.Patch(route);
                break;
            default:
                throw new ArgumentException($"Unsupported HTTP method: {method}");
        }

        endpoint.AllowAnonymous(); // TODO: Add proper authentication when ready
        
        endpoint.Summary(s =>
        {
            s.Summary = summary;
            s.Description = description;
            s.Response(200, "Request processed successfully");
            s.Response(400, "Invalid request");
            s.Response(401, "Unauthorized");
            s.Response(500, "Internal server error");
        });

        if (tags != null && tags.Length > 0)
        {
            endpoint.Tags(tags);
        }
    }

    /// <summary>
    /// Configure a management endpoint with standard settings
    /// </summary>
    public static void ConfigureManagementEndpoint(
        this IEndpointDefinition endpoint,
        string method,
        string route,
        string summary,
        string description,
        string policy = "RequireTenantAdmin",
        string[]? tags = null)
    {
        switch (method.ToUpperInvariant())
        {
            case "GET":
                endpoint.Get(route);
                break;
            case "POST":
                endpoint.Post(route);
                break;
            case "PUT":
                endpoint.Put(route);
                break;
            case "DELETE":
                endpoint.Delete(route);
                break;
            case "PATCH":
                endpoint.Patch(route);
                break;
            default:
                throw new ArgumentException($"Unsupported HTTP method: {method}");
        }

        endpoint.Policies(policy);
        
        endpoint.Summary(s =>
        {
            s.Summary = summary;
            s.Description = description;
            s.Response(200, "Operation completed successfully");
            s.Response(400, "Invalid request");
            s.Response(401, "Unauthorized");
            s.Response(403, "Forbidden");
            s.Response(404, "Resource not found");
            s.Response(500, "Internal server error");
        });

        if (tags != null && tags.Length > 0)
        {
            endpoint.Tags(tags);
        }
    }
}

/// <summary>
/// Standard response models for consistent API responses
/// </summary>
public class StandardResponse<T>
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public T? Data { get; set; }
    public string? Error { get; set; }
    public string? Details { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class QueuedResponse<T> : StandardResponse<T>
{
    public string? QueueId { get; set; }
    public DateTime? QueuedAt { get; set; }
    public string? CorrelationId { get; set; }
    public string Status { get; set; } = "Queued";
}

/// <summary>
/// Attribute to mark endpoints that should skip queue processing
/// </summary>
[AttributeUsage(AttributeTargets.Class)]
public class SkipQueueProcessingAttribute : Attribute
{
}

/// <summary>
/// Attribute to mark endpoints with specific queue priority
/// </summary>
[AttributeUsage(AttributeTargets.Class)]
public class QueuePriorityAttribute : Attribute
{
    public RequestPriority Priority { get; }

    public QueuePriorityAttribute(RequestPriority priority)
    {
        Priority = priority;
    }
}

public enum RequestPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}
