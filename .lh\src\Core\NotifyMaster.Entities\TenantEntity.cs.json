{"sourceFile": "src/Core/NotifyMaster.Entities/TenantEntity.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751239046804, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751239046804, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Entities;\n\n/// <summary>\n/// Database entity for Tenant\n/// </summary>\n[Table(\"Tenants\")]\npublic class TenantEntity\n{\n    [Key]\n    [MaxLength(36)]\n    public string Id { get; set; } = string.Empty;\n    \n    [Required]\n    [MaxLength(100)]\n    public string Name { get; set; } = string.Empty;\n    \n    [MaxLength(500)]\n    public string? Description { get; set; }\n    \n    [Required]\n    [MaxLength(100)]\n    public string Domain { get; set; } = string.Empty;\n    \n    public int Status { get; set; } = 0; // TenantStatus enum as int\n    \n    public int Plan { get; set; } = 0; // TenantPlan enum as int\n    \n    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;\n    \n    public DateTime? UpdatedAt { get; set; }\n    \n    [MaxLength(36)]\n    public string? CreatedBy { get; set; }\n    \n    [Column(TypeName = \"jsonb\")]\n    public string Settings { get; set; } = \"{}\";\n    \n    // Limits as separate columns for better querying\n    public int MaxUsers { get; set; } = 10;\n    public int MaxPlugins { get; set; } = 5;\n    public int MaxMessagesPerMonth { get; set; } = 1000;\n    public int MaxStorageGB { get; set; } = 1;\n    public int MaxApiCallsPerDay { get; set; } = 10000;\n    \n    [Column(TypeName = \"jsonb\")]\n    public string CustomLimits { get; set; } = \"{}\";\n    \n    // Usage as separate columns for better querying\n    public int CurrentUsers { get; set; }\n    public int CurrentPlugins { get; set; }\n    public int MessagesThisMonth { get; set; }\n    public double StorageUsedGB { get; set; }\n    public int ApiCallsToday { get; set; }\n    public DateTime UsageLastUpdated { get; set; } = DateTime.UtcNow;\n    \n    [Column(TypeName = \"jsonb\")]\n    public string CustomUsage { get; set; } = \"{}\";\n    \n    // Navigation properties\n    public virtual ICollection<UserEntity> Users { get; set; } = new List<UserEntity>();\n    public virtual ICollection<TenantPluginEntity> Plugins { get; set; } = new List<TenantPluginEntity>();\n\n}\n"}]}