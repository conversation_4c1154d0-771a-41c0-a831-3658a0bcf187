using System.Diagnostics;

namespace NotifyMasterApi.Services;

public class NotificationProcessingService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<NotificationProcessingService> _logger;
    private readonly TimeSpan _processingInterval = TimeSpan.FromSeconds(5);

    public NotificationProcessingService(
        IServiceProvider serviceProvider,
        ILogger<NotificationProcessingService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Notification processing service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var queueService = scope.ServiceProvider.GetRequiredService<INotificationQueueService>();
                var loggingService = scope.ServiceProvider.GetRequiredService<INotificationLoggingService>();
                var emailService = scope.ServiceProvider.GetRequiredService<EmailService.Library.Interfaces.IEmailService>();
                var smsService = scope.ServiceProvider.GetRequiredService<SmsService.Library.Interfaces.ISmsService>();
                var pushService = scope.ServiceProvider.GetRequiredService<PushNotificationService.Library.Interfaces.IPushNotificationService>();

                var notification = await queueService.DequeueNotificationAsync();
                if (notification != null)
                {
                    await ProcessNotificationAsync(notification, queueService, loggingService, emailService, smsService, pushService);
                }
                else
                {
                    // No notifications to process, wait before checking again
                    await Task.Delay(_processingInterval, stoppingToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in notification processing service");
                await Task.Delay(_processingInterval, stoppingToken);
            }
        }

        _logger.LogInformation("Notification processing service stopped");
    }

    private async Task ProcessNotificationAsync(
        NotificationQueueItem notification,
        INotificationQueueService queueService,
        INotificationLoggingService loggingService,
        EmailService.Library.Interfaces.IEmailService emailService,
        SmsService.Library.Interfaces.ISmsService smsService,
        PushNotificationService.Library.Interfaces.IPushNotificationService pushService)
    {
        var stopwatch = Stopwatch.StartNew();
        var success = false;
        string? errorMessage = null;

        try
        {
            _logger.LogInformation("Processing notification {QueueId} of type {Type} for {Recipient}", 
                notification.QueueId, notification.Type, notification.Recipient);

            // Update notification status to processing
            await loggingService.UpdateNotificationStatusAsync(
                notification.MessageId,
                NotifyMasterApi.Data.Entities.NotificationStatus.Processing,
                GetProviderName(notification.Type));

            switch (notification.Type)
            {
                case NotificationType.Email:
                    success = await ProcessEmailNotificationAsync(notification, emailService, loggingService);
                    break;

                case NotificationType.Sms:
                    success = await ProcessSmsNotificationAsync(notification, smsService, loggingService);
                    break;

                case NotificationType.PushMessage:
                    success = await ProcessPushNotificationAsync(notification, pushService, loggingService);
                    break;

                default:
                    errorMessage = $"Unsupported notification type: {notification.Type}";
                    _logger.LogError(errorMessage);
                    break;
            }

            stopwatch.Stop();

            if (success)
            {
                await queueService.MarkNotificationProcessedAsync(notification.QueueId);
                await loggingService.UpdateMetricsAsync(notification.Type, GetProviderName(notification.Type), true, stopwatch.ElapsedMilliseconds);
                _logger.LogInformation("Successfully processed notification {QueueId}", notification.QueueId);
            }
            else
            {
                await queueService.MarkNotificationFailedAsync(notification.QueueId, errorMessage ?? "Unknown error", notification.RetryCount);
                await loggingService.UpdateMetricsAsync(notification.Type, GetProviderName(notification.Type), false, stopwatch.ElapsedMilliseconds);
                _logger.LogWarning("Failed to process notification {QueueId}: {ErrorMessage}", notification.QueueId, errorMessage);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            errorMessage = ex.Message;
            await queueService.MarkNotificationFailedAsync(notification.QueueId, errorMessage, notification.RetryCount);
            await loggingService.UpdateMetricsAsync(notification.Type, GetProviderName(notification.Type), false, stopwatch.ElapsedMilliseconds);
            await loggingService.LogErrorAsync(notification.Type, GetProviderName(notification.Type), "PROCESSING_ERROR", errorMessage, notification.Recipient, notification.MessageId, ex.StackTrace);
            _logger.LogError(ex, "Exception processing notification {QueueId}", notification.QueueId);
        }
    }

    private async Task<bool> ProcessEmailNotificationAsync(NotificationQueueItem notification, EmailService.Library.Interfaces.IEmailService emailService, INotificationLoggingService loggingService)
    {
        try
        {
            var emailRequest = new EmailMessageRequest
            {
                To = notification.Recipient,
                Subject = notification.Subject,
                Body = notification.Content,
                IsHtml = true
            };

            var result = await emailService.SendAsync(emailRequest);
            
            await loggingService.UpdateNotificationStatusAsync(
                notification.MessageId,
                result.IsSuccess ? NotifyMasterApi.Data.Entities.NotificationStatus.Sent : NotifyMasterApi.Data.Entities.NotificationStatus.Failed,
                "EmailService",
                result.IsSuccess ? null : result.ErrorMessage,
                result.IsSuccess ? "Email sent successfully" : null);

            if (!result.IsSuccess)
            {
                await loggingService.LogErrorAsync(NotificationType.Email, "EmailService", "SEND_FAILED", result.ErrorMessage ?? "Unknown error", notification.Recipient, notification.MessageId);
            }

            return result.IsSuccess;
        }
        catch (Exception ex)
        {
            await loggingService.LogErrorAsync(NotificationType.Email, "EmailService", "EXCEPTION", ex.Message, notification.Recipient, notification.MessageId, ex.StackTrace);
            return false;
        }
    }

    private async Task<bool> ProcessSmsNotificationAsync(NotificationQueueItem notification, SmsService.Library.Interfaces.ISmsService smsService, INotificationLoggingService loggingService)
    {
        try
        {
            var smsRequest = new SmsMessageRequest
            {
                PhoneNumber = notification.Recipient,
                Message = notification.Content
            };

            var result = await smsService.SendAsync(smsRequest);
            
            await loggingService.UpdateNotificationStatusAsync(
                notification.MessageId,
                result.IsSuccess ? NotifyMasterApi.Data.Entities.NotificationStatus.Sent : NotifyMasterApi.Data.Entities.NotificationStatus.Failed,
                "SmsService",
                result.IsSuccess ? null : result.ErrorMessage,
                result.IsSuccess ? "SMS sent successfully" : null);

            if (!result.IsSuccess)
            {
                await loggingService.LogErrorAsync(NotificationType.Sms, "SmsService", "SEND_FAILED", result.ErrorMessage ?? "Unknown error", notification.Recipient, notification.MessageId);
            }

            return result.IsSuccess;
        }
        catch (Exception ex)
        {
            await loggingService.LogErrorAsync(NotificationType.Sms, "SmsService", "EXCEPTION", ex.Message, notification.Recipient, notification.MessageId, ex.StackTrace);
            return false;
        }
    }

    private async Task<bool> ProcessPushNotificationAsync(NotificationQueueItem notification, PushNotificationService.Library.Interfaces.IPushNotificationService pushService, INotificationLoggingService loggingService)
    {
        try
        {
            var pushRequest = new PushMessageRequest
            {
                DeviceToken = notification.Recipient,
                Title = notification.Subject,
                Body = notification.Content
            };

            var result = await pushService.SendAsync(pushRequest);
            
            await loggingService.UpdateNotificationStatusAsync(
                notification.MessageId,
                result.IsSuccess ? NotifyMasterApi.Data.Entities.NotificationStatus.Sent : NotifyMasterApi.Data.Entities.NotificationStatus.Failed,
                "PushService",
                result.IsSuccess ? null : result.ErrorMessage,
                result.IsSuccess ? "Push notification sent successfully" : null);

            if (!result.IsSuccess)
            {
                await loggingService.LogErrorAsync(NotificationType.PushMessage, "PushService", "SEND_FAILED", result.ErrorMessage ?? "Unknown error", notification.Recipient, notification.MessageId);
            }

            return result.IsSuccess;
        }
        catch (Exception ex)
        {
            await loggingService.LogErrorAsync(NotificationType.PushMessage, "PushService", "EXCEPTION", ex.Message, notification.Recipient, notification.MessageId, ex.StackTrace);
            return false;
        }
    }

    private string GetProviderName(NotificationType type)
    {
        return type switch
        {
            NotificationType.Email => "EmailService",
            NotificationType.Sms => "SmsService",
            NotificationType.PushMessage => "PushService",
            _ => "Unknown"
        };
    }
}
