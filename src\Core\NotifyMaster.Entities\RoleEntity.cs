// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Entities;

/// <summary>
/// Database entity for Role
/// </summary>
[Table("Roles")]
public class RoleEntity
{
    [Key]
    [MaxLength(36)]
    public string Id { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [MaxLength(36)]
    public string? TenantId { get; set; }
    
    public int Scope { get; set; } = 1; // RoleScope enum as int (Tenant = 1)
    
    public bool IsSystemRole { get; set; } = false;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    [MaxLength(36)]
    public string? CreatedBy { get; set; }
    
    [MaxLength(36)]
    public string? UpdatedBy { get; set; }
    
    // Navigation properties
    public virtual ICollection<UserRoleEntity> Users { get; set; } = new List<UserRoleEntity>();
    public virtual ICollection<RolePermissionEntity> Permissions { get; set; } = new List<RolePermissionEntity>();

}
