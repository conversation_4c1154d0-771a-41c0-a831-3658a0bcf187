{"sourceFile": "src/Core/NotifyMaster.Database/GlobalUsings.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1751229755442, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751230349306, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,6 +16,9 @@\n // NotifyMaster.Core namespaces\n global using NotifyMaster.Core.Interfaces;\n global using NotifyMaster.Core.Models;\n \n+// NotifyMaster.Entities namespaces\n+global using NotifyMaster.Entities;\n+\n // NotifyMaster.Database namespaces\n global using NotifyMaster.Database;\n"}, {"date": 1751237516543, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,10 +15,8 @@\n \n // NotifyMaster.Core namespaces\n global using NotifyMaster.Core.Interfaces;\n global using NotifyMaster.Core.Models;\n+global using NotifyMaster.Core;\n \n-// NotifyMaster.Entities namespaces\n-global using NotifyMaster.Entities;\n-\n // NotifyMaster.Database namespaces\n global using NotifyMaster.Database;\n"}, {"date": 1751237739597, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,6 +17,9 @@\n global using NotifyMaster.Core.Interfaces;\n global using NotifyMaster.Core.Models;\n global using NotifyMaster.Core;\n \n+// PluginCore namespaces\n+global using PluginCore.Models;\n+\n // NotifyMaster.Database namespaces\n global using NotifyMaster.Database;\n"}, {"date": 1751237790629, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,6 +20,9 @@\n \n // PluginCore namespaces\n global using PluginCore.Models;\n \n+// NotifyMaster.Entities namespaces\n+global using NotifyMaster.Entities;\n+\n // NotifyMaster.Database namespaces\n global using NotifyMaster.Database;\n"}], "date": 1751229755442, "name": "Commit-0", "content": "// Global using statements for NotifyMaster.Database project\n\n// System namespaces\nglobal using System;\nglobal using System.Collections.Generic;\nglobal using System.Linq;\nglobal using System.Threading;\nglobal using System.Threading.Tasks;\n\n// Microsoft Extensions\nglobal using Microsoft.Extensions.Logging;\n\n// Entity Framework\nglobal using Microsoft.EntityFrameworkCore;\n\n// NotifyMaster.Core namespaces\nglobal using NotifyMaster.Core.Interfaces;\nglobal using NotifyMaster.Core.Models;\n\n// NotifyMaster.Database namespaces\nglobal using NotifyMaster.Database;\n"}]}