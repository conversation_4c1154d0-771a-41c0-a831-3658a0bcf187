{"sourceFile": "src/Implementations/NotifyMasterApi/NotifyMasterApi.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 18, "patches": [{"date": 1751212535182, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751216933508, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,8 +13,9 @@\n     </PropertyGroup>\r\n \r\n     <ItemGroup>\r\n         <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.14\" />\r\n+        <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.1\" />\r\n         <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.1\" />\r\n         <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\r\n@@ -39,13 +40,12 @@\n \r\n     <ItemGroup>\r\n       <ProjectReference Include=\"..\\..\\Contracts\\NotificationContract\\NotificationContract.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Contracts\\PluginContract\\PluginContract.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Contracts\\EmailContract\\EmailContract.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Contracts\\SmsContract\\SmsContract.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Contracts\\PushNotificationContract\\PushNotificationContract.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Libraries\\EmailService.Library\\Email.Service.csproj\" />\r\n+      <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Libraries\\PushNotificationService.Library\\PushNotification.Service.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Libraries\\SmsService.Library\\Sms.Service.csproj\" />\r\n     </ItemGroup>\r\n \r\n"}, {"date": 1751224364527, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,39 +15,34 @@\n     <ItemGroup>\r\n         <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.14\" />\r\n         <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\r\n-        <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.1\" />\r\n-        <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.1\" />\r\n+        <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\r\n+        <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\r\n         <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\r\n         <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\r\n         <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\r\n         <PackageReference Include=\"Scalar.AspNetCore\" Version=\"1.2.42\" />\r\n         <PackageReference Include=\"FastEndpoints\" Version=\"5.30.0\" />\r\n         <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.14\" />\r\n         <PackageReference Include=\"Hangfire.SqlServer\" Version=\"1.8.14\" />\r\n-        <PackageReference Include=\"Hangfire.InMemory\" Version=\"0.10.4\" />\r\n-        <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.1.0\" />\r\n+        <PackageReference Include=\"Hangfire.InMemory\" Version=\"1.0.0\" />\r\n+        <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.2.0\" />\r\n         <PackageReference Include=\"System.Text.Json\" Version=\"9.0.0\" />\r\n-        <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.0\" />\r\n-        <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.0\" />\r\n-        <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.21.2\" />\r\n-        <PackageReference Include=\"AWSSDK.S3\" Version=\"3.7.402.8\" />\r\n-        <PackageReference Include=\"MediatR\" Version=\"12.4.1\" />\r\n+        <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.6\" />\r\n+        <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.6\" />\r\n+        <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.24.1\" />\r\n+        <PackageReference Include=\"AWSSDK.S3\" Version=\"4.0.3.1\" />\r\n+        <PackageReference Include=\"MediatR\" Version=\"12.5.0\" />\r\n     </ItemGroup>\r\n \r\n \r\n \r\n     <ItemGroup>\r\n-      <ProjectReference Include=\"..\\..\\Contracts\\NotificationContract\\NotificationContract.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Contracts\\PluginContract\\PluginContract.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Contracts\\SmsContract\\SmsContract.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Contracts\\PushNotificationContract\\PushNotificationContract.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\r\n+      <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\r\n       <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Libraries\\PushNotificationService.Library\\PushNotification.Service.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Libraries\\SmsService.Library\\Sms.Service.csproj\" />\r\n     </ItemGroup>\r\n \r\n     <ItemGroup>\r\n       <Folder Include=\"Presentation\" />\r\n"}, {"date": 1751226771587, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,9 +12,10 @@\n         <RootNamespace>NotifyMasterApi</RootNamespace>\r\n     </PropertyGroup>\r\n \r\n     <ItemGroup>\r\n-        <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.14\" />\r\n+        <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.20\" />\r\n+        <PackageReference Include=\"Hangfire.PostgreSql\" Version=\"1.20.12\" />\r\n         <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\r\n         <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\r\n"}, {"date": 1751240038790, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,52 +1,49 @@\n-<Project Sdk=\"Microsoft.NET.Sdk.Web\">\r\n-\r\n-    <PropertyGroup>\r\n-        <TargetFramework>net9.0</TargetFramework>\r\n-        <LangVersion>latest</LangVersion>\r\n-        <Nullable>enable</Nullable>\r\n-        <ImplicitUsings>enable</ImplicitUsings>\r\n-        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>\r\n-        <GenerateDocumentationFile>true</GenerateDocumentationFile>\r\n-        <NoWarn>$(NoWarn);1591</NoWarn>\r\n-        <AssemblyName>NotifyMasterApi</AssemblyName>\r\n-        <RootNamespace>NotifyMasterApi</RootNamespace>\r\n-    </PropertyGroup>\r\n-\r\n-    <ItemGroup>\r\n-        <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.20\" />\r\n-        <PackageReference Include=\"Hangfire.PostgreSql\" Version=\"1.20.12\" />\r\n-        <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\r\n-        <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\r\n-        <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\r\n-        <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\r\n-        <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\r\n-        <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\r\n-        <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\r\n-        <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\r\n-        <PackageReference Include=\"Scalar.AspNetCore\" Version=\"1.2.42\" />\r\n-        <PackageReference Include=\"FastEndpoints\" Version=\"5.30.0\" />\r\n-        <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.14\" />\r\n-        <PackageReference Include=\"Hangfire.SqlServer\" Version=\"1.8.14\" />\r\n-        <PackageReference Include=\"Hangfire.InMemory\" Version=\"1.0.0\" />\r\n-        <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.2.0\" />\r\n-        <PackageReference Include=\"System.Text.Json\" Version=\"9.0.0\" />\r\n-        <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.6\" />\r\n-        <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.6\" />\r\n-        <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.24.1\" />\r\n-        <PackageReference Include=\"AWSSDK.S3\" Version=\"4.0.3.1\" />\r\n-        <PackageReference Include=\"MediatR\" Version=\"12.5.0\" />\r\n-    </ItemGroup>\r\n-\r\n-\r\n-\r\n-    <ItemGroup>\r\n-      <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\r\n-      <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\r\n-    </ItemGroup>\r\n-\r\n-    <ItemGroup>\r\n-      <Folder Include=\"Presentation\" />\r\n-    </ItemGroup>\r\n-\r\n-</Project>\r\n+<Project Sdk=\"Microsoft.NET.Sdk.Web\">\n+  <PropertyGroup>\n+    <TargetFramework>net9.0</TargetFramework>\n+    <LangVersion>latest</LangVersion>\n+    <Nullable>enable</Nullable>\n+    <ImplicitUsings>enable</ImplicitUsings>\n+    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>\n+    <GenerateDocumentationFile>true</GenerateDocumentationFile>\n+    <NoWarn>$(NoWarn);1591</NoWarn>\n+    <AssemblyName>NotifyMasterApi</AssemblyName>\n+    <RootNamespace>NotifyMasterApi</RootNamespace>\n+  </PropertyGroup>\n+  <ItemGroup>\n+    <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.20\" />\n+    <PackageReference Include=\"Hangfire.PostgreSql\" Version=\"1.20.12\" />\n+    <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.EntityFrameworkCore.Sqlite\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\n+    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"9.0.0\" />\n+    <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\n+    <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\n+    <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\n+    <PackageReference Include=\"Scalar.AspNetCore\" Version=\"2.5.3\" />\n+    <PackageReference Include=\"FastEndpoints\" Version=\"6.2.0\" />\n+    <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.20\" />\n+    <PackageReference Include=\"Hangfire.SqlServer\" Version=\"1.8.20\" />\n+    <PackageReference Include=\"Hangfire.InMemory\" Version=\"1.0.0\" />\n+    <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.2.0\" />\n+    <PackageReference Include=\"System.Text.Json\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.24.1\" />\n+    <PackageReference Include=\"AWSSDK.S3\" Version=\"4.0.3.1\" />\n+    <PackageReference Include=\"MediatR\" Version=\"12.5.0\" />\n+    <PackageReference Include=\"Terminal.Gui\" Version=\"1.19.0\" />\n+  </ItemGroup>\n+  <ItemGroup>\n+    <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\n+    <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\n+    <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\n+  </ItemGroup>\n+  <ItemGroup>\n+    <Folder Include=\"Presentation\" />\n+  </ItemGroup>\n+</Project>\n\\ No newline at end of file\n"}, {"date": 1751240300711, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -42,8 +42,6 @@\n     <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\n     <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\n     <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\n   </ItemGroup>\n-  <ItemGroup>\n-    <Folder Include=\"Presentation\" />\n-  </ItemGroup>\n+\n </Project>\n\\ No newline at end of file\n"}, {"date": 1751242473009, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,10 +18,10 @@\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.Sqlite\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\n-    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"9.0.0\" />\n+    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"8.0.11\" />\n+    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"8.0.2\" />\n     <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\n     <PackageReference Include=\"Scalar.AspNetCore\" Version=\"2.5.3\" />\n"}, {"date": 1751242949259, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -35,9 +35,9 @@\n     <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.24.1\" />\n     <PackageReference Include=\"AWSSDK.S3\" Version=\"4.0.3.1\" />\n     <PackageReference Include=\"MediatR\" Version=\"12.5.0\" />\n-    <PackageReference Include=\"Terminal.Gui\" Version=\"1.19.0\" />\n+    <PackageReference Include=\"Terminal.Gui\" Version=\"2.0.0\" />\n   </ItemGroup>\n   <ItemGroup>\n     <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\n     <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\n"}, {"date": 1751243046653, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,10 +18,10 @@\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.Sqlite\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"8.0.11\" />\n-    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"8.0.2\" />\n+    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\"/>\n+    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"8.0.3\" />\n     <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\n     <PackageReference Include=\"Scalar.AspNetCore\" Version=\"2.5.3\" />\n@@ -33,9 +33,9 @@\n     <PackageReference Include=\"System.Text.Json\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.24.1\" />\n-    <PackageReference Include=\"AWSSDK.S3\" Version=\"4.0.3.1\" />\n+    <PackageReference Include=\"AWSSDK.S3\" Version=\"4.0.3\" />\n     <PackageReference Include=\"MediatR\" Version=\"12.5.0\" />\n     <PackageReference Include=\"Terminal.Gui\" Version=\"2.0.0\" />\n   </ItemGroup>\n   <ItemGroup>\n"}, {"date": 1751244976461, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,9 +18,9 @@\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.Sqlite\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\"/>\n+    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\n     <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"8.0.3\" />\n     <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\n@@ -42,6 +42,5 @@\n     <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\n     <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\n     <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\n   </ItemGroup>\n-\n </Project>\n\\ No newline at end of file\n"}, {"date": 1751245511456, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,9 +19,9 @@\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.Sqlite\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\n-    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"8.0.3\" />\n+    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\n     <PackageReference Include=\"Scalar.AspNetCore\" Version=\"2.5.3\" />\n"}, {"date": 1751245818150, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -29,9 +29,9 @@\n     <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.20\" />\n     <PackageReference Include=\"Hangfire.SqlServer\" Version=\"1.8.20\" />\n     <PackageReference Include=\"Hangfire.InMemory\" Version=\"1.0.0\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.2.0\" />\n-    <PackageReference Include=\"System.Text.Json\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"System.Text.Json\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.24.1\" />\n     <PackageReference Include=\"AWSSDK.S3\" Version=\"4.0.3\" />\n"}, {"date": 1751246389217, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -29,9 +29,9 @@\n     <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.20\" />\n     <PackageReference Include=\"Hangfire.SqlServer\" Version=\"1.8.20\" />\n     <PackageReference Include=\"Hangfire.InMemory\" Version=\"1.0.0\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.2.0\" />\n-    <PackageReference Include=\"System.Text.Json\" Version=\"9.0.0\" />\n+    <PackageReference Include=\"System.Text.Json\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.24.1\" />\n     <PackageReference Include=\"AWSSDK.S3\" Version=\"4.0.3\" />\n"}, {"date": 1751246917042, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,9 +19,8 @@\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.Sqlite\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\n-    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\n     <PackageReference Include=\"Scalar.AspNetCore\" Version=\"2.5.3\" />\n"}, {"date": 1751247199487, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -34,9 +34,8 @@\n     <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.24.1\" />\n     <PackageReference Include=\"AWSSDK.S3\" Version=\"4.0.3\" />\n     <PackageReference Include=\"MediatR\" Version=\"12.5.0\" />\n-    <PackageReference Include=\"Terminal.Gui\" Version=\"2.0.0\" />\n   </ItemGroup>\n   <ItemGroup>\n     <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\n     <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\n"}, {"date": 1751247337546, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -14,13 +14,8 @@\n     <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.20\" />\n     <PackageReference Include=\"Hangfire.PostgreSql\" Version=\"1.20.12\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Microsoft.EntityFrameworkCore.Sqlite\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\n     <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\n     <PackageReference Include=\"Scalar.AspNetCore\" Version=\"2.5.3\" />\n"}, {"date": 1751247494491, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,7 @@\n <Project Sdk=\"Microsoft.NET.Sdk.Web\">\n   <PropertyGroup>\n-    <TargetFramework>net9.0</TargetFramework>\n+    <TargetFramework>net8.0</TargetFramework>\n     <LangVersion>latest</LangVersion>\n     <Nullable>enable</Nullable>\n     <ImplicitUsings>enable</ImplicitUsings>\n     <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>\n@@ -9,31 +9,34 @@\n     <NoWarn>$(NoWarn);1591</NoWarn>\n     <AssemblyName>NotifyMasterApi</AssemblyName>\n     <RootNamespace>NotifyMasterApi</RootNamespace>\n   </PropertyGroup>\n+\n   <ItemGroup>\n+    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"8.0.1\" />\n     <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.20\" />\n     <PackageReference Include=\"Hangfire.PostgreSql\" Version=\"1.20.12\" />\n-    <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\n+    <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"8.0.6\" />\n+    <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"8.0.6\" />\n+    <PackageReference Include=\"Serilog.AspNetCore\" Version=\"8.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\n     <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\n     <PackageReference Include=\"Scalar.AspNetCore\" Version=\"2.5.3\" />\n     <PackageReference Include=\"FastEndpoints\" Version=\"6.2.0\" />\n     <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.20\" />\n     <PackageReference Include=\"Hangfire.SqlServer\" Version=\"1.8.20\" />\n     <PackageReference Include=\"Hangfire.InMemory\" Version=\"1.0.0\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.2.0\" />\n-    <PackageReference Include=\"System.Text.Json\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"System.Text.Json\" Version=\"8.0.6\" />\n+    <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"8.0.0\" />\n+    <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"8.0.0\" />\n     <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.24.1\" />\n     <PackageReference Include=\"AWSSDK.S3\" Version=\"4.0.3\" />\n\\ No newline at end of file\n     <PackageReference Include=\"MediatR\" Version=\"12.5.0\" />\n   </ItemGroup>\n+\n   <ItemGroup>\n     <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\n     <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\n     <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\n   </ItemGroup>\n-</Project>\n+</Project>\n"}, {"date": 1751247595653, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,9 +11,9 @@\n     <RootNamespace>NotifyMasterApi</RootNamespace>\n   </PropertyGroup>\n \n   <ItemGroup>\n-    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"8.0.1\" />\n+    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\".0.0-preview.3.efcore.9.0.0\" />\n     <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.20\" />\n     <PackageReference Include=\"Hangfire.PostgreSql\" Version=\"1.20.12\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"8.0.6\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"8.0.6\" />\n@@ -38,5 +38,5 @@\n     <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\n     <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\n     <ProjectReference Include=\"..\\..\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\n   </ItemGroup>\n-</Project>\n\\ No newline at end of file\n+</Project>\n"}, {"date": 1751247643439, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,10 +11,10 @@\n     <RootNamespace>NotifyMasterApi</RootNamespace>\n   </PropertyGroup>\n \n   <ItemGroup>\n-    <PackageReference Include=\"Pomelo.EntityFramewo<PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"9.0.0-preview.3.efcore.9.0.0\" />\n-rkCore.MySql\" Version=\".0.0-preview.3.efcore.9.0.0\" />\n+    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"9.0.0-preview.3.efcore.9.0.0\" />\n+\n     <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.20\" />\n     <PackageReference Include=\"Hangfire.PostgreSql\" Version=\"1.20.12\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"8.0.6\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"8.0.6\" />\n"}], "date": 1751212535181, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk.Web\">\r\n\r\n    <PropertyGroup>\r\n        <TargetFramework>net9.0</TargetFramework>\r\n        <LangVersion>latest</LangVersion>\r\n        <Nullable>enable</Nullable>\r\n        <ImplicitUsings>enable</ImplicitUsings>\r\n        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>\r\n        <GenerateDocumentationFile>true</GenerateDocumentationFile>\r\n        <NoWarn>$(NoWarn);1591</NoWarn>\r\n        <AssemblyName>NotifyMasterApi</AssemblyName>\r\n        <RootNamespace>NotifyMasterApi</RootNamespace>\r\n    </PropertyGroup>\r\n\r\n    <ItemGroup>\r\n        <PackageReference Include=\"Hangfire.AspNetCore\" Version=\"1.8.14\" />\r\n        <PackageReference Include=\"Microsoft.AspNetCore.OpenApi\" Version=\"9.0.6\" />\r\n        <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.1\" />\r\n        <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.1\" />\r\n        <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\r\n        <PackageReference Include=\"Serilog.AspNetCore\" Version=\"9.0.0\" />\r\n        <PackageReference Include=\"Serilog.Sinks.Console\" Version=\"6.0.0\" />\r\n        <PackageReference Include=\"Serilog.Sinks.Debug\" Version=\"3.0.0\" />\r\n        <PackageReference Include=\"Scalar.AspNetCore\" Version=\"1.2.42\" />\r\n        <PackageReference Include=\"FastEndpoints\" Version=\"5.30.0\" />\r\n        <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.14\" />\r\n        <PackageReference Include=\"Hangfire.SqlServer\" Version=\"1.8.14\" />\r\n        <PackageReference Include=\"Hangfire.InMemory\" Version=\"0.10.4\" />\r\n        <PackageReference Include=\"Microsoft.AspNetCore.SignalR\" Version=\"1.1.0\" />\r\n        <PackageReference Include=\"System.Text.Json\" Version=\"9.0.0\" />\r\n        <PackageReference Include=\"Microsoft.Extensions.Caching.Memory\" Version=\"9.0.0\" />\r\n        <PackageReference Include=\"Microsoft.Extensions.Caching.StackExchangeRedis\" Version=\"9.0.0\" />\r\n        <PackageReference Include=\"Azure.Storage.Blobs\" Version=\"12.21.2\" />\r\n        <PackageReference Include=\"AWSSDK.S3\" Version=\"3.7.402.8\" />\r\n        <PackageReference Include=\"MediatR\" Version=\"12.4.1\" />\r\n    </ItemGroup>\r\n\r\n\r\n\r\n    <ItemGroup>\r\n      <ProjectReference Include=\"..\\..\\Contracts\\NotificationContract\\NotificationContract.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Contracts\\PluginContract\\PluginContract.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Contracts\\EmailContract\\EmailContract.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Contracts\\SmsContract\\SmsContract.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Contracts\\PushNotificationContract\\PushNotificationContract.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Core\\PluginCore\\PluginCore.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Libraries\\EmailService.Library\\Email.Service.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Libraries\\PushNotificationService.Library\\PushNotification.Service.csproj\" />\r\n      <ProjectReference Include=\"..\\..\\Libraries\\SmsService.Library\\Sms.Service.csproj\" />\r\n    </ItemGroup>\r\n\r\n    <ItemGroup>\r\n      <Folder Include=\"Presentation\" />\r\n    </ItemGroup>\r\n\r\n</Project>\r\n"}]}