// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Interface for tenant context service
/// </summary>
public interface ITenantContext
{
    /// <summary>
    /// Gets the current tenant ID
    /// </summary>
    string? TenantId { get; }
    
    /// <summary>
    /// Gets the current tenant
    /// </summary>
    Tenant? CurrentTenant { get; }
    
    /// <summary>
    /// Gets the current user ID
    /// </summary>
    string? UserId { get; }
    
    /// <summary>
    /// Gets the current user
    /// </summary>
    User? CurrentUser { get; }
    
    /// <summary>
    /// Sets the current tenant context
    /// </summary>
    void SetTenant(string tenantId, Tenant? tenant = null);
    
    /// <summary>
    /// Sets the current user context
    /// </summary>
    void SetUser(string userId, User? user = null);
    
    /// <summary>
    /// Clears the current context
    /// </summary>
    void Clear();
}
