namespace NotifyMasterApi.Features.Tenants.Commands;

/// <summary>
/// Command to create a new tenant
/// </summary>
public record CreateTenantCommand(
    string Name,
    string? Description,
    string Domain,
    TenantPlan Plan,
    Dictionary<string, object>? Settings,
    TenantLimits? CustomLimits,
    string? CreatedBy) : IRequest<CreateTenantResult>;

/// <summary>
/// Command to update an existing tenant
/// </summary>
public record UpdateTenantCommand(
    string TenantId,
    string? Name,
    string? Description,
    string? Domain,
    TenantPlan? Plan,
    TenantStatus? Status,
    Dictionary<string, object>? Settings,
    TenantLimits? Limits,
    string? UpdatedBy) : IRequest<UpdateTenantResult>;

/// <summary>
/// Command to delete a tenant
/// </summary>
public record DeleteTenantCommand(string TenantId) : IRequest<DeleteTenantResult>;

/// <summary>
/// Command to suspend a tenant
/// </summary>
public record SuspendTenantCommand(string TenantId, string Reason) : IRequest<SuspendTenantResult>;

/// <summary>
/// Command to activate a tenant
/// </summary>
public record ActivateTenantCommand(string TenantId) : IRequest<ActivateTenantResult>;

/// <summary>
/// Result of creating a tenant
/// </summary>
public class CreateTenantResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public Tenant? Tenant { get; set; }
}

/// <summary>
/// Result of updating a tenant
/// </summary>
public class UpdateTenantResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public Tenant? Tenant { get; set; }
}

/// <summary>
/// Result of deleting a tenant
/// </summary>
public class DeleteTenantResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Result of suspending a tenant
/// </summary>
public class SuspendTenantResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Result of activating a tenant
/// </summary>
public class ActivateTenantResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Handler for creating tenants
/// </summary>
public class CreateTenantHandler : IRequestHandler<CreateTenantCommand, CreateTenantResult>
{
    private readonly ITenantService _tenantService;
    private readonly ILogger<CreateTenantHandler> _logger;

    public CreateTenantHandler(ITenantService tenantService, ILogger<CreateTenantHandler> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<CreateTenantResult> Handle(CreateTenantCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var createRequest = new CreateTenantRequest
            {
                Name = request.Name,
                Description = request.Description,
                Domain = request.Domain,
                Plan = request.Plan,
                Settings = request.Settings ?? new Dictionary<string, object>(),
                CustomLimits = request.CustomLimits,
                CreatedBy = request.CreatedBy
            };

            var result = await _tenantService.CreateTenantAsync(createRequest, cancellationToken);

            return new CreateTenantResult
            {
                Success = result.IsSuccess,
                Message = result.Message ?? string.Empty,
                Tenant = result.Data
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tenant {TenantName}", request.Name);
            return new CreateTenantResult
            {
                Success = false,
                Message = "Failed to create tenant"
            };
        }
    }
}

/// <summary>
/// Handler for updating tenants
/// </summary>
public class UpdateTenantHandler : IRequestHandler<UpdateTenantCommand, UpdateTenantResult>
{
    private readonly ITenantService _tenantService;
    private readonly ILogger<UpdateTenantHandler> _logger;

    public UpdateTenantHandler(ITenantService tenantService, ILogger<UpdateTenantHandler> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<UpdateTenantResult> Handle(UpdateTenantCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var updateRequest = new UpdateTenantRequest
            {
                Name = request.Name,
                Description = request.Description,
                Domain = request.Domain,
                Plan = request.Plan,
                Status = request.Status,
                Settings = request.Settings,
                Limits = request.Limits,
                UpdatedBy = request.UpdatedBy
            };

            var result = await _tenantService.UpdateTenantAsync(request.TenantId, updateRequest, cancellationToken);

            return new UpdateTenantResult
            {
                Success = result.IsSuccess,
                Message = result.Message ?? string.Empty,
                Tenant = result.Data
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant {TenantId}", request.TenantId);
            return new UpdateTenantResult
            {
                Success = false,
                Message = "Failed to update tenant"
            };
        }
    }
}

/// <summary>
/// Handler for deleting tenants
/// </summary>
public class DeleteTenantHandler : IRequestHandler<DeleteTenantCommand, DeleteTenantResult>
{
    private readonly ITenantService _tenantService;
    private readonly ILogger<DeleteTenantHandler> _logger;

    public DeleteTenantHandler(ITenantService tenantService, ILogger<DeleteTenantHandler> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<DeleteTenantResult> Handle(DeleteTenantCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var result = await _tenantService.DeleteTenantAsync(request.TenantId, cancellationToken);

            return new DeleteTenantResult
            {
                Success = result.IsSuccess,
                Message = result.Message ?? string.Empty
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting tenant {TenantId}", request.TenantId);
            return new DeleteTenantResult
            {
                Success = false,
                Message = "Failed to delete tenant"
            };
        }
    }
}

/// <summary>
/// Handler for suspending tenants
/// </summary>
public class SuspendTenantHandler : IRequestHandler<SuspendTenantCommand, SuspendTenantResult>
{
    private readonly ITenantService _tenantService;
    private readonly ILogger<SuspendTenantHandler> _logger;

    public SuspendTenantHandler(ITenantService tenantService, ILogger<SuspendTenantHandler> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<SuspendTenantResult> Handle(SuspendTenantCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var result = await _tenantService.SuspendTenantAsync(request.TenantId, request.Reason, cancellationToken);

            return new SuspendTenantResult
            {
                Success = result.IsSuccess,
                Message = result.Message ?? string.Empty
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error suspending tenant {TenantId}", request.TenantId);
            return new SuspendTenantResult
            {
                Success = false,
                Message = "Failed to suspend tenant"
            };
        }
    }
}

/// <summary>
/// Handler for activating tenants
/// </summary>
public class ActivateTenantHandler : IRequestHandler<ActivateTenantCommand, ActivateTenantResult>
{
    private readonly ITenantService _tenantService;
    private readonly ILogger<ActivateTenantHandler> _logger;

    public ActivateTenantHandler(ITenantService tenantService, ILogger<ActivateTenantHandler> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<ActivateTenantResult> Handle(ActivateTenantCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var result = await _tenantService.ActivateTenantAsync(request.TenantId, cancellationToken);

            return new ActivateTenantResult
            {
                Success = result.IsSuccess,
                Message = result.Message ?? string.Empty
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating tenant {TenantId}", request.TenantId);
            return new ActivateTenantResult
            {
                Success = false,
                Message = "Failed to activate tenant"
            };
        }
    }
}
