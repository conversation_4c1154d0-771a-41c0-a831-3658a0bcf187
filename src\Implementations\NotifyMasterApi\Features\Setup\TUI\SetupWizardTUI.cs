// Using statements are handled by GlobalUsings.cs
using Terminal.Gui;

namespace NotifyMasterApi.Features.Setup.TUI;

/// <summary>
/// Classic DOS-style Terminal UI Setup Wizard for first-run configuration
/// </summary>
public class SetupWizardTUI
{
    private readonly ISetupService _setupService;
    private readonly ILogger<SetupWizardTUI> _logger;
    private readonly IConfiguration _configuration;
    private readonly IServiceProvider _serviceProvider;

    private Toplevel _top = null!;
    private Window _mainWindow = null!;
    private MenuBar _menuBar = null!;
    private StatusBar _statusBar = null!;
    private FrameView _contentFrame = null!;
    private FrameView _buttonFrame = null!;

    private SetupWizardData _wizardData = new();
    private int _currentStep = 0;
    private readonly List<SetupStep> _steps = new();

    public SetupWizardTUI(
        ISetupService setupService,
        ILogger<SetupWizardTUI> logger,
        IConfiguration configuration,
        IServiceProvider serviceProvider)
    {
        _setupService = setupService;
        _logger = logger;
        _configuration = configuration;
        _serviceProvider = serviceProvider;
        
        InitializeSteps();
    }

    private void InitializeSteps()
    {
        _steps.Add(new SetupStep("Welcome", "Welcome to NotifyMaster Setup", ShowWelcomeStep));
        _steps.Add(new SetupStep("Database", "Configure Database Connection", ShowDatabaseStep));
        _steps.Add(new SetupStep("Redis", "Configure Redis Connection", ShowRedisStep));
        _steps.Add(new SetupStep("Tenant", "Create First Tenant", ShowTenantStep));
        _steps.Add(new SetupStep("Admin", "Create Admin User", ShowAdminStep));
        _steps.Add(new SetupStep("Complete", "Setup Complete", ShowCompleteStep));
    }

    public async Task<bool> RunAsync()
    {
        try
        {
            // Check if system is already initialized
            if (await _setupService.IsSystemInitializedAsync())
            {
                Console.WriteLine("✅ System is already initialized. Skipping setup wizard.");
                return true;
            }

            Application.Init();
            
            CreateMainWindow();
            ShowCurrentStep();
            
            Application.Run(_mainWindow);
            Application.Shutdown();
            
            return _wizardData.IsCompleted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running setup wizard");
            return false;
        }
    }

    private void CreateMainWindow()
    {
        _mainWindow = new Window("NotifyMaster Setup Wizard")
        {
            X = 0,
            Y = 0,
            Width = Dim.Fill(),
            Height = Dim.Fill()
        };

        // Title
        _titleLabel = new Label("🚀 NotifyMaster Setup Wizard")
        {
            X = Pos.Center(),
            Y = 1,
            ColorScheme = Colors.TopLevel
        };

        // Progress bar
        _progressBar = new ProgressBar()
        {
            X = 2,
            Y = 3,
            Width = Dim.Fill() - 4,
            Height = 1
        };

        // Step label
        _stepLabel = new Label("Step 1 of 6: Welcome")
        {
            X = 2,
            Y = 4
        };

        // Content frame
        _contentFrame = new FrameView("Setup")
        {
            X = 2,
            Y = 6,
            Width = Dim.Fill() - 4,
            Height = Dim.Fill() - 10
        };

        // Navigation buttons
        var prevButton = new Button("Previous")
        {
            X = 2,
            Y = Pos.Bottom(_mainWindow) - 3
        };
        prevButton.Clicked += OnPreviousClicked;

        var nextButton = new Button("Next")
        {
            X = Pos.Right(prevButton) + 2,
            Y = Pos.Bottom(_mainWindow) - 3
        };
        nextButton.Clicked += OnNextClicked;

        var cancelButton = new Button("Cancel")
        {
            X = Pos.Right(_mainWindow) - 12,
            Y = Pos.Bottom(_mainWindow) - 3
        };
        cancelButton.Clicked += OnCancelClicked;

        _mainWindow.Add(_titleLabel, _progressBar, _stepLabel, _contentFrame, prevButton, nextButton, cancelButton);
    }

    private void ShowCurrentStep()
    {
        if (_currentStep >= _steps.Count) return;

        var step = _steps[_currentStep];
        
        // Update progress
        _progressBar.Fraction = (float)_currentStep / (_steps.Count - 1);
        _stepLabel.Text = $"Step {_currentStep + 1} of {_steps.Count}: {step.Title}";
        _contentFrame.Title = step.Description;
        
        // Clear content frame
        _contentFrame.RemoveAll();
        
        // Show step content
        step.ShowContent(_contentFrame, _wizardData);
        
        _mainWindow.SetNeedsDisplay();
    }

    private void OnPreviousClicked()
    {
        if (_currentStep > 0)
        {
            _currentStep--;
            ShowCurrentStep();
        }
    }

    private void OnNextClicked()
    {
        if (_currentStep < _steps.Count - 1)
        {
            if (ValidateCurrentStep())
            {
                _currentStep++;
                ShowCurrentStep();
            }
        }
        else if (_currentStep == _steps.Count - 1)
        {
            // Finish setup
            _ = Task.Run(async () => await CompleteSetupAsync());
        }
    }

    private void OnCancelClicked()
    {
        var result = MessageBox.Query("Cancel Setup", "Are you sure you want to cancel the setup?", "Yes", "No");
        if (result == 0)
        {
            Application.RequestStop();
        }
    }

    private bool ValidateCurrentStep()
    {
        var step = _steps[_currentStep];
        return step.Validate(_wizardData);
    }

    private async Task CompleteSetupAsync()
    {
        try
        {
            Application.MainLoop.Invoke(() =>
            {
                var progressDialog = new Dialog("Completing Setup", 60, 10);
                var progressBar = new ProgressBar()
                {
                    X = 1,
                    Y = 2,
                    Width = Dim.Fill() - 2,
                    Height = 1
                };
                var statusLabel = new Label("Initializing system...")
                {
                    X = 1,
                    Y = 4,
                    Width = Dim.Fill() - 2
                };
                
                progressDialog.Add(progressBar, statusLabel);
                Application.Run(progressDialog);
            });

            // Update configuration
            await UpdateConfigurationAsync();
            
            // Initialize system
            var request = new InitializeSystemRequest
            {
                RootTenantName = _wizardData.TenantName,
                TenantName = _wizardData.TenantName,
                TenantDomain = _wizardData.TenantDomain,
                TenantDescription = _wizardData.TenantDescription,
                AdminEmail = _wizardData.AdminEmail,
                AdminPassword = _wizardData.AdminPassword,
                AdminFirstName = _wizardData.AdminFirstName,
                AdminLastName = _wizardData.AdminLastName
            };

            var success = await _setupService.InitializeSystemAsync(request);
            
            _wizardData.IsCompleted = success;
            
            Application.MainLoop.Invoke(() =>
            {
                if (success)
                {
                    MessageBox.Query("Setup Complete", "✅ NotifyMaster has been successfully configured!\n\nYou can now start using the system.", "OK");
                }
                else
                {
                    MessageBox.ErrorQuery("Setup Failed", "❌ Failed to complete setup. Please check the logs for details.", "OK");
                }
                Application.RequestStop();
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing setup");
            Application.MainLoop.Invoke(() =>
            {
                MessageBox.ErrorQuery("Setup Error", $"❌ Setup failed: {ex.Message}", "OK");
                Application.RequestStop();
            });
        }
    }

    private async Task UpdateConfigurationAsync()
    {
        // Update appsettings.json with new configuration
        var configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
        
        if (File.Exists(configPath))
        {
            var json = await File.ReadAllTextAsync(configPath);
            var config = JsonSerializer.Deserialize<Dictionary<string, object>>(json) ?? new();
            
            // Update connection strings
            if (!config.ContainsKey("ConnectionStrings"))
                config["ConnectionStrings"] = new Dictionary<string, object>();
                
            var connectionStrings = (Dictionary<string, object>)config["ConnectionStrings"];
            connectionStrings["DefaultConnection"] = _wizardData.DatabaseConnectionString;
            connectionStrings["RedisConnection"] = _wizardData.RedisConnectionString;
            
            var updatedJson = JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(configPath, updatedJson);
        }
    }
}

public class SetupWizardData
{
    public string DatabaseConnectionString { get; set; } = string.Empty;
    public string RedisConnectionString { get; set; } = string.Empty;
    public string TenantName { get; set; } = string.Empty;
    public string TenantDomain { get; set; } = string.Empty;
    public string TenantDescription { get; set; } = string.Empty;
    public string AdminEmail { get; set; } = string.Empty;
    public string AdminPassword { get; set; } = string.Empty;
    public string AdminFirstName { get; set; } = string.Empty;
    public string AdminLastName { get; set; } = string.Empty;
    public bool IsCompleted { get; set; }
}

public class SetupStep
{
    public string Name { get; }
    public string Title { get; }
    public string Description { get; }
    public Action<FrameView, SetupWizardData> ShowContent { get; }
    public Func<SetupWizardData, bool> Validate { get; }

    public SetupStep(string name, string description, Action<FrameView, SetupWizardData> showContent, Func<SetupWizardData, bool>? validate = null)
    {
        Name = name;
        Title = name;
        Description = description;
        ShowContent = showContent;
        Validate = validate ?? (_ => true);
    }
}
