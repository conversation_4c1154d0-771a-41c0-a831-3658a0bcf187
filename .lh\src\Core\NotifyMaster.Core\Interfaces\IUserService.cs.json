{"sourceFile": "src/Core/NotifyMaster.Core/Interfaces/IUserService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751232639695, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751232639695, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Core.Interfaces;\n\n/// <summary>\n/// Interface for user management service\n/// </summary>\npublic interface IUserService\n{\n    /// <summary>\n    /// Gets a user by ID\n    /// </summary>\n    Task<User?> GetUserAsync(string userId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets a user by email\n    /// </summary>\n    Task<User?> GetUserByEmailAsync(string email, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets a user by username\n    /// </summary>\n    Task<User?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets users by tenant with pagination\n    /// </summary>\n    Task<IReadOnlyList<User>> GetUsersByTenantAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Creates a new user\n    /// </summary>\n    Task<OperationResult<User>> CreateUserAsync(CreateUserRequest request, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Updates an existing user\n    /// </summary>\n    Task<OperationResult<User>> UpdateUserAsync(string userId, UpdateUserRequest request, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Deletes a user\n    /// </summary>\n    Task<OperationResult> DeleteUserAsync(string userId, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets user roles\n    /// </summary>\n    Task<IReadOnlyList<Role>> GetUserRolesAsync(string userId, string? tenantId = null, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Gets user permissions\n    /// </summary>\n    Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, string? tenantId = null, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Assigns a role to a user\n    /// </summary>\n    Task<OperationResult> AssignRoleAsync(string userId, string roleId, string? tenantId = null, string? assignedBy = null, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Removes a role from a user\n    /// </summary>\n    Task<OperationResult> RemoveRoleAsync(string userId, string roleId, string? tenantId = null, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Grants a permission to a user\n    /// </summary>\n    Task<OperationResult> GrantPermissionAsync(string userId, string permissionId, string? tenantId = null, string? grantedBy = null, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Revokes a permission from a user\n    /// </summary>\n    Task<OperationResult> RevokePermissionAsync(string userId, string permissionId, string? tenantId = null, CancellationToken cancellationToken = default);\n    \n    /// <summary>\n    /// Authenticates a user\n    /// </summary>\n    Task<OperationResult<AuthenticationResult>> AuthenticateAsync(string tenantId, string email, string password, CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Checks if a user has a specific permission\n    /// </summary>\n    Task<bool> HasPermissionAsync(string userId, string resource, string action, string? tenantId = null, CancellationToken cancellationToken = default);\n\n    /// <summary>\n    /// Checks if a user has a specific role\n    /// </summary>\n    Task<bool> HasRoleAsync(string userId, string roleName, string? tenantId = null, CancellationToken cancellationToken = default);\n}\n"}]}