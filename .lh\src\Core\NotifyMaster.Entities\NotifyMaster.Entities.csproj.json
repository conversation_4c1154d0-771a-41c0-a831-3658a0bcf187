{"sourceFile": "src/Core/NotifyMaster.Entities/NotifyMaster.Entities.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1751237282874, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751238043749, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,8 +12,11 @@\n   <ItemGroup>\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n     <PackageReference Include=\"System.ComponentModel.Annotations\" Version=\"5.0.0\" />\n   </ItemGroup>\n+  <ItemGroup>\n+    <ProjectReference Include=\"..\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\n+  </ItemGroup>\n \n \n \n </Project>\n"}, {"date": 1751238847058, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,11 +12,9 @@\n   <ItemGroup>\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n     <PackageReference Include=\"System.ComponentModel.Annotations\" Version=\"5.0.0\" />\n   </ItemGroup>\n-  <ItemGroup>\n-    <ProjectReference Include=\"..\\NotifyMaster.Core\\NotifyMaster.Core.csproj\" />\n-  </ItemGroup>\n \n \n \n+\n </Project>\n"}], "date": 1751237282874, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk\">\n\n  <PropertyGroup>\n    <TargetFramework>net9.0</TargetFramework>\n    <LangVersion>latest</LangVersion>\n    <Nullable>enable</Nullable>\n    <ImplicitUsings>enable</ImplicitUsings>\n    <AssemblyName>NotifyMaster.Entities</AssemblyName>\n    <RootNamespace>NotifyMaster.Entities</RootNamespace>\n  </PropertyGroup>\n\n  <ItemGroup>\n    <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n    <PackageReference Include=\"System.ComponentModel.Annotations\" Version=\"5.0.0\" />\n  </ItemGroup>\n\n\n\n</Project>\n"}]}