{"sourceFile": "src/Core/NotifyMaster.Core/Services/RoleService.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1751235598836, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751235612390, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -229,9 +229,10 @@\n             {\n                 RoleId = roleId,\n                 PermissionId = permissionId,\n                 TenantId = role.TenantId,\n-                AssignedAt = DateTime.UtcNow\n+                AssignedAt = DateTime.UtcNow,\n+                AssignedBy = assignedBy\n             });\n \n             await _context.SaveChangesAsync(cancellationToken);\n \n"}, {"date": 1751238343947, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,12 +6,12 @@\n /// Implementation of role management service using Entity Framework\n /// </summary>\n public class RoleService : IRoleService\n {\n-    private readonly NotifyMasterDbContext _context;\n+    private readonly INotifyMasterDbContext _context;\n     private readonly ILogger<RoleService> _logger;\n \n-    public RoleService(NotifyMasterDbContext context, ILogger<RoleService> logger)\n+    public RoleService(INotifyMasterDbContext context, ILogger<RoleService> logger)\n     {\n         _context = context;\n         _logger = logger;\n     }\n"}, {"date": 1751238594477, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,18 +2,18 @@\n \n namespace NotifyMaster.Core.Services;\n \n /// <summary>\n-/// Implementation of role management service using Entity Framework\n+/// Implementation of role management service using repositories\n /// </summary>\n public class RoleService : IRoleService\n {\n-    private readonly INotifyMasterDbContext _context;\n+    private readonly IRoleRepository _roleRepository;\n     private readonly ILogger<RoleService> _logger;\n \n-    public RoleService(INotifyMasterDbContext context, ILogger<RoleService> logger)\n+    public RoleService(IRoleRepository roleRepository, ILogger<RoleService> logger)\n     {\n-        _context = context;\n+        _roleRepository = roleRepository;\n         _logger = logger;\n     }\n \n     public async Task<Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default)\n"}], "date": 1751235598836, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Core.Services;\n\n/// <summary>\n/// Implementation of role management service using Entity Framework\n/// </summary>\npublic class RoleService : IRoleService\n{\n    private readonly NotifyMasterDbContext _context;\n    private readonly ILogger<RoleService> _logger;\n\n    public RoleService(NotifyMasterDbContext context, ILogger<RoleService> logger)\n    {\n        _context = context;\n        _logger = logger;\n    }\n\n    public async Task<Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Roles\n                .Include(r => r.Permissions).ThenInclude(rp => rp.Permission)\n                .Include(r => r.Users).ThenInclude(ur => ur.User)\n                .FirstOrDefaultAsync(r => r.Id == roleId, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting role {RoleId}\", roleId);\n            return null;\n        }\n    }\n\n    public async Task<Role?> GetRoleByNameAsync(string roleName, RoleScope scope = RoleScope.Tenant, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Roles\n                .Include(r => r.Permissions).ThenInclude(rp => rp.Permission)\n                .FirstOrDefaultAsync(r => r.Name == roleName && r.Scope == scope, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting role by name {RoleName}\", roleName);\n            return null;\n        }\n    }\n\n    public async Task<IReadOnlyList<Role>> GetRolesAsync(RoleScope? scope = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var query = _context.Roles.AsQueryable();\n\n            if (scope.HasValue)\n            {\n                query = query.Where(r => r.Scope == scope.Value);\n            }\n\n            return await query\n                .OrderBy(r => r.Name)\n                .Skip(skip)\n                .Take(take)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting roles\");\n            return Array.Empty<Role>();\n        }\n    }\n\n    public async Task<IReadOnlyList<Role>> GetRolesByTenantAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.Roles\n                .Where(r => r.TenantId == tenantId)\n                .OrderBy(r => r.Name)\n                .Skip(skip)\n                .Take(take)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting roles for tenant {TenantId}\", tenantId);\n            return Array.Empty<Role>();\n        }\n    }\n\n    public async Task<OperationResult<Role>> CreateRoleAsync(CreateRoleRequest request, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            // Check if role already exists\n            var existingRole = await _context.Roles\n                .FirstOrDefaultAsync(r => r.Name == request.Name && r.Scope == request.Scope, cancellationToken);\n\n            if (existingRole != null)\n            {\n                return OperationResult<Role>.Failure(\"A role with this name already exists\");\n            }\n\n            var role = new Role\n            {\n                Id = Guid.NewGuid().ToString(),\n                Name = request.Name,\n                Description = request.Description,\n                Scope = request.Scope,\n                TenantId = request.TenantId,\n                IsActive = true,\n                CreatedAt = DateTime.UtcNow,\n                CreatedBy = request.CreatedBy\n            };\n\n            _context.Roles.Add(role);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Created role {RoleId} ({Name}) with scope {Scope}\", role.Id, role.Name, role.Scope);\n            return OperationResult<Role>.Success(role);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error creating role {Name}\", request.Name);\n            return OperationResult<Role>.Failure($\"Failed to create role: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult<Role>> UpdateRoleAsync(string roleId, UpdateRoleRequest request, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var role = await _context.Roles.FindAsync(roleId);\n            if (role == null)\n            {\n                return OperationResult<Role>.Failure(\"Role not found\");\n            }\n\n            // Update fields if provided\n            if (!string.IsNullOrEmpty(request.Name))\n            {\n                // Check if name is already taken\n                var existingRole = await _context.Roles\n                    .FirstOrDefaultAsync(r => r.Id != roleId && r.Name == request.Name && r.Scope == role.Scope, cancellationToken);\n                if (existingRole != null)\n                {\n                    return OperationResult<Role>.Failure(\"Role name is already taken\");\n                }\n                role.Name = request.Name;\n            }\n\n            if (!string.IsNullOrEmpty(request.Description))\n                role.Description = request.Description;\n\n            if (request.IsActive.HasValue)\n                role.IsActive = request.IsActive.Value;\n\n            role.UpdatedAt = DateTime.UtcNow;\n            role.UpdatedBy = request.UpdatedBy;\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Updated role {RoleId}\", roleId);\n            return OperationResult<Role>.Success(role);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error updating role {RoleId}\", roleId);\n            return OperationResult<Role>.Failure($\"Failed to update role: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> DeleteRoleAsync(string roleId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var role = await _context.Roles.FindAsync(roleId);\n            if (role == null)\n            {\n                return OperationResult.Failure(\"Role not found\");\n            }\n\n            // Check if role is in use\n            var isInUse = await _context.UserRoles.AnyAsync(ur => ur.RoleId == roleId, cancellationToken);\n            if (isInUse)\n            {\n                return OperationResult.Failure(\"Cannot delete role that is assigned to users\");\n            }\n\n            _context.Roles.Remove(role);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Deleted role {RoleId}\", roleId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error deleting role {RoleId}\", roleId);\n            return OperationResult.Failure($\"Failed to delete role: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> AssignPermissionAsync(string roleId, string permissionId, string? assignedBy = null, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var role = await _context.Roles.FindAsync(roleId);\n            if (role == null)\n            {\n                return OperationResult.Failure(\"Role not found\");\n            }\n\n            var permission = await _context.Permissions.FindAsync(permissionId);\n            if (permission == null)\n            {\n                return OperationResult.Failure(\"Permission not found\");\n            }\n\n            var existingRolePermission = await _context.RolePermissions\n                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId, cancellationToken);\n\n            if (existingRolePermission != null)\n            {\n                return OperationResult.Failure(\"Role already has this permission\");\n            }\n\n            _context.RolePermissions.Add(new RolePermission\n            {\n                RoleId = roleId,\n                PermissionId = permissionId,\n                TenantId = role.TenantId,\n                AssignedAt = DateTime.UtcNow\n            });\n\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Assigned permission {PermissionId} to role {RoleId}\", permissionId, roleId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error assigning permission {PermissionId} to role {RoleId}\", permissionId, roleId);\n            return OperationResult.Failure($\"Failed to assign permission: {ex.Message}\");\n        }\n    }\n\n    public async Task<OperationResult> RemovePermissionAsync(string roleId, string permissionId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            var rolePermission = await _context.RolePermissions\n                .FirstOrDefaultAsync(rp => rp.RoleId == roleId && rp.PermissionId == permissionId, cancellationToken);\n\n            if (rolePermission == null)\n            {\n                return OperationResult.Failure(\"Role does not have this permission\");\n            }\n\n            _context.RolePermissions.Remove(rolePermission);\n            await _context.SaveChangesAsync(cancellationToken);\n\n            _logger.LogInformation(\"Removed permission {PermissionId} from role {RoleId}\", permissionId, roleId);\n            return OperationResult.Success();\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error removing permission {PermissionId} from role {RoleId}\", permissionId, roleId);\n            return OperationResult.Failure($\"Failed to remove permission: {ex.Message}\");\n        }\n    }\n\n    public async Task<IReadOnlyList<Permission>> GetRolePermissionsAsync(string roleId, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            return await _context.RolePermissions\n                .Include(rp => rp.Permission)\n                .Where(rp => rp.RoleId == roleId)\n                .Select(rp => rp.Permission)\n                .ToListAsync(cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error getting permissions for role {RoleId}\", roleId);\n            return Array.Empty<Permission>();\n        }\n    }\n}\n"}]}