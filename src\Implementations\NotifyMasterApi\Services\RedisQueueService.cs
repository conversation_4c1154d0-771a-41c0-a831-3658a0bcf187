using StackExchange.Redis;

namespace NotifyMasterApi.Services;

/// <summary>
/// Redis-based queue service for handling notification messages
/// Replaces RabbitMQ functionality with Redis pub/sub and lists
/// </summary>
public interface IQueueService
{
    Task PublishAsync<T>(string queueName, T message, CancellationToken cancellationToken = default);
    Task<T?> ConsumeAsync<T>(string queueName, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> ConsumeBatchAsync<T>(string queueName, int batchSize = 10, CancellationToken cancellationToken = default);
    Task<long> GetQueueLengthAsync(string queueName);
    Task<bool> DeleteQueueAsync(string queueName);
    Task SubscribeAsync<T>(string channel, Func<T, Task> handler, CancellationToken cancellationToken = default);
    Task PublishEventAsync<T>(string channel, T eventData, CancellationToken cancellationToken = default);
}

public class RedisQueueService : IQueueService, IDisposable
{
    private readonly IDatabase _database;
    private readonly ISubscriber _subscriber;
    private readonly IConnectionMultiplexer _redis;
    private readonly ILogger<RedisQueueService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public RedisQueueService(IConnectionMultiplexer redis, ILogger<RedisQueueService> logger)
    {
        _redis = redis;
        _database = redis.GetDatabase();
        _subscriber = redis.GetSubscriber();
        _logger = logger;
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    /// <summary>
    /// Publish a message to a Redis list (queue)
    /// </summary>
    public async Task PublishAsync<T>(string queueName, T message, CancellationToken cancellationToken = default)
    {
        try
        {
            var json = JsonSerializer.Serialize(message, _jsonOptions);
            var redisKey = GetQueueKey(queueName);
            
            await _database.ListLeftPushAsync(redisKey, json);
            
            _logger.LogDebug("Published message to queue {QueueName}: {MessageType}", queueName, typeof(T).Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing message to queue {QueueName}", queueName);
            throw;
        }
    }

    /// <summary>
    /// Consume a single message from a Redis list (queue)
    /// </summary>
    public async Task<T?> ConsumeAsync<T>(string queueName, CancellationToken cancellationToken = default)
    {
        try
        {
            var redisKey = GetQueueKey(queueName);
            
            // Use blocking pop with timeout
            var result = await _database.ListRightPopAsync(redisKey);
            
            if (!result.HasValue)
            {
                return default(T);
            }

            var message = JsonSerializer.Deserialize<T>(result!, _jsonOptions);
            
            _logger.LogDebug("Consumed message from queue {QueueName}: {MessageType}", queueName, typeof(T).Name);
            
            return message;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error consuming message from queue {QueueName}", queueName);
            throw;
        }
    }

    /// <summary>
    /// Consume multiple messages from a Redis list (queue) in batch
    /// </summary>
    public async Task<IEnumerable<T>> ConsumeBatchAsync<T>(string queueName, int batchSize = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            var redisKey = GetQueueKey(queueName);
            var messages = new List<T>();
            
            // Pop multiple messages in a transaction
            var transaction = _database.CreateTransaction();
            var tasks = new List<Task<RedisValue>>();
            
            for (int i = 0; i < batchSize; i++)
            {
                tasks.Add(transaction.ListRightPopAsync(redisKey));
            }
            
            await transaction.ExecuteAsync();
            
            foreach (var task in tasks)
            {
                var result = await task;
                if (result.HasValue)
                {
                    var message = JsonSerializer.Deserialize<T>(result!, _jsonOptions);
                    if (message != null)
                    {
                        messages.Add(message);
                    }
                }
            }
            
            _logger.LogDebug("Consumed {Count} messages from queue {QueueName}", messages.Count, queueName);
            
            return messages;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error consuming batch from queue {QueueName}", queueName);
            throw;
        }
    }

    /// <summary>
    /// Get the current length of a queue
    /// </summary>
    public async Task<long> GetQueueLengthAsync(string queueName)
    {
        try
        {
            var redisKey = GetQueueKey(queueName);
            return await _database.ListLengthAsync(redisKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting queue length for {QueueName}", queueName);
            throw;
        }
    }

    /// <summary>
    /// Delete a queue and all its messages
    /// </summary>
    public async Task<bool> DeleteQueueAsync(string queueName)
    {
        try
        {
            var redisKey = GetQueueKey(queueName);
            return await _database.KeyDeleteAsync(redisKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting queue {QueueName}", queueName);
            throw;
        }
    }

    /// <summary>
    /// Subscribe to a Redis pub/sub channel for real-time events
    /// </summary>
    public async Task SubscribeAsync<T>(string channel, Func<T, Task> handler, CancellationToken cancellationToken = default)
    {
        try
        {
            var redisChannel = GetChannelKey(channel);
            
            await _subscriber.SubscribeAsync(RedisChannel.Literal(redisChannel), async (channel, message) =>
            {
                try
                {
                    if (message.HasValue)
                    {
                        var eventData = JsonSerializer.Deserialize<T>(message!, _jsonOptions);
                        if (eventData != null)
                        {
                            await handler(eventData);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error handling message from channel {Channel}", channel);
                }
            });
            
            _logger.LogInformation("Subscribed to channel {Channel}", channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing to channel {Channel}", channel);
            throw;
        }
    }

    /// <summary>
    /// Publish an event to a Redis pub/sub channel
    /// </summary>
    public async Task PublishEventAsync<T>(string channel, T eventData, CancellationToken cancellationToken = default)
    {
        try
        {
            var json = JsonSerializer.Serialize(eventData, _jsonOptions);
            var redisChannel = GetChannelKey(channel);
            
            var subscriberCount = await _subscriber.PublishAsync(RedisChannel.Literal(redisChannel), json);
            
            _logger.LogDebug("Published event to channel {Channel}, reached {SubscriberCount} subscribers", 
                channel, subscriberCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing event to channel {Channel}", channel);
            throw;
        }
    }

    private static string GetQueueKey(string queueName) => $"queue:{queueName}";
    private static string GetChannelKey(string channelName) => $"channel:{channelName}";

    public void Dispose()
    {
        _redis?.Dispose();
    }
}

/// <summary>
/// Queue names for different notification types
/// </summary>
public static class QueueNames
{
    public const string SmsQueue = "sms-notifications";
    public const string EmailQueue = "email-notifications";
    public const string PushQueue = "push-notifications";
    public const string BulkSmsQueue = "bulk-sms-notifications";
    public const string BulkEmailQueue = "bulk-email-notifications";
    public const string BulkPushQueue = "bulk-push-notifications";
    public const string DeadLetterQueue = "dead-letter-queue";
    public const string RetryQueue = "retry-queue";
}

/// <summary>
/// Channel names for real-time events
/// </summary>
public static class ChannelNames
{
    public const string NotificationSent = "notification-sent";
    public const string NotificationFailed = "notification-failed";
    public const string PluginLoaded = "plugin-loaded";
    public const string PluginUnloaded = "plugin-unloaded";
    public const string HealthStatusChanged = "health-status-changed";
    public const string MetricsUpdated = "metrics-updated";
}

/// <summary>
/// Message models for queue operations
/// </summary>
public record QueuedNotification(
    string Id,
    string Type,
    object Payload,
    DateTime QueuedAt,
    int RetryCount = 0,
    DateTime? ScheduledFor = null
);

public record NotificationEvent(
    string NotificationId,
    string Type,
    string Status,
    string? Provider,
    DateTime Timestamp,
    string? ErrorMessage = null
);

public record PluginEvent(
    string PluginName,
    string Action,
    bool Success,
    DateTime Timestamp,
    string? ErrorMessage = null
);

public record HealthStatusEvent(
    string ComponentName,
    string Status,
    DateTime Timestamp,
    Dictionary<string, object>? Details = null
);

public record MetricsEvent(
    string MetricType,
    Dictionary<string, object> Metrics,
    DateTime Timestamp
);
