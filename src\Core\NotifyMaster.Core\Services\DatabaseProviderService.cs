using NotifyMaster.Database;

namespace NotifyMaster.Core.Services;

/// <summary>
/// Service for configuring database providers based on configuration
/// </summary>
public static class DatabaseProviderService
{
    /// <summary>
    /// Configure the database context with the appropriate provider
    /// </summary>
    public static IServiceCollection AddDatabaseProvider(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        var databaseProvider = configuration["Database:Provider"] ?? DetectProviderFromConnectionString(connectionString);

        if (string.IsNullOrEmpty(connectionString))
        {
            Console.WriteLine("⚠️  No database connection string found - using in-memory fallback");
            return services.AddInMemoryDatabase();
        }

        return databaseProvider.ToLower() switch
        {
            "sqlserver" => services.AddSqlServerDatabase(connectionString),
            "mysql" => services.AddMySqlDatabase(connectionString),
            "postgresql" => services.AddPostgreSqlDatabase(connectionString),
            "sqlite" => services.AddSqliteDatabase(connectionString),
            "inmemory" => services.AddInMemoryDatabase(),
            _ => services.AddAutoDetectDatabase(connectionString)
        };
    }

    /// <summary>
    /// Add SQL Server database provider
    /// </summary>
    private static IServiceCollection AddSqlServerDatabase(this IServiceCollection services, string connectionString)
    {
        services.AddDbContext<NotifyMasterDbContext>(options =>
        {
            options.UseSqlServer(connectionString, sqlOptions =>
            {
                sqlOptions.MigrationsAssembly("NotifyMasterApi");
                sqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorNumbersToAdd: null);
            });
            options.EnableSensitiveDataLogging(false);
            options.EnableServiceProviderCaching();
        });

        Console.WriteLine("✅ Configured SQL Server database provider");
        return services;
    }

    /// <summary>
    /// Add MySQL database provider
    /// </summary>
    private static IServiceCollection AddMySqlDatabase(this IServiceCollection services, string connectionString)
    {
        services.AddDbContext<NotifyMasterDbContext>(options =>
        {
            options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mysqlOptions =>
            {
                mysqlOptions.MigrationsAssembly("NotifyMasterApi");
                mysqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorNumbersToAdd: null);
            });
            options.EnableSensitiveDataLogging(false);
            options.EnableServiceProviderCaching();
        });

        Console.WriteLine("✅ Configured MySQL database provider");
        return services;
    }

    /// <summary>
    /// Add PostgreSQL database provider
    /// </summary>
    private static IServiceCollection AddPostgreSqlDatabase(this IServiceCollection services, string connectionString)
    {
        services.AddDbContext<NotifyMasterDbContext>(options =>
        {
            options.UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.MigrationsAssembly("NotifyMasterApi");
                npgsqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorCodesToAdd: null);
            });
            options.EnableSensitiveDataLogging(false);
            options.EnableServiceProviderCaching();
        });

        Console.WriteLine("✅ Configured PostgreSQL database provider");
        return services;
    }

    /// <summary>
    /// Add SQLite database provider
    /// </summary>
    private static IServiceCollection AddSqliteDatabase(this IServiceCollection services, string connectionString)
    {
        services.AddDbContext<NotifyMasterDbContext>(options =>
        {
            options.UseSqlite(connectionString, sqliteOptions =>
            {
                sqliteOptions.MigrationsAssembly("NotifyMasterApi");
            });
            options.EnableSensitiveDataLogging(false);
            options.EnableServiceProviderCaching();
        });

        Console.WriteLine("✅ Configured SQLite database provider");
        return services;
    }

    /// <summary>
    /// Add In-Memory database provider (for testing/fallback)
    /// </summary>
    private static IServiceCollection AddInMemoryDatabase(this IServiceCollection services)
    {
        services.AddDbContext<NotifyMasterDbContext>(options =>
        {
            options.UseInMemoryDatabase("NotifyMasterInMemory");
            options.EnableSensitiveDataLogging(false);
        });

        Console.WriteLine("✅ Configured In-Memory database provider");
        return services;
    }

    /// <summary>
    /// Auto-detect database provider from connection string and configure accordingly
    /// </summary>
    private static IServiceCollection AddAutoDetectDatabase(this IServiceCollection services, string connectionString)
    {
        var detectedProvider = DetectProviderFromConnectionString(connectionString);
        Console.WriteLine($"🔍 Auto-detected database provider: {detectedProvider}");

        return detectedProvider.ToLower() switch
        {
            "sqlserver" => services.AddSqlServerDatabase(connectionString),
            "mysql" => services.AddMySqlDatabase(connectionString),
            "postgresql" => services.AddPostgreSqlDatabase(connectionString),
            "sqlite" => services.AddSqliteDatabase(connectionString),
            _ => services.AddInMemoryDatabase() // Fallback to in-memory
        };
    }

    /// <summary>
    /// Detect database provider from connection string patterns
    /// </summary>
    private static string DetectProviderFromConnectionString(string? connectionString)
    {
        if (string.IsNullOrEmpty(connectionString))
            return "inmemory";

        var lowerConnectionString = connectionString.ToLower();

        // SQL Server patterns
        if (lowerConnectionString.Contains("server=") && 
            (lowerConnectionString.Contains("database=") || lowerConnectionString.Contains("initial catalog=")))
            return "sqlserver";

        // MySQL patterns
        if (lowerConnectionString.Contains("server=") && lowerConnectionString.Contains("uid="))
            return "mysql";

        // PostgreSQL patterns
        if (lowerConnectionString.Contains("host=") && lowerConnectionString.Contains("database="))
            return "postgresql";

        // SQLite patterns
        if (lowerConnectionString.Contains("data source=") && 
            (lowerConnectionString.Contains(".db") || lowerConnectionString.Contains(".sqlite")))
            return "sqlite";

        // Default fallback
        return "inmemory";
    }

    /// <summary>
    /// Get supported database providers with metadata
    /// </summary>
    public static List<DatabaseProviderInfo> GetSupportedProviders()
    {
        return new List<DatabaseProviderInfo>
        {
            new("SQL Server", "sqlserver", "Microsoft SQL Server database", 1433, true),
            new("MySQL", "mysql", "MySQL database server", 3306, true),
            new("PostgreSQL", "postgresql", "PostgreSQL database server", 5432, true),
            new("SQLite", "sqlite", "SQLite file-based database", 0, false),
            new("In-Memory", "inmemory", "In-memory database for testing", 0, false)
        };
    }
}

/// <summary>
/// Information about a database provider
/// </summary>
/// <param name="DisplayName">Human-readable name</param>
/// <param name="ProviderName">Internal provider identifier</param>
/// <param name="Description">Description of the provider</param>
/// <param name="DefaultPort">Default port for the database</param>
/// <param name="RequiresCredentials">Whether this provider requires username/password</param>
public record DatabaseProviderInfo(
    string DisplayName,
    string ProviderName,
    string Description,
    int DefaultPort,
    bool RequiresCredentials
);
