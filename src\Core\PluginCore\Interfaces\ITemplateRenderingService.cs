namespace PluginCore.Interfaces;

/// <summary>
/// Service for rendering message templates with variable substitution
/// </summary>
public interface ITemplateRenderingService
{
    /// <summary>
    /// Render a template with the provided variables
    /// </summary>
    /// <param name="templateContent">The template content with {{variable}} placeholders</param>
    /// <param name="variables">Dictionary of variables to substitute</param>
    /// <returns>Rendered template content</returns>
    Task<string> RenderTemplateAsync(string templateContent, Dictionary<string, object> variables);
    
    /// <summary>
    /// Render a template with validation and detailed results
    /// </summary>
    /// <param name="templateId">Template identifier for logging</param>
    /// <param name="variables">Dictionary of variables to substitute</param>
    /// <returns>Detailed render result with validation information</returns>
    Task<TemplateRenderResult> RenderTemplateWithValidationAsync(string templateId, Dictionary<string, object> variables);
    
    /// <summary>
    /// Extract all variable names from a template
    /// </summary>
    /// <param name="templateContent">The template content to analyze</param>
    /// <returns>List of variable names found in the template</returns>
    Task<List<string>> ExtractVariablesFromTemplateAsync(string templateContent);
    
    /// <summary>
    /// Validate template syntax
    /// </summary>
    /// <param name="templateContent">The template content to validate</param>
    /// <returns>True if template is valid, false otherwise</returns>
    Task<bool> ValidateTemplateAsync(string templateContent);
}
