{"sourceFile": "src/Implementations/NotifyMasterApi/Features/Email/SendNotificationMessageHandler.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751231208244, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751231208244, "name": "Commit-0", "content": "using NotifyMaster.Core.Interfaces;\nusing NotifyMaster.Core.Models;\nusing static NotifyMaster.Core.Interfaces.Messages;\nusing NotifyMasterApi.Gateways;\nusing NotificationContract.Models;\n\nnamespace NotifyMasterApi.Features.Email;\n\n/// <summary>\n/// Handler for processing email notification messages from the queue\n/// Implements the queue-first pattern: Queue → Database → Send → Response\n/// </summary>\npublic class SendNotificationMessageHandler : MessageHandlerBase<SendNotificationMessage>\n{\n    private readonly IEmailGateway _emailGateway;\n    private readonly ITenantService _tenantService;\n    private readonly IUserService _userService;\n    private readonly ITenantContext _tenantContext;\n\n    public SendNotificationMessageHandler(\n        ILogger<SendNotificationMessageHandler> logger,\n        IEmailGateway emailGateway,\n        ITenantService tenantService,\n        IUserService userService,\n        ITenantContext tenantContext) : base(logger)\n    {\n        _emailGateway = emailGateway;\n        _tenantService = tenantService;\n        _userService = userService;\n        _tenantContext = tenantContext;\n    }\n\n    public override string QueueName => QueueNames.EmailProcessing;\n\n    public override async Task<OperationResult> HandleAsync(\n        SendNotificationMessage message, \n        QueueMessage<SendNotificationMessage> queueMessage, \n        CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            Logger.LogInformation(\"Processing email notification {MessageId} for recipient {Recipient}\", \n                message.Id, message.Recipient);\n\n            // Step 1: Validate tenant and user context\n            var validationResult = await ValidateContextAsync(message, cancellationToken);\n            if (!validationResult.IsSuccess)\n            {\n                return validationResult;\n            }\n\n            // Step 2: Save to database first (audit trail)\n            var messageRecord = await SaveMessageToDatabase(message, queueMessage, cancellationToken);\n            if (messageRecord == null)\n            {\n                return OperationResult.Failure(\"Failed to save message to database\");\n            }\n\n            // Step 3: Send the email\n            var sendResult = await SendEmailAsync(message, cancellationToken);\n            \n            // Step 4: Update database with result\n            await UpdateMessageStatus(messageRecord, sendResult, cancellationToken);\n\n            // Step 5: Send response/webhook if needed\n            if (sendResult.IsSuccess)\n            {\n                await SendSuccessResponse(message, sendResult, cancellationToken);\n                Logger.LogInformation(\"Successfully processed email notification {MessageId}\", message.Id);\n                return OperationResult.Success(\"Email sent successfully\");\n            }\n            else\n            {\n                await SendFailureResponse(message, sendResult, cancellationToken);\n                Logger.LogError(\"Failed to send email notification {MessageId}: {Error}\", message.Id, sendResult.Message);\n                return OperationResult.Failure($\"Failed to send email: {sendResult.Message}\");\n            }\n        }\n        catch (Exception ex)\n        {\n            Logger.LogError(ex, \"Unexpected error processing email notification {MessageId}\", message.Id);\n            return OperationResult.Failure($\"Unexpected error: {ex.Message}\", ex);\n        }\n    }\n\n    private async Task<OperationResult> ValidateContextAsync(SendNotificationMessage message, CancellationToken cancellationToken)\n    {\n        try\n        {\n            // Validate tenant\n            if (!string.IsNullOrEmpty(message.TenantId))\n            {\n                var tenant = await _tenantService.GetTenantAsync(message.TenantId, cancellationToken);\n                if (tenant == null)\n                {\n                    return OperationResult.Failure($\"Tenant {message.TenantId} not found\");\n                }\n\n                if (tenant.Status != TenantStatus.Active)\n                {\n                    return OperationResult.Failure($\"Tenant {message.TenantId} is not active\");\n                }\n\n                // Set tenant context\n                _tenantContext.SetTenant(tenant.Id, tenant.Domain);\n            }\n\n            // Validate user if specified\n            if (!string.IsNullOrEmpty(message.UserId))\n            {\n                var user = await _userService.GetUserAsync(message.UserId, cancellationToken);\n                if (user == null)\n                {\n                    return OperationResult.Failure($\"User {message.UserId} not found\");\n                }\n\n                if (user.Status != UserStatus.Active)\n                {\n                    return OperationResult.Failure($\"User {message.UserId} is not active\");\n                }\n            }\n\n            return OperationResult.Success(\"Context validation passed\");\n        }\n        catch (Exception ex)\n        {\n            Logger.LogError(ex, \"Error validating context for message {MessageId}\", message.Id);\n            return OperationResult.Failure(\"Context validation failed\", ex);\n        }\n    }\n\n    private async Task<MessageRecord?> SaveMessageToDatabase(\n        SendNotificationMessage message, \n        QueueMessage<SendNotificationMessage> queueMessage, \n        CancellationToken cancellationToken)\n    {\n        try\n        {\n            var messageRecord = new MessageRecord\n            {\n                Id = message.Id,\n                TenantId = message.TenantId,\n                UserId = message.UserId,\n                Type = \"Email\",\n                Recipient = message.Recipient,\n                Subject = message.Subject,\n                Content = message.Content,\n                Status = MessageStatus.Processing,\n                QueuedAt = queueMessage.EnqueuedAt,\n                ProcessingStartedAt = DateTime.UtcNow,\n                CorrelationId = message.CorrelationId,\n                Metadata = message.Metadata,\n                Headers = message.Headers\n            };\n\n            await SaveToDatabase(messageRecord, cancellationToken);\n            Logger.LogDebug(\"Saved message {MessageId} to database\", message.Id);\n            return messageRecord;\n        }\n        catch (Exception ex)\n        {\n            Logger.LogError(ex, \"Failed to save message {MessageId} to database\", message.Id);\n            return null;\n        }\n    }\n\n    private async Task<OperationResult> SendEmailAsync(SendNotificationMessage message, CancellationToken cancellationToken)\n    {\n        try\n        {\n            var emailRequest = new EmailMessageRequest\n            {\n                To = message.Recipient,\n                From = message.From,\n                Subject = message.Subject ?? \"\",\n                Body = message.Content,\n                HtmlBody = message.Metadata.TryGetValue(\"HtmlBody\", out var htmlBody) ? htmlBody?.ToString() : null,\n                PlainTextBody = message.Metadata.TryGetValue(\"PlainTextBody\", out var plainBody) ? plainBody?.ToString() : null,\n                Headers = message.Headers,\n                Category = message.Metadata.TryGetValue(\"Category\", out var category) ? category?.ToString() : null\n            };\n\n            // Add CC and BCC if specified\n            if (message.Metadata.TryGetValue(\"Cc\", out var cc) && !string.IsNullOrEmpty(cc?.ToString()))\n            {\n                emailRequest.Cc = new List<string> { cc.ToString()! };\n            }\n\n            if (message.Metadata.TryGetValue(\"Bcc\", out var bcc) && !string.IsNullOrEmpty(bcc?.ToString()))\n            {\n                emailRequest.Bcc = new List<string> { bcc.ToString()! };\n            }\n\n            var result = await _emailGateway.SendAsync(emailRequest);\n            \n            if (result.IsSuccess)\n            {\n                return OperationResult.Success($\"Email sent successfully. Message ID: {result.MessageId}\");\n            }\n            else\n            {\n                return OperationResult.Failure(result.ErrorMessage ?? \"Unknown error occurred\");\n            }\n        }\n        catch (Exception ex)\n        {\n            Logger.LogError(ex, \"Error sending email for message {MessageId}\", message.Id);\n            return OperationResult.Failure(\"Failed to send email\", ex);\n        }\n    }\n\n    private async Task UpdateMessageStatus(MessageRecord messageRecord, OperationResult sendResult, CancellationToken cancellationToken)\n    {\n        try\n        {\n            messageRecord.Status = sendResult.IsSuccess ? MessageStatus.Sent : MessageStatus.Failed;\n            messageRecord.ProcessingCompletedAt = DateTime.UtcNow;\n            messageRecord.ErrorMessage = sendResult.IsSuccess ? null : sendResult.Message;\n\n            await SaveToDatabase(messageRecord, cancellationToken);\n            Logger.LogDebug(\"Updated message {MessageId} status to {Status}\", messageRecord.Id, messageRecord.Status);\n        }\n        catch (Exception ex)\n        {\n            Logger.LogError(ex, \"Failed to update message {MessageId} status\", messageRecord.Id);\n        }\n    }\n\n    private async Task SendSuccessResponse(SendNotificationMessage message, OperationResult sendResult, CancellationToken cancellationToken)\n    {\n        try\n        {\n            // Send webhook or response if needed\n            // This could trigger another queue message for webhook delivery\n            Logger.LogDebug(\"Sending success response for message {MessageId}\", message.Id);\n            await Task.CompletedTask; // Placeholder for webhook/response logic\n        }\n        catch (Exception ex)\n        {\n            Logger.LogError(ex, \"Failed to send success response for message {MessageId}\", message.Id);\n        }\n    }\n\n    private async Task SendFailureResponse(SendNotificationMessage message, OperationResult sendResult, CancellationToken cancellationToken)\n    {\n        try\n        {\n            // Send failure webhook or response if needed\n            Logger.LogDebug(\"Sending failure response for message {MessageId}\", message.Id);\n            await Task.CompletedTask; // Placeholder for webhook/response logic\n        }\n        catch (Exception ex)\n        {\n            Logger.LogError(ex, \"Failed to send failure response for message {MessageId}\", message.Id);\n        }\n    }\n}\n\n/// <summary>\n/// Database record for tracking messages\n/// </summary>\npublic class MessageRecord\n{\n    public string Id { get; set; } = string.Empty;\n    public string TenantId { get; set; } = string.Empty;\n    public string UserId { get; set; } = string.Empty;\n    public string Type { get; set; } = string.Empty; // Email, SMS, Push, etc.\n    public string Recipient { get; set; } = string.Empty;\n    public string? Subject { get; set; }\n    public string Content { get; set; } = string.Empty;\n    public MessageStatus Status { get; set; }\n    public DateTime QueuedAt { get; set; }\n    public DateTime? ProcessingStartedAt { get; set; }\n    public DateTime? ProcessingCompletedAt { get; set; }\n    public string? CorrelationId { get; set; }\n    public string? ErrorMessage { get; set; }\n    public Dictionary<string, object> Metadata { get; set; } = new();\n    public Dictionary<string, string> Headers { get; set; } = new();\n}\n\n/// <summary>\n/// Message status enumeration\n/// </summary>\npublic enum MessageStatus\n{\n    Queued = 0,\n    Processing = 1,\n    Sent = 2,\n    Failed = 3,\n    Cancelled = 4\n}\n"}]}