﻿"restore":{"projectUniqueName":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj","projectName":"NotifyMaster.Core","projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj","outputPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj":{"projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj"},"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj":{"projectPath":"c:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"10.0.100"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"BCrypt.Net-Next":{"target":"Package","version":"[4.0.3, )"},"Hangfire.Core":{"target":"Package","version":"[1.8.20, )"},"Microsoft.AspNetCore.Authentication.JwtBearer":{"target":"Package","version":"[9.0.6, )"},"Microsoft.AspNetCore.Http.Abstractions":{"target":"Package","version":"[2.3.0, )"},"Microsoft.EntityFrameworkCore":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.InMemory":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.SqlServer":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.Sqlite":{"target":"Package","version":"[9.0.6, )"},"Microsoft.Extensions.Configuration.Abstractions":{"target":"Package","version":"[9.0.6, )"},"Microsoft.Extensions.DependencyInjection.Abstractions":{"target":"Package","version":"[9.0.6, )"},"Microsoft.Extensions.Logging.Abstractions":{"target":"Package","version":"[9.0.6, )"},"Npgsql.EntityFrameworkCore.PostgreSQL":{"target":"Package","version":"[9.0.4, )"},"Pomelo.EntityFrameworkCore.MySql":{"target":"Package","version":"[9.0.0-preview.1, )"},"System.IdentityModel.Tokens.Jwt":{"target":"Package","version":"[8.12.1, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.NETCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[9.0.5, 9.0.5]"}],"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json"}}