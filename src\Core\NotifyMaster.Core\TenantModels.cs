namespace PluginCore.Models;

/// <summary>
/// Represents a tenant in the multi-tenant system
/// </summary>
public class Tenant
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required, MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required, MaxLength(100)]
    public string Domain { get; set; } = string.Empty;
    
    public TenantStatus Status { get; set; } = TenantStatus.Active;
    
    public TenantPlan Plan { get; set; } = TenantPlan.Basic;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? CreatedBy { get; set; }
    
    public Dictionary<string, object> Settings { get; set; } = new();
    
    public TenantLimits Limits { get; set; } = new();
    
    public TenantUsage Usage { get; set; } = new();
    
    // Navigation properties
    public List<User> Users { get; set; } = new();
    public List<TenantPlugin> Plugins { get; set; } = new();
}

/// <summary>
/// Tenant status enumeration
/// </summary>
public enum TenantStatus
{
    Active = 0,
    Suspended = 1,
    Inactive = 2,
    Deleted = 3
}

/// <summary>
/// Tenant plan enumeration
/// </summary>
public enum TenantPlan
{
    Basic = 0,
    Professional = 1,
    Enterprise = 2,
    Custom = 3
}

/// <summary>
/// Tenant usage limits
/// </summary>
public class TenantLimits
{
    public int MaxUsers { get; set; } = 10;
    public int MaxMessagesPerMonth { get; set; } = 10000;
    public int MaxPlugins { get; set; } = 5;
    public int MaxTemplates { get; set; } = 50;
    public int MaxWebhooks { get; set; } = 10;
    public bool CanUseCustomDomain { get; set; } = false;
    public bool CanUseAdvancedFeatures { get; set; } = false;
    public Dictionary<string, object> CustomLimits { get; set; } = new();
}

/// <summary>
/// Current tenant usage statistics
/// </summary>
public class TenantUsage
{
    public int CurrentUsers { get; set; }
    public int MessagesThisMonth { get; set; }
    public int ActivePlugins { get; set; }
    public int Templates { get; set; }
    public int Webhooks { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> CustomUsage { get; set; } = new();
}

/// <summary>
/// Represents a user in the system
/// </summary>
public class User
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string TenantId { get; set; } = string.Empty;
    
    [Required, MaxLength(100)]
    public string Username { get; set; } = string.Empty;
    
    [Required, MaxLength(255), EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? FirstName { get; set; }
    
    [MaxLength(100)]
    public string? LastName { get; set; }
    
    [Required]
    public string PasswordHash { get; set; } = string.Empty;
    
    public UserStatus Status { get; set; } = UserStatus.Active;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public DateTime? LastLoginAt { get; set; }
    
    public string? CreatedBy { get; set; }
    
    public Dictionary<string, object> Profile { get; set; } = new();
    
    // Navigation properties
    public Tenant Tenant { get; set; } = null!;
    public List<UserRole> Roles { get; set; } = new();
    public List<UserPermission> Permissions { get; set; } = new();
}

/// <summary>
/// User status enumeration
/// </summary>
public enum UserStatus
{
    Active = 0,
    Inactive = 1,
    Suspended = 2,
    PendingActivation = 3,
    Deleted = 4
}

/// <summary>
/// Represents a role in the system
/// </summary>
public class Role
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required, MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public RoleScope Scope { get; set; } = RoleScope.Tenant;
    
    public bool IsSystemRole { get; set; } = false;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public List<RolePermission> Permissions { get; set; } = new();
    public List<UserRole> Users { get; set; } = new();
}

/// <summary>
/// Role scope enumeration
/// </summary>
public enum RoleScope
{
    System = 0,    // System-wide role
    Tenant = 1     // Tenant-specific role
}

/// <summary>
/// Represents a permission in the system
/// </summary>
public class Permission
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required, MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required, MaxLength(50)]
    public string Resource { get; set; } = string.Empty;
    
    [Required, MaxLength(50)]
    public string Action { get; set; } = string.Empty;
    
    public bool IsSystemPermission { get; set; } = false;
    
    // Navigation properties
    public List<RolePermission> Roles { get; set; } = new();
    public List<UserPermission> Users { get; set; } = new();
}

/// <summary>
/// Many-to-many relationship between users and roles
/// </summary>
public class UserRole
{
    public string UserId { get; set; } = string.Empty;
    public string RoleId { get; set; } = string.Empty;
    public string? TenantId { get; set; }
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    public string? AssignedBy { get; set; }
    
    // Navigation properties
    public User User { get; set; } = null!;
    public Role Role { get; set; } = null!;
}

/// <summary>
/// Many-to-many relationship between users and permissions
/// </summary>
public class UserPermission
{
    public string UserId { get; set; } = string.Empty;
    public string PermissionId { get; set; } = string.Empty;
    public string? TenantId { get; set; }
    public DateTime GrantedAt { get; set; } = DateTime.UtcNow;
    public string? GrantedBy { get; set; }
    
    // Navigation properties
    public User User { get; set; } = null!;
    public Permission Permission { get; set; } = null!;
}

/// <summary>
/// Many-to-many relationship between roles and permissions
/// </summary>
public class RolePermission
{
    public string RoleId { get; set; } = string.Empty;
    public string PermissionId { get; set; } = string.Empty;
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    public string? AssignedBy { get; set; }
    
    // Navigation properties
    public Role Role { get; set; } = null!;
    public Permission Permission { get; set; } = null!;
}

/// <summary>
/// Represents tenant-specific plugin configuration
/// </summary>
public class TenantPlugin
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string TenantId { get; set; } = string.Empty;
    
    [Required, MaxLength(100)]
    public string PluginName { get; set; } = string.Empty;
    
    public bool IsEnabled { get; set; } = true;
    
    public Dictionary<string, object> Configuration { get; set; } = new();
    
    public DateTime ConfiguredAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public string? ConfiguredBy { get; set; }
    
    // Navigation properties
    public Tenant Tenant { get; set; } = null!;
}
