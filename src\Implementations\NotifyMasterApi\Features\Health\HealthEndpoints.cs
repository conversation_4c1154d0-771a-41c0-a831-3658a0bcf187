using System.Diagnostics;
using NotifyMasterApi.Documentation;

namespace NotifyMasterApi.Features.Health;

public class GetHealthEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetHealthEndpoint> _logger;

    public GetHealthEndpoint(IPluginManager pluginManager, ILogger<GetHealthEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        this.ConfigureHealthEndpoint(
            "GET",
            "/api/health",
            "Service Health Check",
            "Get comprehensive health status of the NotificationService including all plugins and system components.\n\n" +
            "## 🎯 Health Monitoring\n" +
            "- **Service Status**: Overall application health\n" +
            "- **Plugin Health**: Individual plugin status and connectivity\n" +
            "- **Database Health**: Database connection and performance\n" +
            "- **External Dependencies**: Third-party service connectivity\n" +
            "- **System Resources**: Memory, CPU, and disk usage\n\n" +
            "## 📊 Health Indicators\n" +
            "- **Healthy**: All systems operational\n" +
            "- **Degraded**: Some non-critical issues detected\n" +
            "- **Unhealthy**: Critical issues affecting functionality\n\n" +
            "## 🔍 Detailed Checks\n" +
            "- Plugin connectivity and configuration\n" +
            "- Database query performance\n" +
            "- External API response times\n" +
            "- Queue depths and processing rates\n" +
            "- Memory usage and garbage collection\n\n" +
            "## 📈 Metrics Included\n" +
            "- Response times for critical operations\n" +
            "- Error rates and success percentages\n" +
            "- Resource utilization statistics\n" +
            "- Plugin-specific health metrics\n\n" +
            "## 🚨 Alerting\n" +
            "This endpoint is used by monitoring systems for:\n" +
            "- Load balancer health checks\n- Kubernetes liveness probes\n- External monitoring services\n- Automated alerting systems",
            new[] { "Monitoring", "System Status", "Diagnostics" }
        );
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            var enabledPlugins = plugins.Where(p => p.IsEnabled).ToList();

            var health = new
            {
                status = "healthy",
                timestamp = DateTime.UtcNow,
                version = "2.0.0",
                services = new
                {
                    email = enabledPlugins.Any(p => p.Type.ToString() == "Email") ? "available" : "unavailable",
                    sms = enabledPlugins.Any(p => p.Type.ToString() == "Sms") ? "available" : "unavailable",
                    push = enabledPlugins.Any(p => p.Type.ToString() == "PushNotification") ? "available" : "unavailable",
                    messaging = enabledPlugins.Any(p => p.Type.ToString() == "Messaging") ? "available" : "unavailable"
                },
                plugins = new
                {
                    total = plugins.Count(),
                    enabled = enabledPlugins.Count,
                    disabled = plugins.Count() - enabledPlugins.Count
                }
            };

            await SendOkAsync(health, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            await SendAsync(new { status = "unhealthy", error = ex.Message, timestamp = DateTime.UtcNow }, 500, ct);
        }
    }
}

public class GetDetailedHealthEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetDetailedHealthEndpoint> _logger;

    public GetDetailedHealthEndpoint(IPluginManager pluginManager, ILogger<GetDetailedHealthEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/health/detailed");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get detailed health information";
            s.Description = "Get comprehensive health information for all components";
            s.Responses[200] = "Detailed health retrieved successfully";
            s.Responses[500] = "Internal server error";
        });
        Tags("Health");
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var plugins = await _pluginManager.GetLoadedPluginsAsync();
            var pluginHealthChecks = new List<object>();

            foreach (var plugin in plugins.Where(p => p.IsEnabled))
            {
                try
                {
                    var healthStatus = await _pluginManager.GetPluginHealthStatusAsync(plugin.Name ?? "");
                    pluginHealthChecks.Add(new
                    {
                        PluginName = plugin.Name,
                        Provider = plugin.Provider,
                        Type = plugin.Type,
                        Status = healthStatus,
                        IsEnabled = plugin.IsEnabled
                    });
                }
                catch (Exception ex)
                {
                    pluginHealthChecks.Add(new
                    {
                        PluginName = plugin.Name,
                        Provider = plugin.Provider,
                        Type = plugin.Type,
                        Status = new { healthy = false, error = ex.Message },
                        IsEnabled = plugin.IsEnabled
                    });
                }
            }

            var detailedHealth = new
            {
                Service = new
                {
                    Status = "Running",
                    Version = "2.0.0",
                    Timestamp = DateTime.UtcNow,
                    Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime
                },
                Plugins = pluginHealthChecks,
                Infrastructure = new
                {
                    Database = new { Status = "InMemory", Type = "EntityFramework" },
                    Logging = new { Status = "Active", Provider = "Serilog" },
                    ApiDocumentation = new { Status = "Active", Provider = "Scalar" }
                },
                Features = new
                {
                    PluginManagement = true,
                    MetricsTracking = true,
                    HealthMonitoring = true,
                    FeatureDetection = true
                }
            };

            await SendOkAsync(detailedHealth, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting detailed health information");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetPluginHealthRequest
{
    public string PluginName { get; set; } = string.Empty;
}

public class GetPluginHealthEndpoint : Endpoint<GetPluginHealthRequest, object>
{
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<GetPluginHealthEndpoint> _logger;

    public GetPluginHealthEndpoint(IPluginManager pluginManager, ILogger<GetPluginHealthEndpoint> logger)
    {
        _pluginManager = pluginManager;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/health/plugins/{pluginName}");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get plugin health";
            s.Description = "Get health status for a specific plugin";
            s.Responses[200] = "Plugin health retrieved successfully";
            s.Responses[404] = "Plugin not found";
            s.Responses[500] = "Internal server error";
        });
        Tags("Health");
    }

    public override async Task HandleAsync(GetPluginHealthRequest req, CancellationToken ct)
    {
        try
        {
            var plugin = await _pluginManager.GetPluginAsync(req.PluginName);
            if (plugin == null)
            {
                await SendNotFoundAsync(ct);
                return;
            }

            var healthStatus = await _pluginManager.GetPluginHealthStatusAsync(req.PluginName);
            
            var response = new
            {
                PluginName = req.PluginName,
                Provider = plugin.Provider,
                Type = plugin.Type,
                IsEnabled = plugin.IsEnabled,
                HealthStatus = healthStatus,
                Timestamp = DateTime.UtcNow
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting health status for plugin {PluginName}", req.PluginName);
            await SendErrorsAsync(500, ct);
        }
    }
}
