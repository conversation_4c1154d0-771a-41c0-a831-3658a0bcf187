// Using statements are handled by GlobalUsings.cs

namespace PluginCore.Services;

/// <summary>
/// Interface for tenant context service
/// </summary>
public interface ITenantContext
{
    /// <summary>
    /// Gets the current tenant ID
    /// </summary>
    string? TenantId { get; }
    
    /// <summary>
    /// Gets the current tenant
    /// </summary>
    Tenant? CurrentTenant { get; }
    
    /// <summary>
    /// Gets the current user ID
    /// </summary>
    string? UserId { get; }
    
    /// <summary>
    /// Gets the current user
    /// </summary>
    User? CurrentUser { get; }
    
    /// <summary>
    /// Sets the current tenant context
    /// </summary>
    void SetTenant(string tenantId, Tenant? tenant = null);
    
    /// <summary>
    /// Sets the current user context
    /// </summary>
    void SetUser(string userId, User? user = null);
    
    /// <summary>
    /// Clears the current context
    /// </summary>
    void Clear();
    
    /// <summary>
    /// Checks if the current user has a specific permission
    /// </summary>
    Task<bool> HasPermissionAsync(string resource, string action);
    
    /// <summary>
    /// Checks if the current user has a specific role
    /// </summary>
    Task<bool> HasRoleAsync(string roleName);
    
    /// <summary>
    /// Gets all permissions for the current user
    /// </summary>
    Task<IReadOnlyList<Permission>> GetUserPermissionsAsync();
    
    /// <summary>
    /// Gets all roles for the current user
    /// </summary>
    Task<IReadOnlyList<Role>> GetUserRolesAsync();
}

/// <summary>
/// Implementation of tenant context service
/// </summary>
public class TenantContext : ITenantContext
{
    private readonly ILogger<TenantContext> _logger;
    private readonly ITenantService _tenantService;
    private readonly IUserService _userService;
    
    private string? _tenantId;
    private Tenant? _currentTenant;
    private string? _userId;
    private User? _currentUser;

    public TenantContext(
        ILogger<TenantContext> logger,
        ITenantService tenantService,
        IUserService userService)
    {
        _logger = logger;
        _tenantService = tenantService;
        _userService = userService;
    }

    public string? TenantId => _tenantId;
    public Tenant? CurrentTenant => _currentTenant;
    public string? UserId => _userId;
    public User? CurrentUser => _currentUser;

    public void SetTenant(string tenantId, Tenant? tenant = null)
    {
        _tenantId = tenantId;
        _currentTenant = tenant;
        
        _logger.LogDebug("Tenant context set to: {TenantId}", tenantId);
    }

    public void SetUser(string userId, User? user = null)
    {
        _userId = userId;
        _currentUser = user;
        
        _logger.LogDebug("User context set to: {UserId}", userId);
    }

    public void Clear()
    {
        _tenantId = null;
        _currentTenant = null;
        _userId = null;
        _currentUser = null;
        
        _logger.LogDebug("Tenant context cleared");
    }

    public async Task<bool> HasPermissionAsync(string resource, string action)
    {
        if (string.IsNullOrEmpty(_userId) || string.IsNullOrEmpty(_tenantId))
        {
            return false;
        }

        try
        {
            return await _userService.HasPermissionAsync(_userId, _tenantId, resource, action);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission {Resource}:{Action} for user {UserId}", 
                resource, action, _userId);
            return false;
        }
    }

    public async Task<bool> HasRoleAsync(string roleName)
    {
        if (string.IsNullOrEmpty(_userId) || string.IsNullOrEmpty(_tenantId))
        {
            return false;
        }

        try
        {
            return await _userService.HasRoleAsync(_userId, _tenantId, roleName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking role {RoleName} for user {UserId}", 
                roleName, _userId);
            return false;
        }
    }

    public async Task<IReadOnlyList<Permission>> GetUserPermissionsAsync()
    {
        if (string.IsNullOrEmpty(_userId) || string.IsNullOrEmpty(_tenantId))
        {
            return Array.Empty<Permission>();
        }

        try
        {
            return await _userService.GetUserPermissionsAsync(_userId, _tenantId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions for user {UserId}", _userId);
            return Array.Empty<Permission>();
        }
    }

    public async Task<IReadOnlyList<Role>> GetUserRolesAsync()
    {
        if (string.IsNullOrEmpty(_userId) || string.IsNullOrEmpty(_tenantId))
        {
            return Array.Empty<Role>();
        }

        try
        {
            return await _userService.GetUserRolesAsync(_userId, _tenantId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for user {UserId}", _userId);
            return Array.Empty<Role>();
        }
    }
}

/// <summary>
/// Tenant context middleware for HTTP requests
/// </summary>
public class TenantContextMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TenantContextMiddleware> _logger;

    public TenantContextMiddleware(RequestDelegate next, ILogger<TenantContextMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ITenantContext tenantContext, ITenantService tenantService)
    {
        try
        {
            // Extract tenant information from request
            var tenantId = ExtractTenantId(context);
            var userId = ExtractUserId(context);

            if (!string.IsNullOrEmpty(tenantId))
            {
                // Load tenant information
                var tenant = await tenantService.GetTenantAsync(tenantId);
                tenantContext.SetTenant(tenantId, tenant);
            }

            if (!string.IsNullOrEmpty(userId))
            {
                tenantContext.SetUser(userId);
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in tenant context middleware");
            throw;
        }
        finally
        {
            // Clear context after request
            tenantContext.Clear();
        }
    }

    private string? ExtractTenantId(HttpContext context)
    {
        // Try to get tenant ID from various sources
        
        // 1. From JWT claims
        var tenantClaim = context.User?.FindFirst("tenant_id")?.Value;
        if (!string.IsNullOrEmpty(tenantClaim))
        {
            return tenantClaim;
        }

        // 2. From custom header
        if (context.Request.Headers.TryGetValue("X-Tenant-Id", out var tenantHeader))
        {
            return tenantHeader.FirstOrDefault();
        }

        // 3. From subdomain (e.g., tenant1.notifymaster.com)
        var host = context.Request.Host.Host;
        if (host.Contains('.'))
        {
            var subdomain = host.Split('.')[0];
            if (!string.IsNullOrEmpty(subdomain) && subdomain != "www" && subdomain != "api")
            {
                return subdomain;
            }
        }

        // 4. From query parameter
        if (context.Request.Query.TryGetValue("tenant", out var tenantQuery))
        {
            return tenantQuery.FirstOrDefault();
        }

        return null;
    }

    private string? ExtractUserId(HttpContext context)
    {
        // Extract user ID from JWT claims
        return context.User?.FindFirst("sub")?.Value ?? 
               context.User?.FindFirst("user_id")?.Value;
    }
}

/// <summary>
/// Extension methods for tenant context
/// </summary>
public static class TenantContextExtensions
{
    /// <summary>
    /// Adds tenant context services to the service collection
    /// </summary>
    public static IServiceCollection AddTenantContext(this IServiceCollection services)
    {
        services.AddScoped<ITenantContext, TenantContext>();
        return services;
    }

    /// <summary>
    /// Adds tenant context middleware to the application pipeline
    /// </summary>
    public static IApplicationBuilder UseTenantContext(this IApplicationBuilder app)
    {
        return app.UseMiddleware<TenantContextMiddleware>();
    }
}
