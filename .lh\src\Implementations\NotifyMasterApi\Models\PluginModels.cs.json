{"sourceFile": "src/Implementations/NotifyMasterApi/Models/PluginModels.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751240674690, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751240674690, "name": "Commit-0", "content": "namespace PluginCore.Models;\n\n/// <summary>\n/// Simple plugin information for API responses\n/// </summary>\npublic class Plugin\n{\n    public string Name { get; set; } = string.Empty;\n    public string Version { get; set; } = string.Empty;\n    public string Type { get; set; } = string.Empty;\n    public string Provider { get; set; } = string.Empty;\n    public string Status { get; set; } = string.Empty;\n    public bool IsLoaded { get; set; }\n    public bool IsHealthy { get; set; }\n    public DateTime LastChecked { get; set; }\n    public string? ErrorMessage { get; set; }\n    public List<string> Features { get; set; } = new();\n}\n\n/// <summary>\n/// Plugin list response\n/// </summary>\npublic class PluginListResponse\n{\n    public List<Plugin> Plugins { get; set; } = new();\n    public int Total { get; set; }\n    public int Loaded { get; set; }\n    public int Healthy { get; set; }\n}\n\n/// <summary>\n/// Plugin operation result\n/// </summary>\npublic class PluginOperationResult\n{\n    public bool Success { get; set; }\n    public string Message { get; set; } = string.Empty;\n    public string? PluginName { get; set; }\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\n}\n\n/// <summary>\n/// Load plugin request\n/// </summary>\npublic class LoadPluginRequest\n{\n    public string Name { get; set; } = string.Empty;\n    public string? Path { get; set; }\n}\n\n/// <summary>\n/// Unload plugin request\n/// </summary>\npublic class UnloadPluginRequest\n{\n    public string Name { get; set; } = string.Empty;\n}\n\n/// <summary>\n/// Load plugins from directory request\n/// </summary>\npublic class LoadDirectoryRequest\n{\n    public string Directory { get; set; } = \"plugins\";\n}\n\n/// <summary>\n/// Plugin health status\n/// </summary>\npublic class PluginHealth\n{\n    public string Name { get; set; } = string.Empty;\n    public bool IsHealthy { get; set; }\n    public string Status { get; set; } = string.Empty;\n    public DateTime LastChecked { get; set; }\n    public string? ErrorMessage { get; set; }\n}\n\n/// <summary>\n/// Plugin health list response\n/// </summary>\npublic class PluginHealthResponse\n{\n    public List<PluginHealth> Plugins { get; set; } = new();\n    public int Total { get; set; }\n    public int Healthy { get; set; }\n    public int Unhealthy { get; set; }\n}\n"}]}