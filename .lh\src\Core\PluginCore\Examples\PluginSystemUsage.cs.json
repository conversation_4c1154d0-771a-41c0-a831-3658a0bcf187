{"sourceFile": "src/Core/PluginCore/Examples/PluginSystemUsage.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1751214032400, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751214074998, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -238,24 +238,24 @@\n \n             var scheduledTime = DateTimeOffset.UtcNow.AddMinutes(5);\n             var result = await smsPlugin.ScheduleMessageAsync(payload, scheduledTime);\n             \n-            if (result.IsSuccess)\n+            if (result.Status == \"Scheduled\")\n             {\n-                logger.LogInformation(\"Message scheduled successfully. Schedule ID: {ScheduleId}\", result.ScheduleId);\n-                \n+                logger.LogInformation(\"Message scheduled successfully. Schedule ID: {ScheduleId}\", result.ScheduledMessageId);\n+\n                 // Cancel the scheduled message after 2 minutes (for demo purposes)\n                 await Task.Delay(TimeSpan.FromMinutes(2));\n-                var cancelResult = await smsPlugin.CancelScheduledMessageAsync(result.ScheduleId);\n-                \n+                var cancelResult = await smsPlugin.CancelScheduledMessageAsync(result.ScheduledMessageId);\n+\n                 if (cancelResult.IsSuccess)\n                 {\n                     logger.LogInformation(\"Scheduled message cancelled successfully\");\n                 }\n             }\n             else\n             {\n-                logger.LogWarning(\"Failed to schedule message: {Message}\", result.Message);\n+                logger.LogWarning(\"Failed to schedule message: {Status}\", result.Status);\n             }\n         }\n         catch (Exception ex)\n         {\n"}, {"date": 1751241532208, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -36,9 +36,9 @@\n         // Get logger\n         var logger = serviceProvider.GetRequiredService<ILogger<PluginSystemUsageExample>>();\n         \n         // If auto-load is disabled, load plugins manually\n-        // await serviceProvider.LoadPluginsAsync(\"plugins\", logger);\n+         await serviceProvider.LoadPluginsAsync(\"plugins\", logger);\n \n         // Use the plugins\n         await UsePluginsExample(serviceProvider, logger);\n     }\n"}, {"date": 1751243355005, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -132,13 +132,14 @@\n     private static async Task SendSmsExample(ISmsPlugin smsPlugin, ILogger logger)\n     {\n         try\n         {\n-            var payload = new MessagePayload(\n-                Recipient: \"+**********\",\n-                Content: \"Hello from the new plugin system!\",\n-                From: \"NotificationService\"\n-            );\n+            var payload = new MessagePayload\n+            {\n+                Recipient = new Recipient { PhoneNumber = \"+**********\" },\n+                Content = \"Hello from the new plugin system!\",\n+                From = \"NotificationService\"\n+            };\n \n             var result = await smsPlugin.SendMessageAsync(payload);\n             \n             if (result.Status == \"Sent\")\n"}, {"date": 1751243367482, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -163,14 +163,15 @@\n     private static async Task SendEmailExample(IEmailPlugin emailPlugin, ILogger logger)\n     {\n         try\n         {\n-            var payload = new MessagePayload(\n-                Recipient: \"<EMAIL>\",\n-                Content: \"Hello from the new plugin system!\",\n-                Subject: \"Test Email\",\n-                From: \"<EMAIL>\"\n-            );\n+            var payload = new MessagePayload\n+            {\n+                Recipient = new Recipient { Email = \"<EMAIL>\" },\n+                Content = \"Hello from the new plugin system!\",\n+                Subject = \"Test Email\",\n+                From = \"<EMAIL>\"\n+            };\n \n             var result = await emailPlugin.SendMessageAsync(payload);\n             \n             if (result.Status == \"Sent\")\n"}, {"date": 1751243388250, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -195,13 +195,14 @@\n     private static async Task SendPushExample(IPushPlugin pushPlugin, ILogger logger)\n     {\n         try\n         {\n-            var payload = new MessagePayload(\n-                Recipient: \"device_token_here\",\n-                Content: \"Hello from the new plugin system!\",\n-                Subject: \"Test Push Notification\"\n-            );\n+            var payload = new MessagePayload\n+            {\n+                Recipient = new Recipient { DeviceToken = \"device_token_here\" },\n+                Content = \"Hello from the new plugin system!\",\n+                Subject = \"Test Push Notification\"\n+            };\n \n             var result = await pushPlugin.SendMessageAsync(payload);\n             \n             if (result.Status == \"Sent\")\n"}, {"date": 1751243408174, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -232,13 +232,14 @@\n         }\n \n         try\n         {\n-            var payload = new MessagePayload(\n-                Recipient: \"+**********\",\n-                Content: \"This is a scheduled message!\",\n-                From: \"NotificationService\"\n-            );\n+            var payload = new MessagePayload\n+            {\n+                Recipient = new Recipient { PhoneNumber = \"+**********\" },\n+                Content = \"This is a scheduled message!\",\n+                From = \"NotificationService\"\n+            };\n \n             var scheduledTime = DateTimeOffset.UtcNow.AddMinutes(5);\n             var result = await smsPlugin.ScheduleMessageAsync(payload, scheduledTime);\n             \n"}], "date": 1751214032400, "name": "Commit-0", "content": "using Microsoft.Extensions.DependencyInjection;\nusing Microsoft.Extensions.Hosting;\nusing Microsoft.Extensions.Logging;\nusing PluginCore.Extensions;\nusing PluginCore.Interfaces;\nusing PluginCore.Models;\nusing PluginCore.Services;\n\nnamespace PluginCore.Examples;\n\n/// <summary>\n/// Example demonstrating how to use the new gateway plugin system.\n/// </summary>\npublic class PluginSystemUsageExample\n{\n    /// <summary>\n    /// Example of setting up the plugin system in a console application.\n    /// </summary>\n    public static async Task ConsoleApplicationExample()\n    {\n        // Create service collection\n        var services = new ServiceCollection();\n        \n        // Add logging\n        services.AddLogging();\n        \n        // Add the complete gateway plugin system\n        services.AddCompleteGatewayPluginSystem(\n            pluginDirectory: \"plugins\",\n            autoLoad: true\n        );\n\n        // Build service provider\n        var serviceProvider = services.BuildServiceProvider();\n        \n        // Get logger\n        var logger = serviceProvider.GetRequiredService<ILogger<PluginSystemUsageExample>>();\n        \n        // If auto-load is disabled, load plugins manually\n        // await serviceProvider.LoadPluginsAsync(\"plugins\", logger);\n\n        // Use the plugins\n        await UsePluginsExample(serviceProvider, logger);\n    }\n\n    /// <summary>\n    /// Example of setting up the plugin system in an ASP.NET Core application.\n    /// </summary>\n    public static void AspNetCoreExample(IServiceCollection services)\n    {\n        // Add the complete gateway plugin system\n        services.AddCompleteGatewayPluginSystem(\n            pluginDirectory: \"plugins\",\n            autoLoad: true\n        );\n\n        // Or configure manually\n        services.AddGatewayPluginSystem()\n               .ConfigureGatewayPluginSystem(options =>\n               {\n                   options.PluginDirectory = \"plugins\";\n                   options.AutoLoadPlugins = true;\n                   options.EnableHealthMonitoring = true;\n                   options.EnableMessageScheduling = true;\n                   options.EnableMessageStorage = true;\n               })\n               .AddPluginLoader();\n    }\n\n    /// <summary>\n    /// Example of using loaded plugins.\n    /// </summary>\n    public static async Task UsePluginsExample(IServiceProvider serviceProvider, ILogger logger)\n    {\n        try\n        {\n            // Get plugin manager\n            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();\n            \n            // Check plugin status\n            var statuses = await pluginManager.GetPluginStatusesAsync();\n            logger.LogInformation(\"Found {Count} loaded plugins\", statuses.Count);\n            \n            foreach (var status in statuses)\n            {\n                logger.LogInformation(\"Plugin: {Name} v{Version} - Status: {Status}\", \n                    status.Name, status.Version, status.Status);\n            }\n\n            // Get SMS plugins\n            var smsPlugins = pluginManager.GetPlugins<ISmsPlugin>();\n            if (smsPlugins.Any())\n            {\n                logger.LogInformation(\"Found {Count} SMS plugins\", smsPlugins.Count);\n                \n                var smsPlugin = smsPlugins.First();\n                await SendSmsExample(smsPlugin, logger);\n            }\n\n            // Get Email plugins\n            var emailPlugins = pluginManager.GetPlugins<IEmailPlugin>();\n            if (emailPlugins.Any())\n            {\n                logger.LogInformation(\"Found {Count} Email plugins\", emailPlugins.Count);\n                \n                var emailPlugin = emailPlugins.First();\n                await SendEmailExample(emailPlugin, logger);\n            }\n\n            // Get Push plugins\n            var pushPlugins = pluginManager.GetPlugins<IPushPlugin>();\n            if (pushPlugins.Any())\n            {\n                logger.LogInformation(\"Found {Count} Push plugins\", pushPlugins.Count);\n                \n                var pushPlugin = pushPlugins.First();\n                await SendPushExample(pushPlugin, logger);\n            }\n\n            // Schedule a message example\n            await ScheduleMessageExample(smsPlugins.FirstOrDefault(), logger);\n        }\n        catch (Exception ex)\n        {\n            logger.LogError(ex, \"Error using plugins\");\n        }\n    }\n\n    /// <summary>\n    /// Example of sending an SMS message.\n    /// </summary>\n    private static async Task SendSmsExample(ISmsPlugin smsPlugin, ILogger logger)\n    {\n        try\n        {\n            var payload = new MessagePayload(\n                Recipient: \"+**********\",\n                Content: \"Hello from the new plugin system!\",\n                From: \"NotificationService\"\n            );\n\n            var result = await smsPlugin.SendMessageAsync(payload);\n            \n            if (result.Status == \"Sent\")\n            {\n                logger.LogInformation(\"SMS sent successfully. Message ID: {MessageId}\", result.MessageId);\n            }\n            else\n            {\n                logger.LogWarning(\"SMS failed to send. Status: {Status}\", result.Status);\n            }\n        }\n        catch (Exception ex)\n        {\n            logger.LogError(ex, \"Failed to send SMS\");\n        }\n    }\n\n    /// <summary>\n    /// Example of sending an email message.\n    /// </summary>\n    private static async Task SendEmailExample(IEmailPlugin emailPlugin, ILogger logger)\n    {\n        try\n        {\n            var payload = new MessagePayload(\n                Recipient: \"<EMAIL>\",\n                Content: \"Hello from the new plugin system!\",\n                Subject: \"Test Email\",\n                From: \"<EMAIL>\"\n            );\n\n            var result = await emailPlugin.SendMessageAsync(payload);\n            \n            if (result.Status == \"Sent\")\n            {\n                logger.LogInformation(\"Email sent successfully. Message ID: {MessageId}\", result.MessageId);\n            }\n            else\n            {\n                logger.LogWarning(\"Email failed to send. Status: {Status}\", result.Status);\n            }\n        }\n        catch (Exception ex)\n        {\n            logger.LogError(ex, \"Failed to send email\");\n        }\n    }\n\n    /// <summary>\n    /// Example of sending a push notification.\n    /// </summary>\n    private static async Task SendPushExample(IPushPlugin pushPlugin, ILogger logger)\n    {\n        try\n        {\n            var payload = new MessagePayload(\n                Recipient: \"device_token_here\",\n                Content: \"Hello from the new plugin system!\",\n                Subject: \"Test Push Notification\"\n            );\n\n            var result = await pushPlugin.SendMessageAsync(payload);\n            \n            if (result.Status == \"Sent\")\n            {\n                logger.LogInformation(\"Push notification sent successfully. Message ID: {MessageId}\", result.MessageId);\n            }\n            else\n            {\n                logger.LogWarning(\"Push notification failed to send. Status: {Status}\", result.Status);\n            }\n        }\n        catch (Exception ex)\n        {\n            logger.LogError(ex, \"Failed to send push notification\");\n        }\n    }\n\n    /// <summary>\n    /// Example of scheduling a message.\n    /// </summary>\n    private static async Task ScheduleMessageExample(ISmsPlugin? smsPlugin, ILogger logger)\n    {\n        if (smsPlugin == null)\n        {\n            logger.LogWarning(\"No SMS plugin available for scheduling example\");\n            return;\n        }\n\n        try\n        {\n            var payload = new MessagePayload(\n                Recipient: \"+**********\",\n                Content: \"This is a scheduled message!\",\n                From: \"NotificationService\"\n            );\n\n            var scheduledTime = DateTimeOffset.UtcNow.AddMinutes(5);\n            var result = await smsPlugin.ScheduleMessageAsync(payload, scheduledTime);\n            \n            if (result.IsSuccess)\n            {\n                logger.LogInformation(\"Message scheduled successfully. Schedule ID: {ScheduleId}\", result.ScheduleId);\n                \n                // Cancel the scheduled message after 2 minutes (for demo purposes)\n                await Task.Delay(TimeSpan.FromMinutes(2));\n                var cancelResult = await smsPlugin.CancelScheduledMessageAsync(result.ScheduleId);\n                \n                if (cancelResult.IsSuccess)\n                {\n                    logger.LogInformation(\"Scheduled message cancelled successfully\");\n                }\n            }\n            else\n            {\n                logger.LogWarning(\"Failed to schedule message: {Message}\", result.Message);\n            }\n        }\n        catch (Exception ex)\n        {\n            logger.LogError(ex, \"Failed to schedule message\");\n        }\n    }\n\n    /// <summary>\n    /// Example of plugin health monitoring.\n    /// </summary>\n    public static async Task HealthMonitoringExample(IServiceProvider serviceProvider, ILogger logger)\n    {\n        try\n        {\n            var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();\n            \n            // Check health of all plugins\n            var statuses = await pluginManager.GetPluginStatusesAsync();\n            \n            foreach (var status in statuses)\n            {\n                if (status.IsHealthy)\n                {\n                    logger.LogInformation(\"Plugin {Name} is healthy\", status.Name);\n                }\n                else\n                {\n                    logger.LogWarning(\"Plugin {Name} is unhealthy: {ErrorMessage}\", \n                        status.Name, status.ErrorMessage);\n                }\n            }\n        }\n        catch (Exception ex)\n        {\n            logger.LogError(ex, \"Failed to check plugin health\");\n        }\n    }\n\n    /// <summary>\n    /// Example of plugin discovery.\n    /// </summary>\n    public static void PluginDiscoveryExample(ILogger logger)\n    {\n        try\n        {\n            var pluginFiles = PluginDiscovery.DiscoverPluginFiles(\"plugins\");\n            \n            logger.LogInformation(\"Discovered {Count} potential plugin files\", pluginFiles.Count());\n            \n            foreach (var pluginFile in pluginFiles)\n            {\n                var fileInfo = PluginDiscovery.GetPluginFileInfo(pluginFile);\n                if (fileInfo != null)\n                {\n                    logger.LogInformation(\"Plugin file: {FileName} ({Size} bytes, modified: {LastModified})\",\n                        fileInfo.FileName, fileInfo.Size, fileInfo.LastModified);\n                }\n            }\n        }\n        catch (Exception ex)\n        {\n            logger.LogError(ex, \"Failed to discover plugins\");\n        }\n    }\n}\n"}]}