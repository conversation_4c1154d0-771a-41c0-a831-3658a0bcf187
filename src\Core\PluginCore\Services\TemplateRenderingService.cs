using System.Text.RegularExpressions;

namespace PluginCore.Services;

/// <summary>
/// Service for rendering message templates with variable substitution
/// </summary>
public class TemplateRenderingService : ITemplateRenderingService
{
    private readonly ILogger<TemplateRenderingService> _logger;
    private static readonly Regex VariableRegex = new(@"\{\{([^}]+)\}\}", RegexOptions.Compiled);

    public TemplateRenderingService(ILogger<TemplateRenderingService> logger)
    {
        _logger = logger;
    }

    public async Task<string> RenderTemplateAsync(string templateContent, Dictionary<string, object> variables)
    {
        if (string.IsNullOrEmpty(templateContent))
        {
            return string.Empty;
        }

        var rendered = templateContent;

        // Replace variables in the format {{variable_name}}
        var matches = VariableRegex.Matches(templateContent);
        
        foreach (Match match in matches)
        {
            var variableName = match.Groups[1].Value.Trim();
            var placeholder = match.Value;

            if (variables.TryGetValue(variableName, out var value))
            {
                var stringValue = value?.ToString() ?? "";
                rendered = rendered.Replace(placeholder, stringValue);
            }
            else
            {
                // Keep placeholder if variable not found
                _logger.LogWarning("Variable '{VariableName}' not found in template variables", variableName);
            }
        }

        return rendered;
    }

    public async Task<TemplateRenderResult> RenderTemplateWithValidationAsync(string templateId, Dictionary<string, object> variables)
    {
        var result = new TemplateRenderResult();

        try
        {
            // In a real implementation, this would fetch the template from storage
            var templateContent = await GetTemplateContentAsync(templateId);
            
            if (string.IsNullOrEmpty(templateContent))
            {
                result.Errors.Add($"Template '{templateId}' not found");
                return result;
            }

            // Extract all variables from template
            var templateVariables = await ExtractVariablesFromTemplateAsync(templateContent);
            
            // Check for missing variables
            var missingVariables = templateVariables.Where(v => !variables.ContainsKey(v)).ToList();
            result.MissingVariables = missingVariables;
            
            if (missingVariables.Any())
            {
                result.Warnings.Add($"Missing variables: {string.Join(", ", missingVariables)}");
            }

            // Check for unused variables
            var unusedVariables = variables.Keys.Where(k => !templateVariables.Contains(k)).ToList();
            if (unusedVariables.Any())
            {
                result.Warnings.Add($"Unused variables: {string.Join(", ", unusedVariables)}");
            }

            // Render template
            result.RenderedContent = await RenderTemplateAsync(templateContent, variables);
            result.UsedVariables = templateVariables.Where(v => variables.ContainsKey(v)).ToList();
            result.Success = true;

            _logger.LogDebug("Successfully rendered template '{TemplateId}' with {VariableCount} variables", 
                templateId, variables.Count);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Errors.Add($"Error rendering template: {ex.Message}");
            _logger.LogError(ex, "Error rendering template '{TemplateId}'", templateId);
        }

        return result;
    }

    public async Task<List<string>> ExtractVariablesFromTemplateAsync(string templateContent)
    {
        if (string.IsNullOrEmpty(templateContent))
        {
            return new List<string>();
        }

        var variables = new HashSet<string>();
        var matches = VariableRegex.Matches(templateContent);

        foreach (Match match in matches)
        {
            var variableName = match.Groups[1].Value.Trim();
            variables.Add(variableName);
        }

        return variables.ToList();
    }

    public async Task<bool> ValidateTemplateAsync(string templateContent)
    {
        try
        {
            if (string.IsNullOrEmpty(templateContent))
            {
                return false;
            }

            // Check for balanced braces
            var openBraces = templateContent.Count(c => c == '{');
            var closeBraces = templateContent.Count(c => c == '}');
            
            if (openBraces != closeBraces)
            {
                return false;
            }

            // Validate variable syntax
            var matches = VariableRegex.Matches(templateContent);
            foreach (Match match in matches)
            {
                var variableName = match.Groups[1].Value.Trim();
                if (string.IsNullOrEmpty(variableName))
                {
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating template");
            return false;
        }
    }

    private async Task<string> GetTemplateContentAsync(string templateId)
    {
        // In a real implementation, this would fetch from database or storage
        // For now, return a sample template
        return templateId switch
        {
            "welcome_email" => "Hello {{user_name}}, welcome to {{app_name}}! Your account is now active.",
            "password_reset" => "Hi {{user_name}}, click here to reset your password: {{reset_link}}",
            "order_confirmation" => "Thank you {{customer_name}}! Your order #{{order_id}} for {{total_amount}} has been confirmed.",
            _ => ""
        };
    }
}
