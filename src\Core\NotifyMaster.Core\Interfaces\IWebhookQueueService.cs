namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Service for managing webhook queue operations
/// </summary>
public interface IWebhookQueueService
{
    /// <summary>
    /// Queue a webhook for delivery
    /// </summary>
    /// <param name="webhook">The webhook job to queue</param>
    /// <returns>Webhook job ID</returns>
    Task<string> QueueWebhookAsync(OutboundWebhookJob webhook);
    
    /// <summary>
    /// Get webhooks with optional filtering
    /// </summary>
    /// <param name="tenantId">Optional tenant ID filter</param>
    /// <param name="status">Optional status filter</param>
    /// <returns>List of webhook jobs</returns>
    Task<List<OutboundWebhookJob>> GetWebhooksAsync(string? tenantId = null, string? status = null);
    
    /// <summary>
    /// Get a specific webhook by ID
    /// </summary>
    /// <param name="webhookId">Webhook job ID</param>
    /// <returns>Webhook job or null if not found</returns>
    Task<OutboundWebhookJob?> GetWebhookAsync(string webhookId);
    
    /// <summary>
    /// Retry a failed webhook
    /// </summary>
    /// <param name="webhookId">Webhook job ID</param>
    /// <returns>True if retry was initiated successfully</returns>
    Task<bool> RetryWebhookAsync(string webhookId);
    
    /// <summary>
    /// Cancel a pending webhook
    /// </summary>
    /// <param name="webhookId">Webhook job ID</param>
    /// <returns>True if cancellation was successful</returns>
    Task<bool> CancelWebhookAsync(string webhookId);
    
    /// <summary>
    /// Get the retry history for a webhook
    /// </summary>
    /// <param name="webhookId">Webhook job ID</param>
    /// <returns>List of retry results</returns>
    Task<List<WebhookRetryResult>> GetWebhookHistoryAsync(string webhookId);
}
