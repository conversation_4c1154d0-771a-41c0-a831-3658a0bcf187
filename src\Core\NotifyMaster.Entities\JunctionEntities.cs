// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Entities;

/// <summary>
/// Database entity for UserRole junction table
/// </summary>
[Table("UserRoles")]
public class UserRoleEntity
{
    [Key]
    [MaxLength(36)]
    public string UserId { get; set; } = string.Empty;
    
    [Key]
    [MaxLength(36)]
    public string RoleId { get; set; } = string.Empty;
    
    [MaxLength(36)]
    public string? TenantId { get; set; }
    
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    
    [MaxLength(36)]
    public string? AssignedBy { get; set; }
    
    // Navigation properties
    public virtual UserEntity User { get; set; } = null!;
    public virtual RoleEntity Role { get; set; } = null!;

}

/// <summary>
/// Database entity for UserPermission junction table
/// </summary>
[Table("UserPermissions")]
public class UserPermissionEntity
{
    [Key]
    [MaxLength(36)]
    public string UserId { get; set; } = string.Empty;
    
    [Key]
    [MaxLength(36)]
    public string PermissionId { get; set; } = string.Empty;
    
    [MaxLength(36)]
    public string? TenantId { get; set; }
    
    public DateTime GrantedAt { get; set; } = DateTime.UtcNow;
    
    [MaxLength(36)]
    public string? GrantedBy { get; set; }
    
    // Navigation properties
    public virtual UserEntity User { get; set; } = null!;
    public virtual PermissionEntity Permission { get; set; } = null!;

}

/// <summary>
/// Database entity for RolePermission junction table
/// </summary>
[Table("RolePermissions")]
public class RolePermissionEntity
{
    [Key]
    [MaxLength(36)]
    public string RoleId { get; set; } = string.Empty;
    
    [Key]
    [MaxLength(36)]
    public string PermissionId { get; set; } = string.Empty;
    
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    
    [MaxLength(36)]
    public string? AssignedBy { get; set; }
    
    // Navigation properties
    public virtual RoleEntity Role { get; set; } = null!;
    public virtual PermissionEntity Permission { get; set; } = null!;

}

/// <summary>
/// Database entity for TenantPlugin
/// </summary>
[Table("TenantPlugins")]
public class TenantPluginEntity
{
    [Key]
    [MaxLength(36)]
    public string Id { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(36)]
    public string TenantId { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string PluginName { get; set; } = string.Empty;
    
    public bool IsEnabled { get; set; } = true;
    
    [Column(TypeName = "jsonb")]
    public string Configuration { get; set; } = "{}";
    
    public DateTime ConfiguredAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    [MaxLength(36)]
    public string? ConfiguredBy { get; set; }
    
    // Navigation properties
    public virtual TenantEntity Tenant { get; set; } = null!;

}
