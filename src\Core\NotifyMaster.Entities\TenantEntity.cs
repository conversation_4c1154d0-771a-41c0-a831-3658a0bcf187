// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Entities;

/// <summary>
/// Database entity for Tenant
/// </summary>
[Table("Tenants")]
public class TenantEntity
{
    [Key]
    [MaxLength(36)]
    public string Id { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Domain { get; set; } = string.Empty;
    
    public int Status { get; set; } = 0; // TenantStatus enum as int
    
    public int Plan { get; set; } = 0; // TenantPlan enum as int
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    [MaxLength(36)]
    public string? CreatedBy { get; set; }
    
    [Column(TypeName = "jsonb")]
    public string Settings { get; set; } = "{}";
    
    // Limits as separate columns for better querying
    public int MaxUsers { get; set; } = 10;
    public int MaxPlugins { get; set; } = 5;
    public int MaxMessagesPerMonth { get; set; } = 1000;
    public int MaxStorageGB { get; set; } = 1;
    public int MaxApiCallsPerDay { get; set; } = 10000;
    
    [Column(TypeName = "jsonb")]
    public string CustomLimits { get; set; } = "{}";
    
    // Usage as separate columns for better querying
    public int CurrentUsers { get; set; }
    public int CurrentPlugins { get; set; }
    public int MessagesThisMonth { get; set; }
    public double StorageUsedGB { get; set; }
    public int ApiCallsToday { get; set; }
    public DateTime UsageLastUpdated { get; set; } = DateTime.UtcNow;
    
    [Column(TypeName = "jsonb")]
    public string CustomUsage { get; set; } = "{}";
    
    // Navigation properties
    public virtual ICollection<UserEntity> Users { get; set; } = new List<UserEntity>();
    public virtual ICollection<TenantPluginEntity> Plugins { get; set; } = new List<TenantPluginEntity>();

}
