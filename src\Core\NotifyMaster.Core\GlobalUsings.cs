// Global using statements for NotifyMaster.Core project

// System namespaces
global using System;
global using System.Collections.Generic;
global using System.ComponentModel.DataAnnotations;
global using System.IdentityModel.Tokens.Jwt;
global using System.Linq;
global using System.Security.Claims;
global using System.Security.Cryptography;
global using System.Text;
global using System.Threading;
global using System.Threading.Tasks;
// ASP.NET Core
global using Microsoft.AspNetCore.Authentication.JwtBearer;
global using Microsoft.AspNetCore.Builder;
global using Microsoft.AspNetCore.Http;
// Entity Framework
global using Microsoft.EntityFrameworkCore;
global using Pomelo.EntityFrameworkCore.MySql;
// Microsoft Extensions
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Options;
global using Microsoft.IdentityModel.Tokens;
// NotifyMaster namespaces
global using NotifyMaster.Core.Interfaces;
global using NotifyMaster.Core.Models;
global using NotifyMaster.Core.Services;
global using NotifyMaster.Database;
global using NotifyMaster.Entities;
// PluginCore namespaces - using aliases to avoid conflicts
global using PluginNotificationType = PluginCore.Models.NotificationType;
global using PluginNotificationPriority = PluginCore.Models.NotificationPriority;
global using PluginNotificationStatus = PluginCore.Models.NotificationStatus;
global using PluginOperationResult = PluginCore.Models.OperationResult;
global using PluginNotificationLog = PluginCore.Models.NotificationLog;
global using PluginScheduledMessage = PluginCore.Base.ScheduledMessage;
global using PluginValidationResult = PluginCore.Models.ValidationResult;
global using PluginITenantContext = PluginCore.Services.ITenantContext;
global using PluginITemplateRenderingService = PluginCore.Interfaces.ITemplateRenderingService;
// Core aliases for disambiguation
global using CoreITenantContext = NotifyMaster.Core.Interfaces.ITenantContext;
global using CoreIQueueService = NotifyMaster.Core.Interfaces.IQueueService;
global using CoreOperationResult = NotifyMaster.Core.Models.OperationResult;
