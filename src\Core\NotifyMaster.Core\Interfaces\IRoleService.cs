// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Interface for role management service
/// </summary>
public interface IRoleService
{
    /// <summary>
    /// Gets a role by ID
    /// </summary>
    Task<Role?> GetRoleAsync(string roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets a role by name
    /// </summary>
    Task<Role?> GetRoleByNameAsync(string roleName, RoleScope scope = RoleScope.Tenant, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all roles with pagination
    /// </summary>
    Task<IReadOnlyList<Role>> GetRolesAsync(RoleScope? scope = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets roles by tenant
    /// </summary>
    Task<IReadOnlyList<Role>> GetRolesByTenantAsync(string tenantId, int skip = 0, int take = 50, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Creates a new role
    /// </summary>
    Task<OperationResult<Role>> CreateRoleAsync(CreateRoleRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Updates an existing role
    /// </summary>
    Task<OperationResult<Role>> UpdateRoleAsync(string roleId, UpdateRoleRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Deletes a role
    /// </summary>
    Task<OperationResult> DeleteRoleAsync(string roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets role permissions
    /// </summary>
    Task<IReadOnlyList<Permission>> GetRolePermissionsAsync(string roleId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Assigns a permission to a role
    /// </summary>
    Task<OperationResult> AssignPermissionAsync(string roleId, string permissionId, string? assignedBy = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Removes a permission from a role
    /// </summary>
    Task<OperationResult> RemovePermissionAsync(string roleId, string permissionId, CancellationToken cancellationToken = default);
}


