namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Service for managing dead letter queue operations
/// </summary>
public interface IDeadLetterQueueService
{
    /// <summary>
    /// Add a failed message to the dead letter queue
    /// </summary>
    /// <param name="message">The dead letter message to add</param>
    Task AddToDeadLetterQueueAsync(DeadLetterMessage message);
    
    /// <summary>
    /// Get dead letter messages with optional filtering
    /// </summary>
    /// <param name="tenantId">Optional tenant ID filter</param>
    /// <param name="channel">Optional channel filter</param>
    /// <returns>List of dead letter messages</returns>
    Task<List<DeadLetterMessage>> GetDeadLetterMessagesAsync(string? tenantId = null, string? channel = null);
    
    /// <summary>
    /// Get a specific dead letter message by ID
    /// </summary>
    /// <param name="messageId">Dead letter message ID</param>
    /// <returns>Dead letter message or null if not found</returns>
    Task<DeadLetterMessage?> GetDeadLetterMessageAsync(string messageId);
    
    /// <summary>
    /// Attempt to reprocess a dead letter message
    /// </summary>
    /// <param name="messageId">Dead letter message ID</param>
    /// <returns>True if reprocessing was initiated successfully</returns>
    Task<bool> ReprocessMessageAsync(string messageId);
    
    /// <summary>
    /// Archive a dead letter message (mark as resolved)
    /// </summary>
    /// <param name="messageId">Dead letter message ID</param>
    /// <returns>True if archiving was successful</returns>
    Task<bool> ArchiveMessageAsync(string messageId);
    
    /// <summary>
    /// Delete a dead letter message permanently
    /// </summary>
    /// <param name="messageId">Dead letter message ID</param>
    /// <returns>True if deletion was successful</returns>
    Task<bool> DeleteMessageAsync(string messageId);
    
    /// <summary>
    /// Get the count of dead letter messages
    /// </summary>
    /// <param name="tenantId">Optional tenant ID filter</param>
    /// <returns>Number of dead letter messages</returns>
    Task<int> GetDeadLetterCountAsync(string? tenantId = null);
}
