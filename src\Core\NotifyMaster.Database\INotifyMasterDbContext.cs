// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Database;

/// <summary>
/// Interface for the NotifyMaster database context
/// </summary>
public interface INotifyMasterDbContext
{
    // Core Management
    DbSet<TenantEntity> Tenants { get; set; }
    DbSet<UserEntity> Users { get; set; }
    DbSet<RoleEntity> Roles { get; set; }
    DbSet<PermissionEntity> Permissions { get; set; }

    // Junction Tables
    DbSet<UserRoleEntity> UserRoles { get; set; }
    DbSet<UserPermissionEntity> UserPermissions { get; set; }
    DbSet<RolePermissionEntity> RolePermissions { get; set; }
    DbSet<TenantPluginEntity> TenantPlugins { get; set; }

    // Standard DbContext methods
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    int SaveChanges();
    
    // Transaction support
    Task<Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);
}
