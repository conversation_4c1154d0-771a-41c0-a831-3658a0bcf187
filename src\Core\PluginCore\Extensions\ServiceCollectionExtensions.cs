namespace PluginCore.Extensions;

/// <summary>
/// Extension methods for configuring the new gateway-based plugin system.
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds the complete gateway-based plugin system.
    /// </summary>
    public static IServiceCollection AddGatewayPluginSystem(this IServiceCollection services)
    {
        // Core plugin manager
        services.AddSingleton<IPluginManager, PluginManager>();

        // Message scheduling and storage infrastructure
        services.AddSingleton<IMessageSchedulingService, DefaultMessageSchedulingService>();
        services.AddSingleton<IMessageStorageService, DefaultMessageStorageService>();

        // Background processor for scheduled messages
        services.AddHostedService<ScheduledMessageProcessor>();

        // Template rendering service
        services.AddScoped<ITemplateRenderingService, TemplateRenderingService>();

        return services;
    }

    /// <summary>
    /// Registers gateway plugin types for dependency injection.
    /// Call this after loading plugins to make them available for injection.
    /// </summary>
    public static IServiceCollection RegisterGatewayPlugins(this IServiceCollection services)
    {
        // SMS Gateways
        services.AddTransient<ISmsPlugin>(provider =>
        {
            var pluginManager = provider.GetRequiredService<IPluginManager>();
            var plugins = pluginManager.GetPlugins<ISmsPlugin>();
            return plugins.FirstOrDefault() ?? throw new InvalidOperationException("No SMS plugins loaded");
        });

        // Email Gateways
        services.AddTransient<IEmailPlugin>(provider =>
        {
            var pluginManager = provider.GetRequiredService<IPluginManager>();
            var plugins = pluginManager.GetPlugins<IEmailPlugin>();
            return plugins.FirstOrDefault() ?? throw new InvalidOperationException("No Email plugins loaded");
        });

        // Push Gateways
        services.AddTransient<IPushPlugin>(provider =>
        {
            var pluginManager = provider.GetRequiredService<IPluginManager>();
            var plugins = pluginManager.GetPlugins<IPushPlugin>();
            return plugins.FirstOrDefault() ?? throw new InvalidOperationException("No Push plugins loaded");
        });

        // WebApp Gateways
        services.AddTransient<IWebAppPlugin>(provider =>
        {
            var pluginManager = provider.GetRequiredService<IPluginManager>();
            var plugins = pluginManager.GetPlugins<IWebAppPlugin>();
            return plugins.FirstOrDefault() ?? throw new InvalidOperationException("No WebApp plugins loaded");
        });

        // Generic gateway access
        services.AddTransient<IGatewayMessagePluginType>(provider =>
        {
            var pluginManager = provider.GetRequiredService<IPluginManager>();
            var plugins = pluginManager.GetPlugins<IGatewayMessagePluginType>();
            return plugins.FirstOrDefault() ?? throw new InvalidOperationException("No gateway plugins loaded");
        });

        return services;
    }

    /// <summary>
    /// Loads plugins from the specified directory and registers them.
    /// </summary>
    public static async Task<IServiceCollection> LoadAndRegisterPluginsAsync(
        this IServiceCollection services,
        string pluginDirectory,
        IServiceProvider serviceProvider)
    {
        var pluginManager = serviceProvider.GetRequiredService<IPluginManager>();
        var result = await pluginManager.LoadPluginsAsync(pluginDirectory);

        if (!result.IsSuccess)
        {
            throw new InvalidOperationException($"Failed to load plugins: {result.Message}");
        }

        // Register the loaded plugins for dependency injection
        services.RegisterGatewayPlugins();

        return services;
    }

    /// <summary>
    /// Configures the plugin system with options.
    /// </summary>
    public static IServiceCollection ConfigureGatewayPluginSystem(
        this IServiceCollection services,
        Action<GatewayPluginSystemOptions> configure)
    {
        services.Configure(configure);
        return services;
    }
}

/// <summary>
/// Configuration options for the gateway plugin system.
/// </summary>
public class GatewayPluginSystemOptions
{
    /// <summary>
    /// Directory where plugins are located.
    /// </summary>
    public string PluginDirectory { get; set; } = "plugins";

    /// <summary>
    /// Whether to automatically load plugins on startup.
    /// </summary>
    public bool AutoLoadPlugins { get; set; } = true;

    /// <summary>
    /// Whether to enable plugin health monitoring.
    /// </summary>
    public bool EnableHealthMonitoring { get; set; } = true;

    /// <summary>
    /// Interval for checking plugin health.
    /// </summary>
    public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Whether to enable message scheduling.
    /// </summary>
    public bool EnableMessageScheduling { get; set; } = true;

    /// <summary>
    /// Whether to enable message storage for resending.
    /// </summary>
    public bool EnableMessageStorage { get; set; } = true;
}
