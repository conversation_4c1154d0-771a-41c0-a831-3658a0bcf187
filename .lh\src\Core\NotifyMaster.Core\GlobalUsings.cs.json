{"sourceFile": "src/Core/NotifyMaster.Core/GlobalUsings.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 8, "patches": [{"date": 1751229668934, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751230149330, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -28,4 +28,11 @@\n // NotifyMaster.Core namespaces\n global using NotifyMaster.Core.Interfaces;\n global using NotifyMaster.Core.Models;\n global using NotifyMaster.Core.Services;\n+\n+// Aliases to avoid conflicts with PluginCore\n+global using CoreOperationResult = NotifyMaster.Core.Models.OperationResult;\n+global using CoreUser = NotifyMaster.Core.Models.User;\n+global using CoreRole = NotifyMaster.Core.Models.Role;\n+global using CorePermission = NotifyMaster.Core.Models.Permission;\n+global using CoreTenant = NotifyMaster.Core.Models.Tenant;\n"}, {"date": 1751230517452, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -28,11 +28,4 @@\n // NotifyMaster.Core namespaces\n global using NotifyMaster.Core.Interfaces;\n global using NotifyMaster.Core.Models;\n global using NotifyMaster.Core.Services;\n-\n-// Aliases to avoid conflicts with PluginCore\n-global using CoreOperationResult = NotifyMaster.Core.Models.OperationResult;\n-global using CoreUser = NotifyMaster.Core.Models.User;\n-global using CoreRole = NotifyMaster.Core.Models.Role;\n-global using CorePermission = NotifyMaster.Core.Models.Permission;\n-global using CoreTenant = NotifyMaster.Core.Models.Tenant;\n"}, {"date": 1751232046999, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,6 +26,9 @@\n global using Microsoft.IdentityModel.Tokens;\n \n // NotifyMaster.Core namespaces\n global using NotifyMaster.Core.Interfaces;\n-global using NotifyMaster.Core.Models;\n global using NotifyMaster.Core.Services;\n+\n+// Aliases to resolve ambiguous references between Core and Plugin models\n+global using CoreModels = NotifyMaster.Core.Models;\n+global using PluginModels = PluginCore.Models;\n"}, {"date": 1751232347501, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,9 +26,6 @@\n global using Microsoft.IdentityModel.Tokens;\n \n // NotifyMaster.Core namespaces\n global using NotifyMaster.Core.Interfaces;\n+global using NotifyMaster.Core.Models;\n global using NotifyMaster.Core.Services;\n-\n-// Aliases to resolve ambiguous references between Core and Plugin models\n-global using CoreModels = NotifyMaster.Core.Models;\n-global using PluginModels = PluginCore.Models;\n"}, {"date": 1751237058358, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,8 +24,13 @@\n global using Microsoft.AspNetCore.Builder;\n global using Microsoft.AspNetCore.Http;\n global using Microsoft.IdentityModel.Tokens;\n \n-// NotifyMaster.Core namespaces\n+// Entity Framework\n+global using Microsoft.EntityFrameworkCore;\n+\n+// NotifyMaster namespaces\n global using NotifyMaster.Core.Interfaces;\n global using NotifyMaster.Core.Models;\n global using NotifyMaster.Core.Services;\n+global using NotifyMaster.Database;\n+global using NotifyMaster.Entities;\n"}, {"date": 1751238188955, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -31,6 +31,4 @@\n // NotifyMaster namespaces\n global using NotifyMaster.Core.Interfaces;\n global using NotifyMaster.Core.Models;\n global using NotifyMaster.Core.Services;\n-global using NotifyMaster.Database;\n-global using NotifyMaster.Entities;\n"}, {"date": 1751238505200, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -31,4 +31,5 @@\n // NotifyMaster namespaces\n global using NotifyMaster.Core.Interfaces;\n global using NotifyMaster.Core.Models;\n global using NotifyMaster.Core.Services;\n+global using NotifyMaster.Database;\n"}, {"date": 1751238561994, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -31,5 +31,4 @@\n // NotifyMaster namespaces\n global using NotifyMaster.Core.Interfaces;\n global using NotifyMaster.Core.Models;\n global using NotifyMaster.Core.Services;\n-global using NotifyMaster.Database;\n"}], "date": 1751229668934, "name": "Commit-0", "content": "// Global using statements for NotifyMaster.Core project\n\n// System namespaces\nglobal using System;\nglobal using System.Collections.Generic;\nglobal using System.ComponentModel.DataAnnotations;\nglobal using System.IdentityModel.Tokens.Jwt;\nglobal using System.Linq;\nglobal using System.Security.Claims;\nglobal using System.Security.Cryptography;\nglobal using System.Text;\nglobal using System.Threading;\nglobal using System.Threading.Tasks;\n\n// Microsoft Extensions\nglobal using Microsoft.Extensions.Configuration;\nglobal using Microsoft.Extensions.DependencyInjection;\nglobal using Microsoft.Extensions.Logging;\nglobal using Microsoft.Extensions.Options;\n\n// ASP.NET Core\nglobal using Microsoft.AspNetCore.Authentication.JwtBearer;\nglobal using Microsoft.AspNetCore.Authorization;\nglobal using Microsoft.AspNetCore.Builder;\nglobal using Microsoft.AspNetCore.Http;\nglobal using Microsoft.IdentityModel.Tokens;\n\n// NotifyMaster.Core namespaces\nglobal using NotifyMaster.Core.Interfaces;\nglobal using NotifyMaster.Core.Models;\nglobal using NotifyMaster.Core.Services;\n"}]}