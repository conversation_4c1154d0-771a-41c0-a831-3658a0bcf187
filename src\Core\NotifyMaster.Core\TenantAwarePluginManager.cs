using PluginCore.Base;
using PluginCore.Interfaces;
using PluginCore.Models;
using OperationResult = NotifyMaster.Core.Models.OperationResult;
using ValidationResult = PluginCore.Models.ValidationResult;

namespace NotifyMaster.Core.Services;

public interface ITenantAwarePluginManager : IPluginManager
{
    Task<IReadOnlyList<PluginCore.Models.Plugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default);
    Task<T?> GetTenantPluginAsync<T>(string tenantId, string pluginName, CancellationToken cancellationToken = default) where T : class, IPluginType;
    Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default);
    Task<OperationResult> EnableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);
    Task<OperationResult> DisableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);
    Task<Dictionary<string, object>?> GetTenantPluginConfigurationAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);
    Task<PluginMetrics?> GetTenantPluginMetricsAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default);
    Task<bool> HasReachedPluginLimitAsync(string tenantId, CancellationToken cancellationToken = default);
}

public class TenantAwarePluginManager : ITenantAwarePluginManager
{
    private readonly ILogger<TenantAwarePluginManager> _logger;
    private readonly IPluginManager _basePluginManager;
    private readonly NotifyMaster.Core.Interfaces.ITenantService _tenantService;
    private readonly NotifyMaster.Core.Interfaces.ITenantContext _tenantContext;

    public TenantAwarePluginManager(
        ILogger<TenantAwarePluginManager> logger,
        IPluginManager basePluginManager,
        NotifyMaster.Core.Interfaces.ITenantService tenantService,
        NotifyMaster.Core.Interfaces.ITenantContext tenantContext)
    {
        _logger = logger;
        _basePluginManager = basePluginManager;
        _tenantService = tenantService;
        _tenantContext = tenantContext;
    }

    // IPluginManager passthroughs
    public Task<PluginCore.Models.OperationResult> LoadPluginsAsync(string pluginDirectory, CancellationToken cancellationToken = default)
        => _basePluginManager.LoadPluginsAsync(pluginDirectory, cancellationToken);

    public Task<PluginCore.Models.OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default)
        => _basePluginManager.LoadPluginAsync(pluginPath, cancellationToken);

    public Task<PluginCore.Models.OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default)
        => _basePluginManager.LoadPluginByNameAsync(pluginName, pluginDirectory, cancellationToken);

    public IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType
        => _basePluginManager.GetPlugins<T>();

    public IReadOnlyList<PluginManifest> GetPluginManifests()
        => _basePluginManager.GetPluginManifests();

    public PluginManifest? GetPluginManifest(string pluginName)
        => _basePluginManager.GetPluginManifest(pluginName);

    public Task<PluginCore.Models.OperationResult> UnloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)
        => _basePluginManager.UnloadPluginAsync(pluginName, cancellationToken);

    public Task<PluginCore.Models.OperationResult> ReloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)
        => _basePluginManager.ReloadPluginAsync(pluginName, cancellationToken);

    public Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(CancellationToken cancellationToken = default)
        => _basePluginManager.GetPluginStatusesAsync(cancellationToken);

    public T? GetPlugin<T>(string pluginName) where T : class, IPluginType
        => _basePluginManager.GetPlugin<T>(pluginName);

    public Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)
        => _basePluginManager.ValidatePluginAsync(pluginPath, cancellationToken);

    public Task<PluginMetrics?> GetPluginMetricsAsync(string pluginName, CancellationToken cancellationToken = default)
        => _basePluginManager.GetPluginMetricsAsync(pluginName, cancellationToken);

    // Tenant-specific logic
    public async Task<IReadOnlyList<PluginCore.Models.Plugin>> GetTenantPluginsAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);
            var enabled = tenantPlugins.Where(p => p.IsEnabled).Select(p => p.PluginName).ToHashSet();

            var result = new List<PluginCore.Models.Plugin>();

            foreach (var manifest in GetPluginManifests().Where(m => enabled.Contains(m.Name)))
            {
                // Create Plugin model from manifest and status information
                var pluginStatuses = await GetPluginStatusesAsync(cancellationToken);
                var status = pluginStatuses.FirstOrDefault(s => s.Name == manifest.Name);

                var plugin = new PluginCore.Models.Plugin
                {
                    Name = manifest.Name,
                    Version = manifest.Version,
                    Description = manifest.Description,
                    Author = manifest.Author,
                    Type = manifest.Type,
                    IsLoaded = status?.IsLoaded ?? false,
                    IsEnabled = manifest.IsEnabled,
                    IsHealthy = status?.IsHealthy ?? false,
                    LoadedAt = status?.LastChecked.DateTime ?? DateTime.MinValue,
                    Configuration = tenantPlugins.FirstOrDefault(tp => tp.PluginName == manifest.Name)?.Configuration ?? new(),
                    Metadata = manifest.Metadata ?? new(),
                    ErrorMessage = status?.ErrorMessage
                };

                result.Add(plugin);
            }

            return result.AsReadOnly();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tenant plugins for {TenantId}", tenantId);
            return Array.Empty<Plugin>();
        }
    }

    public async Task<IReadOnlyList<T>> GetTenantPluginsAsync<T>(string tenantId, CancellationToken cancellationToken = default) where T : class, IPluginType
    {
        try
        {
            var tenantPlugins = await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken);
            var enabled = tenantPlugins.Where(p => p.IsEnabled).Select(p => p.PluginName).ToHashSet();

            var result = new List<T>();

            foreach (var manifest in GetPluginManifests().Where(m => enabled.Contains(m.Name)))
            {
                var plugin = GetPlugin<T>(manifest.Name);
                if (plugin != null)
                    result.Add(plugin);
            }

            return result.AsReadOnly();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tenant plugins for {TenantId}", tenantId);
            return Array.Empty<T>();
        }
    }

    public async Task<T?> GetTenantPluginAsync<T>(string tenantId, string pluginName, CancellationToken cancellationToken = default) where T : class, IPluginType
    {
        try
        {
            var pluginMeta = (await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken))
                .FirstOrDefault(p => p.PluginName == pluginName && p.IsEnabled);

            if (pluginMeta == null)
                return null;

            var plugin = GetPlugins<T>().FirstOrDefault(p => p.GetType().Name.Contains(pluginName));
            if (plugin == null)
                return null;

            await ApplyTenantConfigurationAsync(plugin, pluginMeta.Configuration);
            return plugin;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting plugin {PluginName} for tenant {TenantId}", pluginName, tenantId);
            return null;
        }
    }

    public async Task<OperationResult> ConfigureTenantPluginAsync(string tenantId, string pluginName, Dictionary<string, object> configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            if (await HasReachedPluginLimitAsync(tenantId, cancellationToken))
                return OperationResult.Failure("Tenant has reached plugin limit");

            await _tenantService.ConfigureTenantPluginAsync(tenantId, pluginName, configuration, cancellationToken);
            return OperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring plugin {PluginName} for tenant {TenantId}", pluginName, tenantId);
            return OperationResult.Failure(ex.Message);
        }
    }

    public async Task<OperationResult> EnableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)
    {
        return await ConfigureTenantPluginAsync(tenantId, pluginName, new() { ["enabled"] = true }, cancellationToken);
    }

    public async Task<OperationResult> DisableTenantPluginAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)
    {
        return await ConfigureTenantPluginAsync(tenantId, pluginName, new() { ["enabled"] = false }, cancellationToken);
    }

    public async Task<Dictionary<string, object>?> GetTenantPluginConfigurationAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)
    {
        try
        {
            var plugin = (await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken))
                .FirstOrDefault(p => p.PluginName == pluginName);

            return plugin?.Configuration;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving config for plugin {PluginName} on tenant {TenantId}", pluginName, tenantId);
            return null;
        }
    }

    public async Task<PluginMetrics?> GetTenantPluginMetricsAsync(string tenantId, string pluginName, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _basePluginManager.GetPluginMetricsAsync(pluginName, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving metrics for plugin {PluginName} on tenant {TenantId}", pluginName, tenantId);
            return null;
        }
    }

    public async Task<bool> HasReachedPluginLimitAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            var tenant = await _tenantService.GetTenantAsync(tenantId, cancellationToken);
            if (tenant == null) return true;

            var count = (await _tenantService.GetTenantPluginsAsync(tenantId, cancellationToken))
                .Count(p => p.IsEnabled);

            return count >= tenant.Limits.MaxPlugins;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking plugin limit for tenant {TenantId}", tenantId);
            return true;
        }
    }

    private async Task ApplyTenantConfigurationAsync(object plugin, Dictionary<string, object> config)
    {
        foreach (var pair in config)
        {
            var property = plugin.GetType().GetProperty(pair.Key);
            if (property?.CanWrite != true) continue;

            try { property.SetValue(plugin, pair.Value); }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to apply property {Property} on {PluginType}", pair.Key, plugin.GetType().Name);
            }
        }
    }
}

public static class TenantAwarePluginManagerExtensions
{
    public static IServiceCollection AddTenantAwarePluginManager(this IServiceCollection services)
    {
        return services.AddScoped<ITenantAwarePluginManager, TenantAwarePluginManager>();
    }
}

