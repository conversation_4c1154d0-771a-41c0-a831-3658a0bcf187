// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Interfaces;

/// <summary>
/// Interface for permission management service
/// </summary>
public interface IPermissionService
{
    /// <summary>
    /// Gets a permission by ID
    /// </summary>
    Task<Permission?> GetPermissionAsync(string permissionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a permission by resource and action
    /// </summary>
    Task<Permission?> GetPermissionAsync(string resource, string action, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all permissions with pagination
    /// </summary>
    Task<IReadOnlyList<Permission>> GetPermissionsAsync(string? resource = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a new permission
    /// </summary>
    Task<OperationResult<Permission>> CreatePermissionAsync(CreatePermissionRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing permission
    /// </summary>
    Task<OperationResult<Permission>> UpdatePermissionAsync(string permissionId, UpdatePermissionRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a permission
    /// </summary>
    Task<OperationResult> DeletePermissionAsync(string permissionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all roles that have a specific permission
    /// </summary>
    Task<IReadOnlyList<Role>> GetPermissionRolesAsync(string permissionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all users that have a specific permission (directly or through roles)
    /// </summary>
    Task<IReadOnlyList<User>> GetPermissionUsersAsync(string permissionId, CancellationToken cancellationToken = default);
}
