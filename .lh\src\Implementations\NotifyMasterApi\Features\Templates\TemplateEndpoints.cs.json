{"sourceFile": "src/Implementations/NotifyMasterApi/Features/Templates/TemplateEndpoints.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751241226408, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751241226408, "name": "Commit-0", "content": "using FastEndpoints;\r\nusing NotifyMasterApi.Infrastructure;\r\nusing PluginCore.Interfaces;\r\nusing PluginCore.Models;\r\nusing System.Text.Json;\r\n\r\nnamespace NotifyMasterApi.Features.Templates;\r\n\r\npublic class RenderTemplateRequest\r\n{\r\n    public string TemplateId { get; set; } = string.Empty;\r\n    public Dictionary<string, object> Variables { get; set; } = new();\r\n}\r\n\r\npublic class RenderTemplateEndpoint : Endpoint<RenderTemplateRequest, object>\r\n{\r\n    private readonly ITemplateRenderingService _templateRenderingService;\r\n    private readonly ILogger<RenderTemplateEndpoint> _logger;\r\n\r\n    public RenderTemplateEndpoint(ITemplateRenderingService templateRenderingService, ILogger<RenderTemplateEndpoint> logger)\r\n    {\r\n        _templateRenderingService = templateRenderingService;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Post(\"/api/templates/render\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Render template\";\r\n            s.Description = \"Render message content from template without sending\";\r\n            s.Responses[200] = \"Template rendered successfully\";\r\n            s.Responses[400] = \"Invalid template or variables\";\r\n            s.Responses[404] = \"Template not found\";\r\n        });\r\n        Tags(\"Templates\");\r\n    }\r\n\r\n    public override async Task HandleAsync(RenderTemplateRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var result = await _templateRenderingService.RenderTemplateWithValidationAsync(req.TemplateId, req.Variables);\r\n            \r\n            if (!result.Success)\r\n            {\r\n                await SendAsync(new\r\n                {\r\n                    Success = false,\r\n                    Errors = result.Errors,\r\n                    Warnings = result.Warnings\r\n                }, 400, ct);\r\n                return;\r\n            }\r\n\r\n            await SendOkAsync(new\r\n            {\r\n                Success = true,\r\n                RenderedContent = result.RenderedContent,\r\n                UsedVariables = result.UsedVariables,\r\n                MissingVariables = result.MissingVariables,\r\n                Warnings = result.Warnings,\r\n                Timestamp = DateTime.UtcNow\r\n            }, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error rendering template {TemplateId}\", req.TemplateId);\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class ValidateTemplateRequest\r\n{\r\n    public string TemplateContent { get; set; } = string.Empty;\r\n}\r\n\r\npublic class ValidateTemplateEndpoint : Endpoint<ValidateTemplateRequest, object>\r\n{\r\n    private readonly ITemplateRenderingService _templateRenderingService;\r\n\r\n    public ValidateTemplateEndpoint(ITemplateRenderingService templateRenderingService)\r\n    {\r\n        _templateRenderingService = templateRenderingService;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Post(\"/api/templates/validate\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Validate template\";\r\n            s.Description = \"Validate template syntax and extract variables\";\r\n            s.Responses[200] = \"Template validation completed\";\r\n        });\r\n        Tags(\"Templates\");\r\n    }\r\n\r\n    public override async Task HandleAsync(ValidateTemplateRequest req, CancellationToken ct)\r\n    {\r\n        var isValid = await _templateRenderingService.ValidateTemplateAsync(req.TemplateContent);\r\n        var variables = await _templateRenderingService.ExtractVariablesFromTemplateAsync(req.TemplateContent);\r\n\r\n        await SendOkAsync(new\r\n        {\r\n            IsValid = isValid,\r\n            Variables = variables,\r\n            VariableCount = variables.Count,\r\n            Timestamp = DateTime.UtcNow\r\n        }, ct);\r\n    }\r\n}\r\n\r\npublic class ScheduleMessageRequest\r\n{\r\n    public string TenantId { get; set; } = string.Empty;\r\n    public string Channel { get; set; } = string.Empty;\r\n    public object Payload { get; set; } = new();\r\n    public DateTime? ScheduledFor { get; set; }\r\n    public string? CronExpression { get; set; }\r\n    public Dictionary<string, object> Metadata { get; set; } = new();\r\n}\r\n\r\npublic class ScheduleMessageEndpoint : Endpoint<ScheduleMessageRequest, object>\r\n{\r\n    private readonly ISchedulingService _schedulingService;\r\n    private readonly ILogger<ScheduleMessageEndpoint> _logger;\r\n\r\n    public ScheduleMessageEndpoint(ISchedulingService schedulingService, ILogger<ScheduleMessageEndpoint> logger)\r\n    {\r\n        _schedulingService = schedulingService;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Post(\"/api/messages/schedule\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Schedule message\";\r\n            s.Description = \"Schedule a message for future delivery with optional cron-style recurring schedule\";\r\n            s.Responses[200] = \"Message scheduled successfully\";\r\n            s.Responses[400] = \"Invalid request\";\r\n        });\r\n        Tags(\"Scheduling\");\r\n    }\r\n\r\n    public override async Task HandleAsync(ScheduleMessageRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            if (!req.ScheduledFor.HasValue && string.IsNullOrEmpty(req.CronExpression))\r\n            {\r\n                await SendAsync(new { Error = \"Either ScheduledFor or CronExpression must be provided\" }, 400, ct);\r\n                return;\r\n            }\r\n\r\n            var scheduledMessage = new ScheduledMessage\r\n            {\r\n                TenantId = req.TenantId,\r\n                Channel = req.Channel,\r\n                Payload = JsonSerializer.Serialize(req.Payload),\r\n                ScheduledFor = req.ScheduledFor ?? DateTime.UtcNow.AddMinutes(1),\r\n                CronExpression = req.CronExpression,\r\n                Metadata = req.Metadata\r\n            };\r\n\r\n            var messageId = await _schedulingService.ScheduleMessageAsync(scheduledMessage);\r\n\r\n            await SendOkAsync(new\r\n            {\r\n                MessageId = messageId,\r\n                Status = \"Scheduled\",\r\n                ScheduledFor = scheduledMessage.ScheduledFor,\r\n                CronExpression = scheduledMessage.CronExpression,\r\n                Timestamp = DateTime.UtcNow\r\n            }, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error scheduling message\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetScheduledMessagesRequest\r\n{\r\n    public string? TenantId { get; set; }\r\n}\r\n\r\npublic class GetScheduledMessagesEndpoint : Endpoint<GetScheduledMessagesRequest, object>\r\n{\r\n    private readonly ISchedulingService _schedulingService;\r\n\r\n    public GetScheduledMessagesEndpoint(ISchedulingService schedulingService)\r\n    {\r\n        _schedulingService = schedulingService;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/messages/scheduled\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get scheduled messages\";\r\n            s.Description = \"Get list of scheduled messages with optional tenant filtering\";\r\n            s.Responses[200] = \"Scheduled messages retrieved successfully\";\r\n        });\r\n        Tags(\"Scheduling\");\r\n    }\r\n\r\n    public override async Task HandleAsync(GetScheduledMessagesRequest req, CancellationToken ct)\r\n    {\r\n        var messages = await _schedulingService.GetScheduledMessagesAsync(req.TenantId);\r\n        await SendOkAsync(new\r\n        {\r\n            Messages = messages,\r\n            Count = messages.Count,\r\n            Timestamp = DateTime.UtcNow\r\n        }, ct);\r\n    }\r\n}\r\n\r\npublic class CancelScheduledMessageRequest\r\n{\r\n    public string MessageId { get; set; } = string.Empty;\r\n}\r\n\r\npublic class CancelScheduledMessageEndpoint : Endpoint<CancelScheduledMessageRequest, object>\r\n{\r\n    private readonly ISchedulingService _schedulingService;\r\n\r\n    public CancelScheduledMessageEndpoint(ISchedulingService schedulingService)\r\n    {\r\n        _schedulingService = schedulingService;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Delete(\"/api/messages/scheduled/{messageId}\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Cancel scheduled message\";\r\n            s.Description = \"Cancel a scheduled message before it's sent\";\r\n            s.Responses[200] = \"Message cancelled successfully\";\r\n            s.Responses[404] = \"Message not found\";\r\n            s.Responses[400] = \"Message cannot be cancelled\";\r\n        });\r\n        Tags(\"Scheduling\");\r\n    }\r\n\r\n    public override async Task HandleAsync(CancelScheduledMessageRequest req, CancellationToken ct)\r\n    {\r\n        var success = await _schedulingService.CancelScheduledMessageAsync(req.MessageId);\r\n        \r\n        if (!success)\r\n        {\r\n            await SendAsync(new { Error = \"Message not found or cannot be cancelled\" }, 400, ct);\r\n            return;\r\n        }\r\n\r\n        await SendOkAsync(new\r\n        {\r\n            Success = true,\r\n            Message = \"Scheduled message cancelled\",\r\n            MessageId = req.MessageId,\r\n            Timestamp = DateTime.UtcNow\r\n        }, ct);\r\n    }\r\n}\r\n\r\npublic class CreateValidationRuleRequest\r\n{\r\n    public string TenantId { get; set; } = string.Empty;\r\n    public string Name { get; set; } = string.Empty;\r\n    public string Channel { get; set; } = string.Empty;\r\n    public string RuleType { get; set; } = string.Empty;\r\n    public string RuleDefinition { get; set; } = string.Empty;\r\n    public int Priority { get; set; } = 0;\r\n}\r\n\r\npublic class CreateValidationRuleEndpoint : Endpoint<CreateValidationRuleRequest, object>\r\n{\r\n    private readonly IValidationService _validationService;\r\n    private readonly ILogger<CreateValidationRuleEndpoint> _logger;\r\n\r\n    public CreateValidationRuleEndpoint(IValidationService validationService, ILogger<CreateValidationRuleEndpoint> logger)\r\n    {\r\n        _validationService = validationService;\r\n        _logger = logger;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Post(\"/api/validation/rules\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Create validation rule\";\r\n            s.Description = \"Create a custom validation rule for message validation\";\r\n            s.Responses[200] = \"Validation rule created successfully\";\r\n            s.Responses[400] = \"Invalid request\";\r\n        });\r\n        Tags(\"Validation\");\r\n    }\r\n\r\n    public override async Task HandleAsync(CreateValidationRuleRequest req, CancellationToken ct)\r\n    {\r\n        try\r\n        {\r\n            var rule = new ValidationRule\r\n            {\r\n                TenantId = req.TenantId,\r\n                Name = req.Name,\r\n                Channel = req.Channel,\r\n                RuleType = req.RuleType,\r\n                RuleDefinition = req.RuleDefinition,\r\n                Priority = req.Priority,\r\n                IsActive = true\r\n            };\r\n\r\n            var createdRule = await _validationService.CreateValidationRuleAsync(rule);\r\n            \r\n            if (createdRule == null)\r\n            {\r\n                await SendAsync(new { Error = \"Failed to create validation rule\" }, 400, ct);\r\n                return;\r\n            }\r\n\r\n            await SendOkAsync(new\r\n            {\r\n                Rule = createdRule,\r\n                Message = \"Validation rule created successfully\",\r\n                Timestamp = DateTime.UtcNow\r\n            }, ct);\r\n        }\r\n        catch (Exception ex)\r\n        {\r\n            _logger.LogError(ex, \"Error creating validation rule\");\r\n            await SendErrorsAsync(500, ct);\r\n        }\r\n    }\r\n}\r\n\r\npublic class GetValidationRulesRequest\r\n{\r\n    public string TenantId { get; set; } = string.Empty;\r\n    public string? Channel { get; set; }\r\n}\r\n\r\npublic class GetValidationRulesEndpoint : Endpoint<GetValidationRulesRequest, object>\r\n{\r\n    private readonly IValidationService _validationService;\r\n\r\n    public GetValidationRulesEndpoint(IValidationService validationService)\r\n    {\r\n        _validationService = validationService;\r\n    }\r\n\r\n    public override void Configure()\r\n    {\r\n        Get(\"/api/validation/rules\");\r\n        AllowAnonymous();\r\n        Summary(s =>\r\n        {\r\n            s.Summary = \"Get validation rules\";\r\n            s.Description = \"Get validation rules for a tenant with optional channel filtering\";\r\n            s.Responses[200] = \"Validation rules retrieved successfully\";\r\n        });\r\n        Tags(\"Validation\");\r\n    }\r\n\r\n    public override async Task HandleAsync(GetValidationRulesRequest req, CancellationToken ct)\r\n    {\r\n        var rules = await _validationService.GetValidationRulesAsync(req.TenantId, req.Channel);\r\n        await SendOkAsync(new\r\n        {\r\n            Rules = rules,\r\n            Count = rules.Count,\r\n            Timestamp = DateTime.UtcNow\r\n        }, ct);\r\n    }\r\n}\r\n"}]}