{"sourceFile": "src/Implementations/NotifyMasterApi/Features/Plugins/PluginManagerEndpoints.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751241318300, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751241318300, "name": "Commit-0", "content": "using FastEndpoints;\nusing PluginCore.Interfaces;\nusing PluginCore.Models;\nusing System.ComponentModel.DataAnnotations;\n\nnamespace NotifyMasterApi.Features.Plugins;\n\n// ============================================================================\n// Load Plugins from Directory\n// ============================================================================\n\npublic class LoadPluginsRequest\n{\n    [Required]\n    public string PluginDirectory { get; set; } = string.Empty;\n}\n\npublic class LoadPluginsEndpoint : Endpoint<LoadPluginsRequest, OperationResult>\n{\n    private readonly IPluginManager _pluginManager;\n\n    public LoadPluginsEndpoint(IPluginManager pluginManager)\n    {\n        _pluginManager = pluginManager;\n    }\n\n    public override void Configure()\n    {\n        Post(\"/api/plugins/load-directory\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Load plugins from directory\";\n            s.Description = \"Load all plugins from the specified directory\";\n            s.Response<OperationResult>(200, \"Plugins loaded successfully\");\n            s.Response(400, \"Invalid directory or load failed\");\n        });\n        Tags(\"🔌 Plugin Management\");\n    }\n\n    public override async Task HandleAsync(LoadPluginsRequest req, CancellationToken ct)\n    {\n        var result = await _pluginManager.LoadPluginsAsync(req.PluginDirectory, ct);\n        \n        if (result.IsSuccess)\n        {\n            await SendOkAsync(result, ct);\n        }\n        else\n        {\n            await SendAsync(result, 400, ct);\n        }\n    }\n}\n\n// ============================================================================\n// Load Single Plugin\n// ============================================================================\n\npublic class LoadPluginRequest\n{\n    [Required]\n    public string PluginPath { get; set; } = string.Empty;\n}\n\npublic class LoadPluginEndpoint : Endpoint<LoadPluginRequest, OperationResult>\n{\n    private readonly IPluginManager _pluginManager;\n\n    public LoadPluginEndpoint(IPluginManager pluginManager)\n    {\n        _pluginManager = pluginManager;\n    }\n\n    public override void Configure()\n    {\n        Post(\"/api/plugins/load\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Load single plugin\";\n            s.Description = \"Load a specific plugin by file path\";\n            s.Response<OperationResult>(200, \"Plugin loaded successfully\");\n            s.Response(400, \"Invalid path or load failed\");\n        });\n        Tags(\"🔌 Plugin Management\");\n    }\n\n    public override async Task HandleAsync(LoadPluginRequest req, CancellationToken ct)\n    {\n        var result = await _pluginManager.LoadPluginAsync(req.PluginPath, ct);\n        \n        if (result.IsSuccess)\n        {\n            await SendOkAsync(result, ct);\n        }\n        else\n        {\n            await SendAsync(result, 400, ct);\n        }\n    }\n}\n\n// ============================================================================\n// Load Plugin by Name\n// ============================================================================\n\npublic class LoadPluginByNameRequest\n{\n    [Required]\n    public string PluginName { get; set; } = string.Empty;\n    \n    [Required]\n    public string PluginDirectory { get; set; } = string.Empty;\n}\n\npublic class LoadPluginByNameEndpoint : Endpoint<LoadPluginByNameRequest, OperationResult>\n{\n    private readonly IPluginManager _pluginManager;\n\n    public LoadPluginByNameEndpoint(IPluginManager pluginManager)\n    {\n        _pluginManager = pluginManager;\n    }\n\n    public override void Configure()\n    {\n        Post(\"/api/plugins/load-by-name\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Load plugin by name\";\n            s.Description = \"Load a plugin by name from the specified directory\";\n            s.Response<OperationResult>(200, \"Plugin loaded successfully\");\n            s.Response(400, \"Plugin not found or load failed\");\n        });\n        Tags(\"🔌 Plugin Management\");\n    }\n\n    public override async Task HandleAsync(LoadPluginByNameRequest req, CancellationToken ct)\n    {\n        var result = await _pluginManager.LoadPluginByNameAsync(req.PluginName, req.PluginDirectory, ct);\n        \n        if (result.IsSuccess)\n        {\n            await SendOkAsync(result, ct);\n        }\n        else\n        {\n            await SendAsync(result, 400, ct);\n        }\n    }\n}\n\n// ============================================================================\n// Get Plugin Manifests\n// ============================================================================\n\npublic class GetPluginManifestsEndpoint : EndpointWithoutRequest<List<PluginManifest>>\n{\n    private readonly IPluginManager _pluginManager;\n\n    public GetPluginManifestsEndpoint(IPluginManager pluginManager)\n    {\n        _pluginManager = pluginManager;\n    }\n\n    public override void Configure()\n    {\n        Get(\"/api/plugins/manifests\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Get plugin manifests\";\n            s.Description = \"Get all loaded plugin manifests with metadata\";\n            s.Response<List<PluginManifest>>(200, \"Plugin manifests retrieved successfully\");\n        });\n        Tags(\"🔌 Plugin Management\");\n    }\n\n    public override async Task HandleAsync(CancellationToken ct)\n    {\n        var manifests = _pluginManager.GetPluginManifests();\n        await SendOkAsync(manifests.ToList(), ct);\n    }\n}\n\n// ============================================================================\n// Get Plugin Manifest by Name\n// ============================================================================\n\npublic class GetPluginManifestRequest\n{\n    [Required]\n    public string PluginName { get; set; } = string.Empty;\n}\n\npublic class GetPluginManifestEndpoint : Endpoint<GetPluginManifestRequest, PluginManifest?>\n{\n    private readonly IPluginManager _pluginManager;\n\n    public GetPluginManifestEndpoint(IPluginManager pluginManager)\n    {\n        _pluginManager = pluginManager;\n    }\n\n    public override void Configure()\n    {\n        Get(\"/api/plugins/manifests/{pluginName}\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Get plugin manifest by name\";\n            s.Description = \"Get the manifest for a specific plugin\";\n            s.Response<PluginManifest>(200, \"Plugin manifest retrieved successfully\");\n            s.Response(404, \"Plugin not found\");\n        });\n        Tags(\"🔌 Plugin Management\");\n    }\n\n    public override async Task HandleAsync(GetPluginManifestRequest req, CancellationToken ct)\n    {\n        var manifest = _pluginManager.GetPluginManifest(req.PluginName);\n        \n        if (manifest == null)\n        {\n            await SendNotFoundAsync(ct);\n            return;\n        }\n        \n        await SendOkAsync(manifest, ct);\n    }\n}\n\n// ============================================================================\n// Unload Plugin\n// ============================================================================\n\npublic class UnloadPluginRequest\n{\n    [Required]\n    public string PluginName { get; set; } = string.Empty;\n}\n\npublic class UnloadPluginEndpoint : Endpoint<UnloadPluginRequest, OperationResult>\n{\n    private readonly IPluginManager _pluginManager;\n\n    public UnloadPluginEndpoint(IPluginManager pluginManager)\n    {\n        _pluginManager = pluginManager;\n    }\n\n    public override void Configure()\n    {\n        Delete(\"/api/plugins/{pluginName}\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Unload plugin\";\n            s.Description = \"Unload a specific plugin from memory\";\n            s.Response<OperationResult>(200, \"Plugin unloaded successfully\");\n            s.Response(400, \"Plugin not found or unload failed\");\n        });\n        Tags(\"🔌 Plugin Management\");\n    }\n\n    public override async Task HandleAsync(UnloadPluginRequest req, CancellationToken ct)\n    {\n        var result = await _pluginManager.UnloadPluginAsync(req.PluginName, ct);\n        \n        if (result.IsSuccess)\n        {\n            await SendOkAsync(result, ct);\n        }\n        else\n        {\n            await SendAsync(result, 400, ct);\n        }\n    }\n}\n\n// ============================================================================\n// Reload Plugin\n// ============================================================================\n\npublic class ReloadPluginRequest\n{\n    [Required]\n    public string PluginName { get; set; } = string.Empty;\n}\n\npublic class ReloadPluginEndpoint : Endpoint<ReloadPluginRequest, OperationResult>\n{\n    private readonly IPluginManager _pluginManager;\n\n    public ReloadPluginEndpoint(IPluginManager pluginManager)\n    {\n        _pluginManager = pluginManager;\n    }\n\n    public override void Configure()\n    {\n        Put(\"/api/plugins/{pluginName}/reload\");\n        AllowAnonymous();\n        Summary(s =>\n        {\n            s.Summary = \"Reload plugin\";\n            s.Description = \"Reload a specific plugin (unload and load again)\";\n            s.Response<OperationResult>(200, \"Plugin reloaded successfully\");\n            s.Response(400, \"Plugin not found or reload failed\");\n        });\n        Tags(\"🔌 Plugin Management\");\n    }\n\n    public override async Task HandleAsync(ReloadPluginRequest req, CancellationToken ct)\n    {\n        var result = await _pluginManager.ReloadPluginAsync(req.PluginName, ct);\n\n        if (result.IsSuccess)\n        {\n            await SendOkAsync(result, ct);\n        }\n        else\n        {\n            await SendAsync(result, 400, ct);\n        }\n    }\n}\n"}]}