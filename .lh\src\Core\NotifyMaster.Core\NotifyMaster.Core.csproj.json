{"sourceFile": "src/Core/NotifyMaster.Core/NotifyMaster.Core.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 14, "patches": [{"date": 1751224627211, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751228525432, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,9 +9,9 @@\n   <ItemGroup>\n     <PackageReference Include=\"Microsoft.Extensions.DependencyInjection.Abstractions\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Configuration.Abstractions\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Logging.Abstractions\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Microsoft.AspNetCore.Http.Abstractions\" Version=\"2.2.0\" />\n+    <PackageReference Include=\"Microsoft.AspNetCore.Http.Abstractions\" Version=\"2.3.0\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"System.IdentityModel.Tokens.Jwt\" Version=\"8.12.1\" />\n   </ItemGroup>\n \n"}, {"date": 1751229704422, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,22 +1,16 @@\n <Project Sdk=\"Microsoft.NET.Sdk\">\n-\n   <PropertyGroup>\n     <TargetFramework>net9.0</TargetFramework>\n     <ImplicitUsings>enable</ImplicitUsings>\n     <Nullable>enable</Nullable>\n   </PropertyGroup>\n-\n   <ItemGroup>\n+    <PackageReference Include=\"BCrypt.Net-Next\" Version=\"4.0.3\" />\n     <PackageReference Include=\"Microsoft.Extensions.DependencyInjection.Abstractions\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Configuration.Abstractions\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.Extensions.Logging.Abstractions\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Http.Abstractions\" Version=\"2.3.0\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"System.IdentityModel.Tokens.Jwt\" Version=\"8.12.1\" />\n   </ItemGroup>\n-\n-  <ItemGroup>\n-    <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n-  </ItemGroup>\n-\n-</Project>\n+</Project>\n\\ No newline at end of file\n"}, {"date": 1751229963986, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,5 +12,9 @@\n     <PackageReference Include=\"Microsoft.AspNetCore.Http.Abstractions\" Version=\"2.3.0\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"System.IdentityModel.Tokens.Jwt\" Version=\"8.12.1\" />\n   </ItemGroup>\n+\n+  <ItemGroup>\n+    <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n+  </ItemGroup>\n </Project>\n\\ No newline at end of file\n"}, {"date": 1751231928068, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,8 +11,9 @@\n     <PackageReference Include=\"Microsoft.Extensions.Logging.Abstractions\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Http.Abstractions\" Version=\"2.3.0\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"System.IdentityModel.Tokens.Jwt\" Version=\"8.12.1\" />\n+    <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.17\" />\n   </ItemGroup>\n \n   <ItemGroup>\n     <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n"}, {"date": 1751237025255, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,6 +16,7 @@\n   </ItemGroup>\n \n   <ItemGroup>\n     <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n+    <ProjectReference Include=\"..\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\n   </ItemGroup>\n </Project>\n\\ No newline at end of file\n"}, {"date": 1751238173455, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,7 +16,6 @@\n   </ItemGroup>\n \n   <ItemGroup>\n     <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n-    <ProjectReference Include=\"..\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\n   </ItemGroup>\n </Project>\n\\ No newline at end of file\n"}, {"date": 1751238236132, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,8 +12,9 @@\n     <PackageReference Include=\"Microsoft.AspNetCore.Http.Abstractions\" Version=\"2.3.0\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"System.IdentityModel.Tokens.Jwt\" Version=\"8.12.1\" />\n     <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.17\" />\n+    <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n   </ItemGroup>\n \n   <ItemGroup>\n     <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n"}, {"date": 1751238490177, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,6 +17,7 @@\n   </ItemGroup>\n \n   <ItemGroup>\n     <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n+    <ProjectReference Include=\"..\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\n   </ItemGroup>\n </Project>\n\\ No newline at end of file\n"}, {"date": 1751238551236, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,7 +17,6 @@\n   </ItemGroup>\n \n   <ItemGroup>\n     <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n-    <ProjectReference Include=\"..\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\n   </ItemGroup>\n </Project>\n\\ No newline at end of file\n"}, {"date": 1751240951619, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,8 +13,13 @@\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"System.IdentityModel.Tokens.Jwt\" Version=\"8.12.1\" />\n     <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.17\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.EntityFrameworkCore.Sqlite\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"9.0.6\" />\n   </ItemGroup>\n \n   <ItemGroup>\n     <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n"}, {"date": 1751240965808, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -22,6 +22,7 @@\n   </ItemGroup>\n \n   <ItemGroup>\n     <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n+    <ProjectReference Include=\"..\\NotifyMaster.Database\\NotifyMaster.Database.csproj\" />\n   </ItemGroup>\n </Project>\n\\ No newline at end of file\n"}, {"date": 1751242489816, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,10 +16,10 @@\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.Sqlite\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"9.0.6\" />\n+    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"8.0.11\" />\n+    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"8.0.2\" />\n   </ItemGroup>\n \n   <ItemGroup>\n     <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n"}, {"date": 1751242505110, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,9 +11,9 @@\n     <PackageReference Include=\"Microsoft.Extensions.Logging.Abstractions\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Http.Abstractions\" Version=\"2.3.0\" />\n     <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"System.IdentityModel.Tokens.Jwt\" Version=\"8.12.1\" />\n-    <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.17\" />\n+    <PackageReference Include=\"Hangfire.Core\" Version=\"1.8.20\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.Sqlite\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\n"}, {"date": 1751245255184, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,10 +16,10 @@\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.SqlServer\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.Sqlite\" Version=\"9.0.6\" />\n     <PackageReference Include=\"Microsoft.EntityFrameworkCore.InMemory\" Version=\"9.0.6\" />\n-    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"8.0.11\" />\n-    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"8.0.2\" />\n+    <PackageReference Include=\"Npgsql.EntityFrameworkCore.PostgreSQL\" Version=\"9.0.4\" />\n+    <PackageReference Include=\"Pomelo.EntityFrameworkCore.MySql\" Version=\"9.0.0\" />\n   </ItemGroup>\n \n   <ItemGroup>\n     <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n"}], "date": 1751224627211, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk\">\n\n  <PropertyGroup>\n    <TargetFramework>net9.0</TargetFramework>\n    <ImplicitUsings>enable</ImplicitUsings>\n    <Nullable>enable</Nullable>\n  </PropertyGroup>\n\n  <ItemGroup>\n    <PackageReference Include=\"Microsoft.Extensions.DependencyInjection.Abstractions\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Microsoft.Extensions.Configuration.Abstractions\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Microsoft.Extensions.Logging.Abstractions\" Version=\"9.0.6\" />\n    <PackageReference Include=\"Microsoft.AspNetCore.Http.Abstractions\" Version=\"2.2.0\" />\n    <PackageReference Include=\"Microsoft.AspNetCore.Authentication.JwtBearer\" Version=\"9.0.6\" />\n    <PackageReference Include=\"System.IdentityModel.Tokens.Jwt\" Version=\"8.12.1\" />\n  </ItemGroup>\n\n  <ItemGroup>\n    <ProjectReference Include=\"..\\PluginCore\\PluginCore.csproj\" />\n  </ItemGroup>\n\n</Project>\n"}]}