// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Entities;

/// <summary>
/// Database entity for Permission
/// </summary>
[Table("Permissions")]
public class PermissionEntity
{
    [Key]
    [MaxLength(36)]
    public string Id { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string Resource { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string Action { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public bool IsSystemPermission { get; set; } = false;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    [MaxLength(36)]
    public string? CreatedBy { get; set; }
    
    // Navigation properties
    public virtual ICollection<UserPermissionEntity> Users { get; set; } = new List<UserPermissionEntity>();
    public virtual ICollection<RolePermissionEntity> Roles { get; set; } = new List<RolePermissionEntity>();

}
