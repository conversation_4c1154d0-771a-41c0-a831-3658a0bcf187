{"sourceFile": "src/Core/PluginCore/Services/PluginManager.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 8, "patches": [{"date": 1751212338917, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751214244519, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -232,60 +232,51 @@\n \n     public async Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(CancellationToken cancellationToken = default)\n     {\n         var statuses = new List<PluginStatus>();\n+        Dictionary<string, LoadedPlugin> pluginsCopy;\n \n         lock (_lock)\n         {\n-            foreach (var (name, loadedPlugin) in _loadedPlugins)\n+            pluginsCopy = new Dictionary<string, LoadedPlugin>(_loadedPlugins);\n+        }\n+\n+        foreach (var (name, loadedPlugin) in pluginsCopy)\n+        {\n+            try\n             {\n-                try\n-                {\n-                    var isHealthy = true;\n-                    var status = \"Loaded\";\n-                    string? errorMessage = null;\n+                var isHealthy = true;\n+                var status = \"Loaded\";\n+                string? errorMessage = null;\n \n-                    // Check if plugin is healthy (if it implements health check)\n-                    if (loadedPlugin.Instance is IGatewayMessagePluginType gateway)\n-                    {\n-                        try\n-                        {\n-                            isHealthy = await gateway.IsAvailableAsync(cancellationToken);\n-                            status = isHealthy ? \"Healthy\" : \"Unhealthy\";\n-                        }\n-                        catch (Exception ex)\n-                        {\n-                            isHealthy = false;\n-                            status = \"Error\";\n-                            errorMessage = ex.Message;\n-                        }\n-                    }\n+                // For now, just assume plugins are healthy if they're loaded\n+                // In the future, we can add a health check interface\n+                status = \"Healthy\";\n \n-                    statuses.Add(new PluginStatus(\n-                        Name: name,\n-                        Version: loadedPlugin.Manifest.Version,\n-                        Type: loadedPlugin.Manifest.Type,\n-                        IsLoaded: true,\n-                        IsHealthy: isHealthy,\n-                        Status: status,\n-                        LastChecked: DateTimeOffset.UtcNow,\n-                        ErrorMessage: errorMessage\n-                    ));\n-                }\n-                catch (Exception ex)\n-                {\n-                    statuses.Add(new PluginStatus(\n-                        Name: name,\n-                        Version: loadedPlugin.Manifest.Version,\n-                        Type: loadedPlugin.Manifest.Type,\n-                        IsLoaded: true,\n-                        IsHealthy: false,\n-                        Status: \"Error\",\n-                        LastChecked: DateTimeOffset.UtcNow,\n-                        ErrorMessage: ex.Message\n-                    ));\n-                }\n+                statuses.Add(new PluginStatus(\n+                    Name: name,\n+                    Version: loadedPlugin.Manifest.Version,\n+                    Type: loadedPlugin.Manifest.Type,\n+                    IsLoaded: true,\n+                    IsHealthy: isHealthy,\n+                    Status: status,\n+                    LastChecked: DateTimeOffset.UtcNow,\n+                    ErrorMessage: errorMessage\n+                ));\n             }\n+            catch (Exception ex)\n+            {\n+                statuses.Add(new PluginStatus(\n+                    Name: name,\n+                    Version: loadedPlugin.Manifest.Version,\n+                    Type: loadedPlugin.Manifest.Type,\n+                    IsLoaded: true,\n+                    IsHealthy: false,\n+                    Status: \"Error\",\n+                    LastChecked: DateTimeOffset.UtcNow,\n+                    ErrorMessage: ex.Message\n+                ));\n+            }\n         }\n \n         return statuses.AsReadOnly();\n     }\n"}, {"date": 1751225260526, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,5 @@\n-using Microsoft.Extensions.Logging;\n-using PluginCore.Base;\n-using PluginCore.Interfaces;\n-using PluginCore.Models;\n-using PluginCore.Utilities;\n-using System.Reflection;\n-using System.Text.Json;\n+// Using statements are handled by GlobalUsings.cs\n \n namespace PluginCore.Services;\n \n /// <summary>\n"}, {"date": 1751229008080, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,6 @@\n // Using statements are handled by GlobalUsings.cs\n+using ValidationResult = PluginCore.Models.ValidationResult;\n \n namespace PluginCore.Services;\n \n /// <summary>\n"}, {"date": 1751229029910, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -462,8 +462,53 @@\n             return null;\n         }\n     }\n \n+    public async Task<PluginMetrics?> GetPluginMetricsAsync(string pluginName, CancellationToken cancellationToken = default)\n+    {\n+        try\n+        {\n+            lock (_lock)\n+            {\n+                if (!_loadedPlugins.TryGetValue(pluginName, out var loadedPlugin))\n+                    return null;\n+\n+                // Try to get metrics from the plugin if it implements IGatewayMetricsPluginType\n+                if (loadedPlugin.Instance is IGatewayMetricsPluginType metricsPlugin)\n+                {\n+                    var metrics = metricsPlugin.GetMetricsAsync(cancellationToken).Result;\n+                    return new PluginMetrics\n+                    {\n+                        PluginName = pluginName,\n+                        MessagesSent = metrics.TotalMessages,\n+                        MessagesSucceeded = metrics.SuccessfulMessages,\n+                        MessagesFailed = metrics.FailedMessages,\n+                        AverageResponseTime = metrics.AverageResponseTime,\n+                        LastActivity = metrics.LastActivity,\n+                        CustomMetrics = metrics.CustomMetrics\n+                    };\n+                }\n+\n+                // Return basic metrics if plugin doesn't implement metrics interface\n+                return new PluginMetrics\n+                {\n+                    PluginName = pluginName,\n+                    MessagesSent = 0,\n+                    MessagesSucceeded = 0,\n+                    MessagesFailed = 0,\n+                    AverageResponseTime = 0,\n+                    LastActivity = DateTime.UtcNow,\n+                    CustomMetrics = new Dictionary<string, object>()\n+                };\n+            }\n+        }\n+        catch (Exception ex)\n+        {\n+            _logger.LogError(ex, \"Failed to get metrics for plugin {PluginName}\", pluginName);\n+            return null;\n+        }\n+    }\n+\n     private string GetPluginTypeString(Type type)\n     {\n         if (typeof(ISmsPlugin).IsAssignableFrom(type)) return \"SMS\";\n         if (typeof(IEmailPlugin).IsAssignableFrom(type)) return \"Email\";\n"}, {"date": 1751229102896, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -474,18 +474,23 @@\n \n                 // Try to get metrics from the plugin if it implements IGatewayMetricsPluginType\n                 if (loadedPlugin.Instance is IGatewayMetricsPluginType metricsPlugin)\n                 {\n-                    var metrics = metricsPlugin.GetMetricsAsync(cancellationToken).Result;\n+                    var statusReport = await metricsPlugin.GetStatusReportAsync(cancellationToken);\n                     return new PluginMetrics\n                     {\n                         PluginName = pluginName,\n-                        MessagesSent = metrics.TotalMessages,\n-                        MessagesSucceeded = metrics.SuccessfulMessages,\n-                        MessagesFailed = metrics.FailedMessages,\n-                        AverageResponseTime = metrics.AverageResponseTime,\n-                        LastActivity = metrics.LastActivity,\n-                        CustomMetrics = metrics.CustomMetrics\n+                        MessagesSent = 0, // Would need to get from usage metrics\n+                        MessagesSucceeded = 0,\n+                        MessagesFailed = 0,\n+                        AverageResponseTime = statusReport.AverageLatency,\n+                        LastActivity = DateTime.UtcNow,\n+                        CustomMetrics = new Dictionary<string, object>\n+                        {\n+                            [\"IsHealthy\"] = statusReport.IsHealthy,\n+                            [\"Status\"] = statusReport.Status,\n+                            [\"LastChecked\"] = statusReport.LastChecked\n+                        }\n                     };\n                 }\n \n                 // Return basic metrics if plugin doesn't implement metrics interface\n"}, {"date": 1751229175576, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -466,46 +466,47 @@\n     public async Task<PluginMetrics?> GetPluginMetricsAsync(string pluginName, CancellationToken cancellationToken = default)\n     {\n         try\n         {\n+            LoadedPlugin? loadedPlugin;\n             lock (_lock)\n             {\n-                if (!_loadedPlugins.TryGetValue(pluginName, out var loadedPlugin))\n+                if (!_loadedPlugins.TryGetValue(pluginName, out loadedPlugin))\n                     return null;\n+            }\n \n-                // Try to get metrics from the plugin if it implements IGatewayMetricsPluginType\n-                if (loadedPlugin.Instance is IGatewayMetricsPluginType metricsPlugin)\n-                {\n-                    var statusReport = await metricsPlugin.GetStatusReportAsync(cancellationToken);\n-                    return new PluginMetrics\n-                    {\n-                        PluginName = pluginName,\n-                        MessagesSent = 0, // Would need to get from usage metrics\n-                        MessagesSucceeded = 0,\n-                        MessagesFailed = 0,\n-                        AverageResponseTime = statusReport.AverageLatency,\n-                        LastActivity = DateTime.UtcNow,\n-                        CustomMetrics = new Dictionary<string, object>\n-                        {\n-                            [\"IsHealthy\"] = statusReport.IsHealthy,\n-                            [\"Status\"] = statusReport.Status,\n-                            [\"LastChecked\"] = statusReport.LastChecked\n-                        }\n-                    };\n-                }\n-\n-                // Return basic metrics if plugin doesn't implement metrics interface\n+            // Try to get metrics from the plugin if it implements IGatewayMetricsPluginType\n+            if (loadedPlugin.Instance is IGatewayMetricsPluginType metricsPlugin)\n+            {\n+                var statusReport = await metricsPlugin.GetStatusReportAsync(cancellationToken);\n                 return new PluginMetrics\n                 {\n                     PluginName = pluginName,\n-                    MessagesSent = 0,\n+                    MessagesSent = 0, // Would need to get from usage metrics\n                     MessagesSucceeded = 0,\n                     MessagesFailed = 0,\n-                    AverageResponseTime = 0,\n+                    AverageResponseTime = statusReport.CurrentLatencyMs,\n                     LastActivity = DateTime.UtcNow,\n-                    CustomMetrics = new Dictionary<string, object>()\n+                    CustomMetrics = new Dictionary<string, object>\n+                    {\n+                        [\"IsHealthy\"] = statusReport.IsHealthy,\n+                        [\"Message\"] = statusReport.Message,\n+                        [\"ConnectivityStatus\"] = statusReport.ConnectivityStatus\n+                    }\n                 };\n             }\n+\n+            // Return basic metrics if plugin doesn't implement metrics interface\n+            return new PluginMetrics\n+            {\n+                PluginName = pluginName,\n+                MessagesSent = 0,\n+                MessagesSucceeded = 0,\n+                MessagesFailed = 0,\n+                AverageResponseTime = 0,\n+                LastActivity = DateTime.UtcNow,\n+                CustomMetrics = new Dictionary<string, object>()\n+            };\n         }\n         catch (Exception ex)\n         {\n             _logger.LogError(ex, \"Failed to get metrics for plugin {PluginName}\", pluginName);\n"}, {"date": 1751238705202, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,9 +24,9 @@\n         {\n             if (!Directory.Exists(pluginDirectory))\n             {\n                 return GatewayUtilities.CreateOperationResult(false, $\"Plugin directory does not exist: {pluginDirectory}, starting creation\");\n-                Dire\n+                Directo\n             }\n \n             var pluginFiles = Directory.GetFiles(pluginDirectory, \"*.dll\", SearchOption.AllDirectories);\n             var loadedCount = 0;\n"}, {"date": 1751238716625, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,10 +23,10 @@\n         try\n         {\n             if (!Directory.Exists(pluginDirectory))\n             {\n+                Directory\n                 return GatewayUtilities.CreateOperationResult(false, $\"Plugin directory does not exist: {pluginDirectory}, starting creation\");\n-                Directory.\n             }\n \n             var pluginFiles = Directory.GetFiles(pluginDirectory, \"*.dll\", SearchOption.AllDirectories);\n             var loadedCount = 0;\n"}], "date": 1751212338917, "name": "Commit-0", "content": "using Microsoft.Extensions.Logging;\nusing PluginCore.Base;\nusing PluginCore.Interfaces;\nusing PluginCore.Models;\nusing PluginCore.Utilities;\nusing System.Reflection;\nusing System.Text.Json;\n\nnamespace PluginCore.Services;\n\n/// <summary>\n/// Default implementation of the plugin manager.\n/// </summary>\npublic class PluginManager : IPluginManager\n{\n    private readonly ILogger<PluginManager> _logger;\n    private readonly Dictionary<string, LoadedPlugin> _loadedPlugins = new();\n    private readonly Dictionary<Type, List<object>> _pluginsByType = new();\n    private readonly object _lock = new();\n\n    public PluginManager(ILogger<PluginManager> logger)\n    {\n        _logger = logger;\n    }\n\n    public async Task<OperationResult> LoadPluginsAsync(string pluginDirectory, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            if (!Directory.Exists(pluginDirectory))\n            {\n                return GatewayUtilities.CreateOperationResult(false, $\"Plugin directory does not exist: {pluginDirectory}\");\n            }\n\n            var pluginFiles = Directory.GetFiles(pluginDirectory, \"*.dll\", SearchOption.AllDirectories);\n            var loadedCount = 0;\n            var errors = new List<string>();\n\n            foreach (var pluginFile in pluginFiles)\n            {\n                try\n                {\n                    var result = await LoadSinglePluginAsync(pluginFile, cancellationToken);\n                    if (result.IsSuccess)\n                    {\n                        loadedCount++;\n                    }\n                    else\n                    {\n                        errors.Add($\"{Path.GetFileName(pluginFile)}: {result.Message}\");\n                    }\n                }\n                catch (Exception ex)\n                {\n                    _logger.LogError(ex, \"Failed to load plugin from {PluginFile}\", pluginFile);\n                    errors.Add($\"{Path.GetFileName(pluginFile)}: {ex.Message}\");\n                }\n            }\n\n            var message = $\"Loaded {loadedCount} plugins successfully\";\n            if (errors.Any())\n            {\n                message += $\". {errors.Count} failed to load.\";\n            }\n\n            _logger.LogInformation(message);\n            return GatewayUtilities.CreateOperationResult(true, message);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to load plugins from directory {PluginDirectory}\", pluginDirectory);\n            return GatewayUtilities.CreateOperationResult(false, \"Failed to load plugins\", ex);\n        }\n    }\n\n    public async Task<OperationResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            if (!File.Exists(pluginPath))\n            {\n                return GatewayUtilities.CreateOperationResult(false, $\"Plugin file does not exist: {pluginPath}\");\n            }\n\n            return await LoadSinglePluginAsync(pluginPath, cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to load plugin from {PluginPath}\", pluginPath);\n            return GatewayUtilities.CreateOperationResult(false, \"Failed to load plugin\", ex);\n        }\n    }\n\n    public async Task<OperationResult> LoadPluginByNameAsync(string pluginName, string pluginDirectory, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            if (!Directory.Exists(pluginDirectory))\n            {\n                return GatewayUtilities.CreateOperationResult(false, $\"Plugin directory does not exist: {pluginDirectory}\");\n            }\n\n            // Search for plugin file by name\n            var pluginFiles = Directory.GetFiles(pluginDirectory, \"*.dll\", SearchOption.AllDirectories)\n                .Where(file => Path.GetFileNameWithoutExtension(file).Contains(pluginName, StringComparison.OrdinalIgnoreCase))\n                .ToList();\n\n            if (!pluginFiles.Any())\n            {\n                return GatewayUtilities.CreateOperationResult(false, $\"No plugin files found matching name: {pluginName}\");\n            }\n\n            if (pluginFiles.Count > 1)\n            {\n                _logger.LogWarning(\"Multiple plugin files found for {PluginName}, using first match: {PluginFile}\",\n                    pluginName, pluginFiles.First());\n            }\n\n            return await LoadSinglePluginAsync(pluginFiles.First(), cancellationToken);\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to load plugin by name {PluginName}\", pluginName);\n            return GatewayUtilities.CreateOperationResult(false, \"Failed to load plugin\", ex);\n        }\n    }\n\n    public IReadOnlyList<T> GetPlugins<T>() where T : class, IPluginType\n    {\n        lock (_lock)\n        {\n            if (_pluginsByType.TryGetValue(typeof(T), out var plugins))\n            {\n                return plugins.Cast<T>().ToList().AsReadOnly();\n            }\n            return new List<T>().AsReadOnly();\n        }\n    }\n\n    public T? GetPlugin<T>(string pluginName) where T : class, IPluginType\n    {\n        lock (_lock)\n        {\n            if (_loadedPlugins.TryGetValue(pluginName, out var loadedPlugin) && \n                loadedPlugin.Instance is T plugin)\n            {\n                return plugin;\n            }\n            return null;\n        }\n    }\n\n    public IReadOnlyList<PluginManifest> GetPluginManifests()\n    {\n        lock (_lock)\n        {\n            return _loadedPlugins.Values\n                .Select(p => p.Manifest)\n                .ToList()\n                .AsReadOnly();\n        }\n    }\n\n    public PluginManifest? GetPluginManifest(string pluginName)\n    {\n        lock (_lock)\n        {\n            return _loadedPlugins.TryGetValue(pluginName, out var plugin) ? plugin.Manifest : null;\n        }\n    }\n\n    public async Task<OperationResult> UnloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            if (!_loadedPlugins.TryGetValue(pluginName, out var loadedPlugin))\n            {\n                return GatewayUtilities.CreateOperationResult(false, $\"Plugin '{pluginName}' not found\");\n            }\n\n            // Remove from type collections\n            var pluginType = loadedPlugin.Instance.GetType();\n            var interfaces = pluginType.GetInterfaces().Where(i => typeof(IPluginType).IsAssignableFrom(i));\n            \n            foreach (var interfaceType in interfaces)\n            {\n                if (_pluginsByType.TryGetValue(interfaceType, out var plugins))\n                {\n                    plugins.Remove(loadedPlugin.Instance);\n                    if (!plugins.Any())\n                    {\n                        _pluginsByType.Remove(interfaceType);\n                    }\n                }\n            }\n\n            // Dispose if disposable\n            if (loadedPlugin.Instance is IDisposable disposable)\n            {\n                disposable.Dispose();\n            }\n\n            _loadedPlugins.Remove(pluginName);\n            \n            _logger.LogInformation(\"Plugin '{PluginName}' unloaded successfully\", pluginName);\n            return GatewayUtilities.CreateOperationResult(true, $\"Plugin '{pluginName}' unloaded successfully\");\n        }\n    }\n\n    public async Task<OperationResult> ReloadPluginAsync(string pluginName, CancellationToken cancellationToken = default)\n    {\n        lock (_lock)\n        {\n            if (!_loadedPlugins.TryGetValue(pluginName, out var loadedPlugin))\n            {\n                return GatewayUtilities.CreateOperationResult(false, $\"Plugin '{pluginName}' not found\");\n            }\n\n            var pluginPath = loadedPlugin.AssemblyPath;\n            \n            // Unload first\n            var unloadResult = UnloadPluginAsync(pluginName, cancellationToken).Result;\n            if (!unloadResult.IsSuccess)\n            {\n                return unloadResult;\n            }\n\n            // Reload\n            return LoadSinglePluginAsync(pluginPath, cancellationToken).Result;\n        }\n    }\n\n    public async Task<IReadOnlyList<PluginStatus>> GetPluginStatusesAsync(CancellationToken cancellationToken = default)\n    {\n        var statuses = new List<PluginStatus>();\n\n        lock (_lock)\n        {\n            foreach (var (name, loadedPlugin) in _loadedPlugins)\n            {\n                try\n                {\n                    var isHealthy = true;\n                    var status = \"Loaded\";\n                    string? errorMessage = null;\n\n                    // Check if plugin is healthy (if it implements health check)\n                    if (loadedPlugin.Instance is IGatewayMessagePluginType gateway)\n                    {\n                        try\n                        {\n                            isHealthy = await gateway.IsAvailableAsync(cancellationToken);\n                            status = isHealthy ? \"Healthy\" : \"Unhealthy\";\n                        }\n                        catch (Exception ex)\n                        {\n                            isHealthy = false;\n                            status = \"Error\";\n                            errorMessage = ex.Message;\n                        }\n                    }\n\n                    statuses.Add(new PluginStatus(\n                        Name: name,\n                        Version: loadedPlugin.Manifest.Version,\n                        Type: loadedPlugin.Manifest.Type,\n                        IsLoaded: true,\n                        IsHealthy: isHealthy,\n                        Status: status,\n                        LastChecked: DateTimeOffset.UtcNow,\n                        ErrorMessage: errorMessage\n                    ));\n                }\n                catch (Exception ex)\n                {\n                    statuses.Add(new PluginStatus(\n                        Name: name,\n                        Version: loadedPlugin.Manifest.Version,\n                        Type: loadedPlugin.Manifest.Type,\n                        IsLoaded: true,\n                        IsHealthy: false,\n                        Status: \"Error\",\n                        LastChecked: DateTimeOffset.UtcNow,\n                        ErrorMessage: ex.Message\n                    ));\n                }\n            }\n        }\n\n        return statuses.AsReadOnly();\n    }\n\n    public async Task<ValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)\n    {\n        try\n        {\n            if (!File.Exists(pluginPath))\n            {\n                return new ValidationResult { IsValid = false, ErrorMessage = \"Plugin file does not exist\" };\n            }\n\n            // Try to load assembly\n            var assembly = Assembly.LoadFrom(pluginPath);\n            \n            // Look for plugin types\n            var pluginTypes = assembly.GetTypes()\n                .Where(t => t.IsClass && !t.IsAbstract && typeof(IPluginType).IsAssignableFrom(t))\n                .ToList();\n\n            if (!pluginTypes.Any())\n            {\n                return new ValidationResult { IsValid = false, ErrorMessage = \"No plugin types found in assembly\" };\n            }\n\n            // Validate each plugin type\n            foreach (var pluginType in pluginTypes)\n            {\n                // Check if it has a parameterless constructor or constructor with ILogger\n                var constructors = pluginType.GetConstructors();\n                var hasValidConstructor = constructors.Any(c => \n                    c.GetParameters().Length == 0 || \n                    (c.GetParameters().Length == 1 && c.GetParameters()[0].ParameterType.Name.Contains(\"ILogger\")));\n\n                if (!hasValidConstructor)\n                {\n                    return new ValidationResult \n                    { \n                        IsValid = false, \n                        ErrorMessage = $\"Plugin type {pluginType.Name} does not have a valid constructor\" \n                    };\n                }\n            }\n\n            return new ValidationResult { IsValid = true };\n        }\n        catch (Exception ex)\n        {\n            return new ValidationResult { IsValid = false, ErrorMessage = ex.Message };\n        }\n    }\n\n    private async Task<OperationResult> LoadSinglePluginAsync(string pluginPath, CancellationToken cancellationToken)\n    {\n        try\n        {\n            // Validate first\n            var validation = await ValidatePluginAsync(pluginPath, cancellationToken);\n            if (!validation.IsValid)\n            {\n                return GatewayUtilities.CreateOperationResult(false, validation.ErrorMessage ?? \"Plugin validation failed\");\n            }\n\n            var assembly = Assembly.LoadFrom(pluginPath);\n            var pluginTypes = assembly.GetTypes()\n                .Where(t => t.IsClass && !t.IsAbstract && typeof(IPluginType).IsAssignableFrom(t))\n                .ToList();\n\n            foreach (var pluginType in pluginTypes)\n            {\n                try\n                {\n                    // Create instance\n                    var instance = CreatePluginInstance(pluginType);\n                    if (instance == null)\n                    {\n                        continue;\n                    }\n\n                    // Get manifest\n                    var manifest = await GetPluginManifestAsync(instance);\n                    if (manifest == null)\n                    {\n                        _logger.LogWarning(\"Plugin {PluginType} does not provide a manifest\", pluginType.Name);\n                        continue;\n                    }\n\n                    // Store plugin\n                    var loadedPlugin = new LoadedPlugin(instance, manifest, pluginPath);\n                    \n                    lock (_lock)\n                    {\n                        _loadedPlugins[manifest.Name] = loadedPlugin;\n                        \n                        // Add to type collections\n                        var interfaces = pluginType.GetInterfaces().Where(i => typeof(IPluginType).IsAssignableFrom(i));\n                        foreach (var interfaceType in interfaces)\n                        {\n                            if (!_pluginsByType.ContainsKey(interfaceType))\n                            {\n                                _pluginsByType[interfaceType] = new List<object>();\n                            }\n                            _pluginsByType[interfaceType].Add(instance);\n                        }\n                    }\n\n                    _logger.LogInformation(\"Loaded plugin: {PluginName} v{Version} ({Type})\", \n                        manifest.Name, manifest.Version, manifest.Type);\n                }\n                catch (Exception ex)\n                {\n                    _logger.LogError(ex, \"Failed to load plugin type {PluginType}\", pluginType.Name);\n                }\n            }\n\n            return GatewayUtilities.CreateOperationResult(true, \"Plugin loaded successfully\");\n        }\n        catch (Exception ex)\n        {\n            return GatewayUtilities.CreateOperationResult(false, \"Failed to load plugin\", ex);\n        }\n    }\n\n    private object? CreatePluginInstance(Type pluginType)\n    {\n        try\n        {\n            // Try constructor with ILogger parameter first\n            var loggerConstructor = pluginType.GetConstructors()\n                .FirstOrDefault(c => c.GetParameters().Length == 1 && \n                                   c.GetParameters()[0].ParameterType.Name.Contains(\"ILogger\"));\n\n            if (loggerConstructor != null)\n            {\n                var loggerType = typeof(ILogger<>).MakeGenericType(pluginType);\n                var logger = _logger; // Use the plugin manager's logger for now\n                return Activator.CreateInstance(pluginType, logger);\n            }\n\n            // Try parameterless constructor\n            var parameterlessConstructor = pluginType.GetConstructors()\n                .FirstOrDefault(c => c.GetParameters().Length == 0);\n\n            if (parameterlessConstructor != null)\n            {\n                return Activator.CreateInstance(pluginType);\n            }\n\n            return null;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to create instance of plugin type {PluginType}\", pluginType.Name);\n            return null;\n        }\n    }\n\n    private async Task<PluginManifest?> GetPluginManifestAsync(object pluginInstance)\n    {\n        try\n        {\n            // Try to get manifest from admin interface\n            if (pluginInstance is IGatewayAdminPluginType adminPlugin)\n            {\n                return await adminPlugin.GetManifestAsync();\n            }\n\n            // Fallback: create a basic manifest\n            var type = pluginInstance.GetType();\n            return new PluginManifest(\n                Name: type.Name,\n                Version: \"1.0.0\",\n                Description: $\"Plugin: {type.Name}\",\n                Author: \"Unknown\",\n                Type: GetPluginTypeString(type),\n                Provider: \"Unknown\",\n                AssemblyName: type.Assembly.GetName().Name ?? \"Unknown\",\n                EntryPoint: type.FullName ?? \"Unknown\",\n                Dependencies: new List<PluginDependency>(),\n                Configuration: new Dictionary<string, PluginConfigurationItem>(),\n                SupportedFeatures: new List<string>()\n            );\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Failed to get manifest for plugin {PluginType}\", pluginInstance.GetType().Name);\n            return null;\n        }\n    }\n\n    private string GetPluginTypeString(Type type)\n    {\n        if (typeof(ISmsPlugin).IsAssignableFrom(type)) return \"SMS\";\n        if (typeof(IEmailPlugin).IsAssignableFrom(type)) return \"Email\";\n        if (typeof(IPushPlugin).IsAssignableFrom(type)) return \"Push\";\n        if (typeof(IWebAppPlugin).IsAssignableFrom(type)) return \"WebApp\";\n        return \"Unknown\";\n    }\n}\n\n/// <summary>\n/// Represents a loaded plugin with its metadata.\n/// </summary>\ninternal record LoadedPlugin(object Instance, PluginManifest Manifest, string AssemblyPath);\n"}]}