// Location: PluginCore/Models/GatewayModels.cs

namespace PluginCore.Models;

// --- Core Primitives ---

/// <summary>
/// A universal response for operations that may succeed or fail, providing detailed error information.
/// </summary>
/// <param name="Errors">Additional error details, if applicable.</param>
/// <param name="Message">A human-readable message describing the result.</param>
/// <param name="IsSuccess">Indicates whether the operation was successful.</param>
public record OperationResult(bool IsSuccess, string? Message = null, IEnumerable<ErrorDetail>? Errors = null)
{
    public static OperationResult Success(string? message = null) => new(true, message);
    public static OperationResult Failure(string? message = null, IEnumerable<ErrorDetail>? errors = null) => new(false, message, errors);
}

/// <summary>
/// A universal response for operations that may succeed or fail, with a data payload.
/// </summary>
/// <typeparam name="T">The type of data returned on success</typeparam>
public record OperationResult<T>(bool IsSuccess, T? Data = default, string? Message = null, IEnumerable<ErrorDetail>? Errors = null)
{
    public static OperationResult<T> Success(T data, string? message = null) => new(true, data, message);
    public static OperationResult<T> Failure(string? message = null, IEnumerable<ErrorDetail>? errors = null) => new(false, default, message, errors);
}

/// <summary>
/// A structured error detail used in OperationResult.
/// </summary>
/// <param name="Code">A unique code identifying the error.</param>
/// <param name="Description">A human-readable description of the error.</param>
/// <param name="Target">The target of the error (e.g., a field name).</param>
public record ErrorDetail(string Code, string Description, string? Target = null);

// --- Message-Related Models ---

/// <summary>
/// Defines the complete payload for sending a single message, used across multiple methods.
/// </summary>
public class MessagePayload
{
    public Recipient Recipient { get; set; } = new();
    public string Content { get; set; } = string.Empty;
    public string? From { get; set; }
    public string? Subject { get; set; }
    public Dictionary<string, string>? Headers { get; set; }
    public string? CorrelationId { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
    public PushNotificationData? PushNotificationData { get; set; }
    public WebAppNotificationData? WebAppNotificationData { get; set; }
}

/// <summary>
/// The result of a successful message send operation.
/// </summary>
/// <param name="MessageId">The unique identifier for the sent message.</param>
/// <param name="Timestamp">The timestamp of the send operation.</param>
/// <param name="Status">The status of the message (e.g., sent, queued).</param>
/// <param name="CorrelationId">An optional correlation ID for tracking.</param>
public record MessageSendResult(string MessageId, DateTimeOffset Timestamp, string Status, string? CorrelationId = null);

/// <summary>
/// The result of a message scheduling operation.
/// </summary>
/// <param name="ScheduledMessageId">The unique identifier for the scheduled message.</param>
/// <param name="ScheduledTime">The scheduled time of the message.</param>
/// <param name="Status">The status of the scheduling operation.</param>
public record MessageScheduleResult(string ScheduledMessageId, DateTimeOffset ScheduledTime, string Status);

/// <summary>
/// Contains the real-time status of a message.
/// </summary>
/// <param name="MessageId">The unique identifier for the message.</param>
/// <param name="Status">The current status of the message.</param>
/// <param name="LastUpdated">The timestamp of the last status update.</param>
/// <param name="Reason">An optional reason for the current status.</param>
public record MessageStatusInfo(string MessageId, string Status, DateTimeOffset LastUpdated, string? Reason = null);

/// <summary>
/// Contains a provider-issued delivery receipt.
/// </summary>
/// <param name="MessageId">The unique identifier for the message.</param>
/// <param name="Status">The status of the message as reported by the provider.</param>
/// <param name="Timestamp">The timestamp of the delivery receipt.</param>
/// <param name="ProviderCode">An optional code provided by the provider for the status.</param>
public record DeliveryReceipt(string MessageId, string Status, DateTimeOffset Timestamp, string? ProviderCode = null);

/// <summary>
/// The response from a raw payload send.
/// </summary>
/// <param name="IsSuccess">Indicates whether the operation was successful.</param>
/// <param name="StatusCode">The HTTP status code of the response.</param>
/// <param name="Body">The body of the response.</param>
/// <param name="Errors">Additional error details, if applicable.</param>
public record RawGatewayResponse(bool IsSuccess, int StatusCode, string Body, IEnumerable<ErrorDetail>? Errors = null);

/// <summary>
/// A message prepared for sending but not yet dispatched, used for previews or dry runs.
/// </summary>
/// <param name="RenderedPayload">The final, rendered payload to be sent.</param>
/// <param name="FinalHeaders">The final headers to be included with the message.</param>
/// <param name="Validation">The result of any validation performed on the message.</param>
public record PreparedMessage(object RenderedPayload, Dictionary<string, string> FinalHeaders, ValidationResult Validation);


// --- Metrics-Related Models ---

/// <summary>
/// A comprehensive report on the gateway's current operational status.
/// </summary>
/// <param name="IsHealthy">Indicates whether the gateway is healthy.</param>
/// <param name="CurrentLatencyMs">The current latency in milliseconds.</param>
/// <param name="Message">A human-readable message describing the status.</param>
/// <param name="ConnectivityStatus">The status of each connectivity check.</param>
public record GatewayStatusReport(bool IsHealthy, double CurrentLatencyMs, string Message, Dictionary<string, string> ConnectivityStatus);

/// <summary>
/// The result of a message delivery report query.
/// </summary>
/// <param name="MessageId">The unique identifier for the message.</param>
/// <param name="Status">The current status of the message.</param>
/// <param name="Timestamp">The timestamp of the last status update.</param>
/// <param name="FailureReason">The reason for failure, if applicable.</param>
public record DeliveryResult(string MessageId, string Status, DateTimeOffset Timestamp, string? FailureReason = null);

/// <summary>
/// A summary of usage metrics over a specified period.
/// </summary>
/// <param name="MessagesSent">The total number of messages sent.</param>
/// <param name="MessagesFailed">The total number of messages that failed.</param>
/// <param name="MessagesQueued">The total number of messages queued.</param>
/// <param name="EstimatedCost">The estimated cost of operations.</param>
public record UsageMetrics(int MessagesSent, int MessagesFailed, int MessagesQueued, decimal EstimatedCost);

/// <summary>
/// A single entry in the gateway's error log.
/// </summary>
/// <param name="ErrorId">The unique identifier for the error.</param>
/// <param name="ErrorCode">The code associated with the error.</param>
/// <param name="Description">A human-readable description of the error.</param>
/// <param name="Severity">The severity of the error (e.g., low, medium, high).</param>
/// <param name="Timestamp">The timestamp of the error entry.</param>
public record GatewayErrorEntry(string ErrorId, string ErrorCode, string Description, string Severity, DateTimeOffset Timestamp);

/// <summary>
/// A snapshot of system throughput.
/// </summary>
/// <param name="MessagesPerMinute">The number of messages processed per minute.</param>
/// <param name="AverageDeliveryTimeMs">The average delivery time in milliseconds.</param>
/// <param name="Resolution">The time resolution of the snapshot (e.g., 1 minute, 5 minutes).</param>
public record PerformanceSnapshot(int MessagesPerMinute, double AverageDeliveryTimeMs, TimeSpan Resolution);

/// <summary>
/// A report on Service Level Agreement (SLA) compliance.
/// </summary>
/// <param name="AvailabilityPercentage">The percentage of time the service was available.</param>
/// <param name="AverageDeliveryTimeMs">The average delivery time in milliseconds.</param>
/// <param name="Breaches">The number of SLA breaches.</param>
public record SlaReport(double AvailabilityPercentage, double AverageDeliveryTimeMs, int Breaches);

/// <summary>
/// A breakdown of latencies for different gateway operations.
/// </summary>
/// <param name="AuthenticationMs">The average latency for authentication operations.</param>
/// <param name="SendApiCallMs">The average latency for sending API calls.</param>
/// <param name="DeliveryCallbackMs">The average latency for delivery callbacks.</param>
public record LatencyMetrics(double AuthenticationMs, double SendApiCallMs, double DeliveryCallbackMs);

/// <summary>
/// Data for analyzing traffic trends.
/// </summary>
/// <param name="Period">The time period for the trend.</param>
/// <param name="Granularity">The granularity of the trend (e.g., hourly, daily).</param>
/// <param name="TotalMessages">The total number of messages during the period.</param>
/// <param name="Delivered">The number of messages delivered.</param>
/// <param name="Failed">The number of messages failed.</param>

public record TrafficTrend(DateTime Period, string Granularity, int TotalMessages, int Delivered, int Failed, double AvgLatencyMs);

/// <summary>
/// The result of anomaly detection analysis.
/// </summary>
/// <param name="AnomalyId">The unique identifier for the anomaly.</param>
/// <param name="DetectedAt">The timestamp when the anomaly was detected.</param>
/// <param name="Description">A human-readable description of the anomaly.</param>
/// <param name="Severity">The severity of the anomaly (e.g., low, medium, high).</param>
public record AnomalyDetectionResult(string AnomalyId, DateTime DetectedAt, string Description, string Severity);

/// <summary>
/// Represents a generated, downloadable report.
/// </summary>
/// <param name="FileName">The name of the report file.</param>
/// <param name="MimeType">The MIME type of the report.</param>
/// <param name="Content">The content of the report.</param>
public record GeneratedReport(string FileName, string MimeType, byte[] Content);

/// <summary>
/// Information about a single retry attempt.
/// </summary>
/// <param name="MessageId">The unique identifier for the message.</param>
/// <param name="RetryCount">The number of the retry attempt.</param>
/// <param name="ErrorCode">The error code that triggered the retry.</param>
/// <param name="Outcome">The outcome of the retry attempt.</param>
/// <param name="AttemptedAt">The timestamp of the retry attempt.</param>
public record RetryAttemptInfo(string MessageId, int RetryCount, string ErrorCode, string Outcome, DateTime AttemptedAt);

/// <summary>
/// A record of a configuration change and its observed impact.
/// </summary>
/// <param name="ChangeId">The unique identifier for the configuration change.</param>
/// <param name="ChangedAt">The timestamp of the configuration change.</param>
/// <param name="Description">A human-readable description of the change.</param>
/// <param name="ImpactAnalysis">The analysis of the impact of the change.</param>
public record ConfigurationImpactRecord(string ChangeId, DateTime ChangedAt, string Description, string ImpactAnalysis);

/// <summary>
/// A Recipient class where all fields are optional to support different gateway types.
/// </summary>
public class Recipient
{
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
    public string? DeviceToken { get; set; }
    public string? UserId { get; set; }
    public string? Address { get; set; }
    public string? ReceptorName { get; set; }
    public string? Topic { get; set; }
    public bool IsBroadcast { get; set; }
    public List<string>? TargetRoles { get; set; }
    public List<string>? ExcludeUserIds { get; set; }
    public Dictionary<string, string>? PersonalizationData { get; set; }
}

/// <summary>
/// Options for generating a metrics report.
/// </summary>
/// <param name="Format">The format of the report (e.g., csv, pdf).</param>
/// <param name="Language">The language of the report (e.g., en-US, fr-FR).</param>
public record MetricsReportOptions(string Format = "csv", string Language = "en-US");

public record GatewayConfiguration(
    string Key,
    string DisplayName,
    string? Value,
    string? Description,
    bool IsSensitive,
    bool IsRequired,
    string ValueType = "string",
    string? DefaultValue = null,
    List<string>? AllowedValues = null,
    Dictionary<string, string>? Metadata = null
);
public record PluginManifest(string Name, string Version, string Description, string Author, string Type, string Provider, string AssemblyName, string EntryPoint, List<PluginDependency> Dependencies, Dictionary<string, PluginConfigurationItem> Configuration, List<string> SupportedFeatures, Dictionary<string, object>? Metadata = null, string MinimumFrameworkVersion = "net9.0", bool IsEnabled = true, int Priority = 100);

public record PluginDependency(string Name, string Version, bool IsRequired = true);

public record PluginConfigurationItem(string Type, string Description, List<string>? ValidationRules = null, object? DefaultValue = null, bool IsRequired = true, bool IsSecret = false);

/// <summary>
/// Represents the result of a validation operation.
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> Errors { get; set; } = new();

    public ValidationResult() { }

    public ValidationResult(bool isValid, string? errorMessage = null)
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
        if (!string.IsNullOrEmpty(errorMessage))
        {
            Errors.Add(errorMessage);
        }
    }
}

/// <summary>
/// Represents a plugin in the system
/// </summary>
public class Plugin
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public bool IsLoaded { get; set; }
    public bool IsEnabled { get; set; }
    public bool IsHealthy { get; set; }
    public DateTime LoadedAt { get; set; }
    public DateTime LastChecked { get; set; }
    public string? AssemblyPath { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<string> Features { get; set; } = new();
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Represents plugin metrics
/// </summary>
public class PluginMetrics
{
    public string PluginName { get; set; } = string.Empty;
    public int MessagesSent { get; set; }
    public int MessagesSucceeded { get; set; }
    public int MessagesFailed { get; set; }
    public double AverageResponseTime { get; set; }
    public DateTime LastActivity { get; set; }
    public Dictionary<string, object> CustomMetrics { get; set; } = new();
}

/// <summary>
/// Push notification specific data
/// </summary>
public class PushNotificationData
{
    public string Title { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string? Icon { get; set; }
    public string? Sound { get; set; }
    public string? Badge { get; set; }
    public string? ClickAction { get; set; }
    public Dictionary<string, object> CustomData { get; set; } = new();
}

/// <summary>
/// Web app notification specific data
/// </summary>
public class WebAppNotificationData
{
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string Type { get; set; } = "info"; // info, success, warning, error
    public string? Icon { get; set; }
    public string? ActionUrl { get; set; }
    public string? ActionText { get; set; }
    public bool IsPersistent { get; set; } = true;
    public DateTime? ExpiresAt { get; set; }
    public Dictionary<string, object> CustomData { get; set; } = new();
}