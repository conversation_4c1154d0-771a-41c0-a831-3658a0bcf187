{"sourceFile": "src/Core/NotifyMaster.Entities/PermissionEntity.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751238914312, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751238914312, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\n\nnamespace NotifyMaster.Entities;\n\n/// <summary>\n/// Database entity for Permission\n/// </summary>\n[Table(\"Permissions\")]\npublic class PermissionEntity\n{\n    [Key]\n    [MaxLength(36)]\n    public string Id { get; set; } = string.Empty;\n    \n    [Required]\n    [MaxLength(100)]\n    public string Resource { get; set; } = string.Empty;\n    \n    [Required]\n    [MaxLength(100)]\n    public string Action { get; set; } = string.Empty;\n    \n    [MaxLength(500)]\n    public string? Description { get; set; }\n    \n    public bool IsSystemPermission { get; set; } = false;\n    \n    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;\n    \n    [MaxLength(36)]\n    public string? CreatedBy { get; set; }\n    \n    // Navigation properties\n    public virtual ICollection<UserPermissionEntity> Users { get; set; } = new List<UserPermissionEntity>();\n    public virtual ICollection<RolePermissionEntity> Roles { get; set; } = new List<RolePermissionEntity>();\n\n}\n"}]}