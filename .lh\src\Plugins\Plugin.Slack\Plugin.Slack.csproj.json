{"sourceFile": "src/Plugins/Plugin.Slack/Plugin.Slack.csproj", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751236932004, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751236932003, "name": "Commit-0", "content": "<Project Sdk=\"Microsoft.NET.Sdk\">\r\n\r\n    <PropertyGroup>\r\n        <TargetFramework>net9.0</TargetFramework>\r\n        <Nullable>enable</Nullable>\r\n        <ImplicitUsings>enable</ImplicitUsings>\r\n        <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>\r\n    </PropertyGroup>\r\n\r\n    <ItemGroup>\r\n        <PackageReference Include=\"Microsoft.Extensions.Logging.Abstractions\" Version=\"9.0.6\" />\r\n        <PackageReference Include=\"Microsoft.Extensions.Options\" Version=\"9.0.6\" />\r\n        <PackageReference Include=\"Microsoft.Extensions.Http\" Version=\"9.0.0\" />\r\n        <PackageReference Include=\"System.Text.Json\" Version=\"9.0.0\" />\r\n    </ItemGroup>\r\n\r\n \r\n\r\n    <ItemGroup>\r\n        <EmbeddedResource Include=\"manifest.json\" />\r\n    </ItemGroup>\r\n\r\n</Project>\r\n"}]}