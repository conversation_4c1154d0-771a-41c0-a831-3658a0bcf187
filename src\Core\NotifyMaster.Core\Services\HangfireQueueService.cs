// Using statements are handled by GlobalUsings.cs
using Hangfire;

namespace NotifyMaster.Core.Services;

/// <summary>
/// Hangfire-based implementation of queue service
/// </summary>
public class HangfireQueueService : IQueueService
{
    private readonly ILogger<HangfireQueueService> _logger;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IRecurringJobManager _recurringJobManager;

    public HangfireQueueService(
        ILogger<HangfireQueueService> logger,
        IBackgroundJobClient backgroundJobClient,
        IRecurringJobManager recurringJobManager)
    {
        _logger = logger;
        _backgroundJobClient = backgroundJobClient;
        _recurringJobManager = recurringJobManager;
    }

    public async Task<OperationResult<string>> EnqueueAsync<T>(string queueName, T message, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var queueMessage = CreateQueueMessage(queueName, message);
            
            var jobId = _backgroundJobClient.Enqueue<IQueueProcessor>(
                processor => processor.ProcessMessageAsync<T>(queueMessage, cancellationToken));

            _logger.LogInformation("Enqueued message {MessageId} to queue {QueueName} with job ID {JobId}", 
                queueMessage.Id, queueName, jobId);

            return OperationResult<string>.Success(jobId, "Message enqueued successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to enqueue message to queue {QueueName}", queueName);
            return OperationResult<string>.Failure("Failed to enqueue message", ex);
        }
    }

    public async Task<OperationResult<string>> EnqueueDelayedAsync<T>(string queueName, T message, TimeSpan delay, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var queueMessage = CreateQueueMessage(queueName, message);
            queueMessage.ScheduledAt = DateTime.UtcNow.Add(delay);
            queueMessage.Status = QueueMessageStatus.Scheduled;

            var jobId = _backgroundJobClient.Schedule<IQueueProcessor>(
                processor => processor.ProcessMessageAsync<T>(queueMessage, cancellationToken),
                delay);

            _logger.LogInformation("Scheduled message {MessageId} to queue {QueueName} with delay {Delay} and job ID {JobId}", 
                queueMessage.Id, queueName, delay, jobId);

            return OperationResult<string>.Success(jobId, "Message scheduled successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to schedule message to queue {QueueName} with delay {Delay}", queueName, delay);
            return OperationResult<string>.Failure("Failed to schedule message", ex);
        }
    }

    public async Task<OperationResult<string>> EnqueueScheduledAsync<T>(string queueName, T message, DateTimeOffset scheduleAt, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var queueMessage = CreateQueueMessage(queueName, message);
            queueMessage.ScheduledAt = scheduleAt.DateTime;
            queueMessage.Status = QueueMessageStatus.Scheduled;

            var jobId = _backgroundJobClient.Schedule<IQueueProcessor>(
                processor => processor.ProcessMessageAsync<T>(queueMessage, cancellationToken),
                scheduleAt);

            _logger.LogInformation("Scheduled message {MessageId} to queue {QueueName} at {ScheduleAt} with job ID {JobId}", 
                queueMessage.Id, queueName, scheduleAt, jobId);

            return OperationResult<string>.Success(jobId, "Message scheduled successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to schedule message to queue {QueueName} at {ScheduleAt}", queueName, scheduleAt);
            return OperationResult<string>.Failure("Failed to schedule message", ex);
        }
    }

    public async Task<OperationResult<T?>> DequeueAsync<T>(string queueName, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            // Hangfire handles dequeuing automatically through job processing
            // This method is primarily for manual dequeuing scenarios
            _logger.LogWarning("Manual dequeue requested for queue {QueueName}. Hangfire handles automatic dequeuing.", queueName);
            return OperationResult<T?>.Success(null, "Hangfire handles automatic dequeuing");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to dequeue message from queue {QueueName}", queueName);
            return OperationResult<T?>.Failure("Failed to dequeue message", ex);
        }
    }

    public async Task<OperationResult<int>> GetQueueCountAsync(string queueName, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = JobStorage.Current.GetConnection();
            var monitoring = JobStorage.Current.GetMonitoringApi();
            
            var enqueuedJobs = monitoring.EnqueuedJobs(queueName, 0, int.MaxValue);
            var count = enqueuedJobs.Count;

            _logger.LogDebug("Queue {QueueName} has {Count} messages", queueName, count);
            return OperationResult<int>.Success(count, "Queue count retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get queue count for {QueueName}", queueName);
            return OperationResult<int>.Failure("Failed to get queue count", ex);
        }
    }

    public async Task<OperationResult> PurgeQueueAsync(string queueName, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = JobStorage.Current.GetConnection();
            var monitoring = JobStorage.Current.GetMonitoringApi();
            
            var enqueuedJobs = monitoring.EnqueuedJobs(queueName, 0, int.MaxValue);
            foreach (var job in enqueuedJobs)
            {
                _backgroundJobClient.Delete(job.Key);
            }

            _logger.LogInformation("Purged {Count} messages from queue {QueueName}", enqueuedJobs.Count, queueName);
            return OperationResult.Success("Queue purged successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to purge queue {QueueName}", queueName);
            return OperationResult.Failure("Failed to purge queue", ex);
        }
    }

    public async Task<OperationResult> CancelScheduledMessageAsync(string messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = _backgroundJobClient.Delete(messageId);
            
            if (result)
            {
                _logger.LogInformation("Cancelled scheduled message {MessageId}", messageId);
                return OperationResult.Success("Scheduled message cancelled successfully");
            }
            else
            {
                _logger.LogWarning("Failed to cancel scheduled message {MessageId} - job not found or already processed", messageId);
                return OperationResult.Failure("Message not found or already processed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel scheduled message {MessageId}", messageId);
            return OperationResult.Failure("Failed to cancel scheduled message", ex);
        }
    }

    public async Task<OperationResult<QueueStatistics>> GetQueueStatisticsAsync(string queueName, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = JobStorage.Current.GetConnection();
            var monitoring = JobStorage.Current.GetMonitoringApi();
            
            var enqueuedJobs = monitoring.EnqueuedJobs(queueName, 0, int.MaxValue);
            var processingJobs = monitoring.ProcessingJobs(0, int.MaxValue);
            var succeededJobs = monitoring.SucceededJobs(0, int.MaxValue);
            var failedJobs = monitoring.FailedJobs(0, int.MaxValue);

            var statistics = new QueueStatistics
            {
                QueueName = queueName,
                PendingMessages = enqueuedJobs.Count,
                ProcessingMessages = processingJobs.Count(j => j.Value.Job.Queue == queueName),
                CompletedMessages = succeededJobs.Count,
                FailedMessages = failedJobs.Count,
                LastActivity = DateTime.UtcNow,
                AverageProcessingTime = 0, // Would need to calculate from job history
                CustomMetrics = new Dictionary<string, object>
                {
                    ["TotalJobs"] = enqueuedJobs.Count + processingJobs.Count() + succeededJobs.Count + failedJobs.Count
                }
            };

            return OperationResult<QueueStatistics>.Success(statistics, "Queue statistics retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get queue statistics for {QueueName}", queueName);
            return OperationResult<QueueStatistics>.Failure("Failed to get queue statistics", ex);
        }
    }

    private QueueMessage<T> CreateQueueMessage<T>(string queueName, T message) where T : class
    {
        return new QueueMessage<T>
        {
            Id = Guid.NewGuid().ToString(),
            QueueName = queueName,
            Payload = message,
            EnqueuedAt = DateTime.UtcNow,
            Status = QueueMessageStatus.Pending,
            Headers = new Dictionary<string, string>
            {
                ["ContentType"] = typeof(T).FullName ?? "unknown",
                ["EnqueuedBy"] = "HangfireQueueService"
            }
        };
    }
}
