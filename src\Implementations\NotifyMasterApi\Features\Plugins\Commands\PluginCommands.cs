namespace NotifyMasterApi.Features.Plugins.Commands;

/// <summary>
/// Command to load a plugin
/// </summary>
public record LoadPluginCommand(string Name, string? Path = null) : IRequest<PluginOperationResult>;

/// <summary>
/// Command to unload a plugin
/// </summary>
public record UnloadPluginCommand(string Name) : IRequest<PluginOperationResult>;

/// <summary>
/// Command to load all plugins from a directory
/// </summary>
public record LoadDirectoryCommand(string Directory = "plugins") : IRequest<PluginOperationResult>;

/// <summary>
/// Command to reload a plugin
/// </summary>
public record ReloadPluginCommand(string Name) : IRequest<PluginOperationResult>;
