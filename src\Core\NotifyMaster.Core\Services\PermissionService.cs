// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Services;

/// <summary>
/// Service for permission management operations
/// </summary>
public class PermissionService(
    IPermissionRepository permissionRepository,
    IRoleRepository roleRepository,
    IUserRepository userRepository,
    ILogger<PermissionService> logger) : IPermissionService
{
    private readonly IPermissionRepository _permissionRepository = permissionRepository;
    private readonly IRoleRepository _roleRepository = roleRepository;
    private readonly IUserRepository _userRepository = userRepository;
    private readonly ILogger<PermissionService> _logger = logger;

    public async Task<Permission?> GetPermissionAsync(string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _permissionRepository.GetByIdAsync(permissionId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permission {PermissionId}", permissionId);
            return null;
        }
    }

    public async Task<Permission?> GetPermissionAsync(string resource, string action, CancellationToken cancellationToken = default)
    {
        try
        {
            var permissions = await _permissionRepository.GetAllAsync(cancellationToken);
            return permissions.FirstOrDefault(p => 
                p.Resource.Equals(resource, StringComparison.OrdinalIgnoreCase) && 
                p.Action.Equals(action, StringComparison.OrdinalIgnoreCase));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permission for resource {Resource} and action {Action}", resource, action);
            return null;
        }
    }

    public async Task<IReadOnlyList<Permission>> GetPermissionsAsync(string? resource = null, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        try
        {
            var allPermissions = await _permissionRepository.GetAllAsync(cancellationToken);
            
            if (!string.IsNullOrEmpty(resource))
            {
                allPermissions = allPermissions.Where(p => p.Resource.Equals(resource, StringComparison.OrdinalIgnoreCase));
            }
            
            return allPermissions.Skip(skip).Take(take).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions with resource={Resource}, skip={Skip}, take={Take}", resource, skip, take);
            return new List<Permission>();
        }
    }

    public async Task<CoreOperationResult<Permission>> CreatePermissionAsync(CreatePermissionRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if permission already exists
            var existingPermission = await GetPermissionAsync(request.Resource, request.Action, cancellationToken);
            if (existingPermission != null)
            {
                return CoreOperationResult<Permission>.Failure("A permission with this resource and action already exists");
            }

            var permission = new Permission
            {
                Id = Guid.NewGuid().ToString(),
                Resource = request.Resource,
                Action = request.Action,
                Description = request.Description,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy
            };

            var createdPermission = await _permissionRepository.AddAsync(permission, cancellationToken);
            _logger.LogInformation("Created permission {PermissionId} for {Resource}:{Action}", createdPermission.Id, request.Resource, request.Action);
            
            return CoreOperationResult<Permission>.Success(createdPermission);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating permission for {Resource}:{Action}", request.Resource, request.Action);
            return CoreOperationResult<Permission>.Failure($"Failed to create permission: {ex.Message}");
        }
    }

    public async Task<CoreOperationResult<Permission>> UpdatePermissionAsync(string permissionId, UpdatePermissionRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var permission = await _permissionRepository.GetByIdAsync(permissionId, cancellationToken);
            if (permission == null)
            {
                return CoreOperationResult<Permission>.Failure("Permission not found");
            }

            // Update properties
            if (!string.IsNullOrEmpty(request.Description))
                permission.Description = request.Description;

            var updatedPermission = await _permissionRepository.UpdateAsync(permission, cancellationToken);
            _logger.LogInformation("Updated permission {PermissionId}", permissionId);

            return CoreOperationResult<Permission>.Success(updatedPermission);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating permission {PermissionId}", permissionId);
            return CoreOperationResult<Permission>.Failure($"Failed to update permission: {ex.Message}");
        }
    }

    public async Task<CoreOperationResult> DeletePermissionAsync(string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            var permission = await _permissionRepository.GetByIdAsync(permissionId, cancellationToken);
            if (permission == null)
            {
                return CoreOperationResult.Failure("Permission not found");
            }

            await _permissionRepository.DeleteAsync(permissionId, cancellationToken);
            _logger.LogInformation("Deleted permission {PermissionId}", permissionId);

            return CoreOperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting permission {PermissionId}", permissionId);
            return CoreOperationResult.Failure($"Failed to delete permission: {ex.Message}");
        }
    }

    public async Task<IReadOnlyList<Role>> GetPermissionRolesAsync(string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement role-permission relationship retrieval
            var roles = await _roleRepository.GetAllAsync(cancellationToken);
            _logger.LogInformation("Retrieved roles for permission {PermissionId}", permissionId);
            return roles.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for permission {PermissionId}", permissionId);
            return new List<Role>();
        }
    }

    public async Task<IReadOnlyList<User>> GetPermissionUsersAsync(string permissionId, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: Implement user-permission relationship retrieval (direct and through roles)
            var users = await _userRepository.GetAllAsync(cancellationToken);
            _logger.LogInformation("Retrieved users for permission {PermissionId}", permissionId);
            return users.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting users for permission {PermissionId}", permissionId);
            return new List<User>();
        }
    }

    /// <summary>
    /// Seeds default permissions for the system
    /// </summary>
    public async Task<CoreOperationResult> SeedDefaultPermissionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Seeding default permissions...");

            var defaultPermissions = new[]
            {
                new { Resource = "Users", Action = "Create", Name = "Create Users", Description = "Create new users" },
                new { Resource = "Users", Action = "Read", Name = "Read Users", Description = "View user information" },
                new { Resource = "Users", Action = "Update", Name = "Update Users", Description = "Update user information" },
                new { Resource = "Users", Action = "Delete", Name = "Delete Users", Description = "Delete users" },
                new { Resource = "Tenants", Action = "Create", Name = "Create Tenants", Description = "Create new tenants" },
                new { Resource = "Tenants", Action = "Read", Name = "Read Tenants", Description = "View tenant information" },
                new { Resource = "Tenants", Action = "Update", Name = "Update Tenants", Description = "Update tenant information" },
                new { Resource = "Tenants", Action = "Delete", Name = "Delete Tenants", Description = "Delete tenants" },
                new { Resource = "Notifications", Action = "Send", Name = "Send Notifications", Description = "Send notifications" },
                new { Resource = "Notifications", Action = "Read", Name = "Read Notifications", Description = "View notification logs" },
                new { Resource = "Plugins", Action = "Manage", Name = "Manage Plugins", Description = "Install, configure, and manage plugins" },
                new { Resource = "System", Action = "Admin", Name = "System Administration", Description = "Full system administration access" }
            };

            foreach (var perm in defaultPermissions)
            {
                var existing = await GetPermissionAsync(perm.Resource, perm.Action, cancellationToken);
                if (existing == null)
                {
                    await CreatePermissionAsync(new CreatePermissionRequest
                    {
                        Description = perm.Description,
                        Resource = perm.Resource,
                        Action = perm.Action,
                        CreatedBy = "System"
                    }, cancellationToken);
                }
            }

            _logger.LogInformation("Default permissions seeded successfully");
            return CoreOperationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error seeding default permissions");
            return CoreOperationResult.Failure($"Failed to seed default permissions: {ex.Message}");
        }
    }
}
