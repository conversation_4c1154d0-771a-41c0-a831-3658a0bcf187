{"sourceFile": "src/Implementations/NotifyMasterApi/Features/Setup/TUI/SetupWizardTUI.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751236451924, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751236451924, "name": "Commit-0", "content": "// Using statements are handled by GlobalUsings.cs\nusing Terminal.Gui;\n\nnamespace NotifyMasterApi.Features.Setup.TUI;\n\n/// <summary>\n/// Classic DOS-style Terminal UI Setup Wizard for first-run configuration\n/// </summary>\npublic class SetupWizardTUI\n{\n    private readonly ISetupService _setupService;\n    private readonly ILogger<SetupWizardTUI> _logger;\n    private readonly IConfiguration _configuration;\n    private readonly IServiceProvider _serviceProvider;\n\n    private Toplevel _top = null!;\n    private Window _mainWindow = null!;\n    private MenuBar _menuBar = null!;\n    private StatusBar _statusBar = null!;\n    private FrameView _contentFrame = null!;\n    private FrameView _buttonFrame = null!;\n\n    private SetupWizardData _wizardData = new();\n    private int _currentStep = 0;\n    private readonly List<SetupStep> _steps = new();\n\n    public SetupWizardTUI(\n        ISetupService setupService,\n        ILogger<SetupWizardTUI> logger,\n        IConfiguration configuration,\n        IServiceProvider serviceProvider)\n    {\n        _setupService = setupService;\n        _logger = logger;\n        _configuration = configuration;\n        _serviceProvider = serviceProvider;\n        \n        InitializeSteps();\n    }\n\n    private void InitializeSteps()\n    {\n        _steps.Add(new SetupStep(\"Welcome\", \"Welcome to NotifyMaster Setup\", ShowWelcomeStep));\n        _steps.Add(new SetupStep(\"Database\", \"Configure Database Connection\", ShowDatabaseStep));\n        _steps.Add(new SetupStep(\"Redis\", \"Configure Redis Connection\", ShowRedisStep));\n        _steps.Add(new SetupStep(\"Tenant\", \"Create First Tenant\", ShowTenantStep));\n        _steps.Add(new SetupStep(\"Admin\", \"Create Admin User\", ShowAdminStep));\n        _steps.Add(new SetupStep(\"Complete\", \"Setup Complete\", ShowCompleteStep));\n    }\n\n    public async Task<bool> RunAsync()\n    {\n        try\n        {\n            // Check if system is already initialized\n            if (await _setupService.IsSystemInitializedAsync())\n            {\n                Console.WriteLine(\"✅ System is already initialized. Skipping setup wizard.\");\n                return true;\n            }\n\n            Application.Init();\n            \n            CreateMainWindow();\n            ShowCurrentStep();\n            \n            Application.Run(_mainWindow);\n            Application.Shutdown();\n            \n            return _wizardData.IsCompleted;\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error running setup wizard\");\n            return false;\n        }\n    }\n\n    private void CreateMainWindow()\n    {\n        _mainWindow = new Window(\"NotifyMaster Setup Wizard\")\n        {\n            X = 0,\n            Y = 0,\n            Width = Dim.Fill(),\n            Height = Dim.Fill()\n        };\n\n        // Title\n        _titleLabel = new Label(\"🚀 NotifyMaster Setup Wizard\")\n        {\n            X = Pos.Center(),\n            Y = 1,\n            ColorScheme = Colors.TopLevel\n        };\n\n        // Progress bar\n        _progressBar = new ProgressBar()\n        {\n            X = 2,\n            Y = 3,\n            Width = Dim.Fill() - 4,\n            Height = 1\n        };\n\n        // Step label\n        _stepLabel = new Label(\"Step 1 of 6: Welcome\")\n        {\n            X = 2,\n            Y = 4\n        };\n\n        // Content frame\n        _contentFrame = new FrameView(\"Setup\")\n        {\n            X = 2,\n            Y = 6,\n            Width = Dim.Fill() - 4,\n            Height = Dim.Fill() - 10\n        };\n\n        // Navigation buttons\n        var prevButton = new Button(\"Previous\")\n        {\n            X = 2,\n            Y = Pos.Bottom(_mainWindow) - 3\n        };\n        prevButton.Clicked += OnPreviousClicked;\n\n        var nextButton = new Button(\"Next\")\n        {\n            X = Pos.Right(prevButton) + 2,\n            Y = Pos.Bottom(_mainWindow) - 3\n        };\n        nextButton.Clicked += OnNextClicked;\n\n        var cancelButton = new Button(\"Cancel\")\n        {\n            X = Pos.Right(_mainWindow) - 12,\n            Y = Pos.Bottom(_mainWindow) - 3\n        };\n        cancelButton.Clicked += OnCancelClicked;\n\n        _mainWindow.Add(_titleLabel, _progressBar, _stepLabel, _contentFrame, prevButton, nextButton, cancelButton);\n    }\n\n    private void ShowCurrentStep()\n    {\n        if (_currentStep >= _steps.Count) return;\n\n        var step = _steps[_currentStep];\n        \n        // Update progress\n        _progressBar.Fraction = (float)_currentStep / (_steps.Count - 1);\n        _stepLabel.Text = $\"Step {_currentStep + 1} of {_steps.Count}: {step.Title}\";\n        _contentFrame.Title = step.Description;\n        \n        // Clear content frame\n        _contentFrame.RemoveAll();\n        \n        // Show step content\n        step.ShowContent(_contentFrame, _wizardData);\n        \n        _mainWindow.SetNeedsDisplay();\n    }\n\n    private void OnPreviousClicked()\n    {\n        if (_currentStep > 0)\n        {\n            _currentStep--;\n            ShowCurrentStep();\n        }\n    }\n\n    private void OnNextClicked()\n    {\n        if (_currentStep < _steps.Count - 1)\n        {\n            if (ValidateCurrentStep())\n            {\n                _currentStep++;\n                ShowCurrentStep();\n            }\n        }\n        else if (_currentStep == _steps.Count - 1)\n        {\n            // Finish setup\n            _ = Task.Run(async () => await CompleteSetupAsync());\n        }\n    }\n\n    private void OnCancelClicked()\n    {\n        var result = MessageBox.Query(\"Cancel Setup\", \"Are you sure you want to cancel the setup?\", \"Yes\", \"No\");\n        if (result == 0)\n        {\n            Application.RequestStop();\n        }\n    }\n\n    private bool ValidateCurrentStep()\n    {\n        var step = _steps[_currentStep];\n        return step.Validate(_wizardData);\n    }\n\n    private async Task CompleteSetupAsync()\n    {\n        try\n        {\n            Application.MainLoop.Invoke(() =>\n            {\n                var progressDialog = new Dialog(\"Completing Setup\", 60, 10);\n                var progressBar = new ProgressBar()\n                {\n                    X = 1,\n                    Y = 2,\n                    Width = Dim.Fill() - 2,\n                    Height = 1\n                };\n                var statusLabel = new Label(\"Initializing system...\")\n                {\n                    X = 1,\n                    Y = 4,\n                    Width = Dim.Fill() - 2\n                };\n                \n                progressDialog.Add(progressBar, statusLabel);\n                Application.Run(progressDialog);\n            });\n\n            // Update configuration\n            await UpdateConfigurationAsync();\n            \n            // Initialize system\n            var request = new InitializeSystemRequest\n            {\n                RootTenantName = _wizardData.TenantName,\n                TenantName = _wizardData.TenantName,\n                TenantDomain = _wizardData.TenantDomain,\n                TenantDescription = _wizardData.TenantDescription,\n                AdminEmail = _wizardData.AdminEmail,\n                AdminPassword = _wizardData.AdminPassword,\n                AdminFirstName = _wizardData.AdminFirstName,\n                AdminLastName = _wizardData.AdminLastName\n            };\n\n            var success = await _setupService.InitializeSystemAsync(request);\n            \n            _wizardData.IsCompleted = success;\n            \n            Application.MainLoop.Invoke(() =>\n            {\n                if (success)\n                {\n                    MessageBox.Query(\"Setup Complete\", \"✅ NotifyMaster has been successfully configured!\\n\\nYou can now start using the system.\", \"OK\");\n                }\n                else\n                {\n                    MessageBox.ErrorQuery(\"Setup Failed\", \"❌ Failed to complete setup. Please check the logs for details.\", \"OK\");\n                }\n                Application.RequestStop();\n            });\n        }\n        catch (Exception ex)\n        {\n            _logger.LogError(ex, \"Error completing setup\");\n            Application.MainLoop.Invoke(() =>\n            {\n                MessageBox.ErrorQuery(\"Setup Error\", $\"❌ Setup failed: {ex.Message}\", \"OK\");\n                Application.RequestStop();\n            });\n        }\n    }\n\n    private async Task UpdateConfigurationAsync()\n    {\n        // Update appsettings.json with new configuration\n        var configPath = Path.Combine(Directory.GetCurrentDirectory(), \"appsettings.json\");\n        \n        if (File.Exists(configPath))\n        {\n            var json = await File.ReadAllTextAsync(configPath);\n            var config = JsonSerializer.Deserialize<Dictionary<string, object>>(json) ?? new();\n            \n            // Update connection strings\n            if (!config.ContainsKey(\"ConnectionStrings\"))\n                config[\"ConnectionStrings\"] = new Dictionary<string, object>();\n                \n            var connectionStrings = (Dictionary<string, object>)config[\"ConnectionStrings\"];\n            connectionStrings[\"DefaultConnection\"] = _wizardData.DatabaseConnectionString;\n            connectionStrings[\"RedisConnection\"] = _wizardData.RedisConnectionString;\n            \n            var updatedJson = JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });\n            await File.WriteAllTextAsync(configPath, updatedJson);\n        }\n    }\n}\n\npublic class SetupWizardData\n{\n    public string DatabaseConnectionString { get; set; } = string.Empty;\n    public string RedisConnectionString { get; set; } = string.Empty;\n    public string TenantName { get; set; } = string.Empty;\n    public string TenantDomain { get; set; } = string.Empty;\n    public string TenantDescription { get; set; } = string.Empty;\n    public string AdminEmail { get; set; } = string.Empty;\n    public string AdminPassword { get; set; } = string.Empty;\n    public string AdminFirstName { get; set; } = string.Empty;\n    public string AdminLastName { get; set; } = string.Empty;\n    public bool IsCompleted { get; set; }\n}\n\npublic class SetupStep\n{\n    public string Name { get; }\n    public string Title { get; }\n    public string Description { get; }\n    public Action<FrameView, SetupWizardData> ShowContent { get; }\n    public Func<SetupWizardData, bool> Validate { get; }\n\n    public SetupStep(string name, string description, Action<FrameView, SetupWizardData> showContent, Func<SetupWizardData, bool>? validate = null)\n    {\n        Name = name;\n        Title = name;\n        Description = description;\n        ShowContent = showContent;\n        Validate = validate ?? (_ => true);\n    }\n}\n"}]}