// Global using statements for NotifyMasterApi project

// System namespaces
global using System;
global using System.Collections.Generic;
global using System.ComponentModel.DataAnnotations;
global using System.Linq;
global using System.Text;
global using System.Text.Json;
global using System.Threading;
global using System.Threading.Tasks;
// FastEndpoints
global using FastEndpoints;
// Hangfire
global using Hangfire;
// MediatR
global using MediatR;
// ASP.NET Core
global using Microsoft.AspNetCore.Authentication.JwtBearer;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Builder;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Mvc;
// Entity Framework
global using Microsoft.EntityFrameworkCore;
// Microsoft Extensions
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Hosting;
global using Microsoft.Extensions.Logging;
// JWT and Security
global using Microsoft.IdentityModel.Tokens;
// NotifyMaster.Core namespaces
global using NotifyMaster.Core.Extensions;
global using NotifyMaster.Core.Services;
// NotifyMaster.Database namespaces
global using NotifyMaster.Database;
// NotifyMasterApi namespaces
global using NotifyMasterApi.Infrastructure;
global using NotifyMasterApi.Services;
// PluginCore namespaces
global using PluginCore.Base;
global using PluginCore.Interfaces;
global using PluginCore.Models;
global using PluginCore.Services;
