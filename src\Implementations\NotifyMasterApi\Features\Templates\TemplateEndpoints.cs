namespace NotifyMasterApi.Features.Templates;

public class RenderTemplateRequest
{
    public string TemplateId { get; set; } = string.Empty;
    public Dictionary<string, object> Variables { get; set; } = new();
}

public class RenderTemplateEndpoint : Endpoint<RenderTemplateRequest, object>
{
    private readonly ITemplateRenderingService _templateRenderingService;
    private readonly ILogger<RenderTemplateEndpoint> _logger;

    public RenderTemplateEndpoint(ITemplateRenderingService templateRenderingService, ILogger<RenderTemplateEndpoint> logger)
    {
        _templateRenderingService = templateRenderingService;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/templates/render");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Render template";
            s.Description = "Render message content from template without sending";
            s.Responses[200] = "Template rendered successfully";
            s.Responses[400] = "Invalid template or variables";
            s.Responses[404] = "Template not found";
        });
        Tags("Templates");
    }

    public override async Task HandleAsync(RenderTemplateRequest req, CancellationToken ct)
    {
        try
        {
            var result = await _templateRenderingService.RenderTemplateWithValidationAsync(req.TemplateId, req.Variables);
            
            if (!result.Success)
            {
                await SendAsync(new
                {
                    Success = false,
                    Errors = result.Errors,
                    Warnings = result.Warnings
                }, 400, ct);
                return;
            }

            await SendOkAsync(new
            {
                Success = true,
                RenderedContent = result.RenderedContent,
                UsedVariables = result.UsedVariables,
                MissingVariables = result.MissingVariables,
                Warnings = result.Warnings,
                Timestamp = DateTime.UtcNow
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering template {TemplateId}", req.TemplateId);
            await SendErrorsAsync(500, ct);
        }
    }
}

public class ValidateTemplateRequest
{
    public string TemplateContent { get; set; } = string.Empty;
}

public class ValidateTemplateEndpoint : Endpoint<ValidateTemplateRequest, object>
{
    private readonly ITemplateRenderingService _templateRenderingService;

    public ValidateTemplateEndpoint(ITemplateRenderingService templateRenderingService)
    {
        _templateRenderingService = templateRenderingService;
    }

    public override void Configure()
    {
        Post("/api/templates/validate");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Validate template";
            s.Description = "Validate template syntax and extract variables";
            s.Responses[200] = "Template validation completed";
        });
        Tags("Templates");
    }

    public override async Task HandleAsync(ValidateTemplateRequest req, CancellationToken ct)
    {
        var isValid = await _templateRenderingService.ValidateTemplateAsync(req.TemplateContent);
        var variables = await _templateRenderingService.ExtractVariablesFromTemplateAsync(req.TemplateContent);

        await SendOkAsync(new
        {
            IsValid = isValid,
            Variables = variables,
            VariableCount = variables.Count,
            Timestamp = DateTime.UtcNow
        }, ct);
    }
}

public class ScheduleMessageRequest
{
    public string TenantId { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public object Payload { get; set; } = new();
    public DateTime? ScheduledFor { get; set; }
    public string? CronExpression { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ScheduleMessageEndpoint : Endpoint<ScheduleMessageRequest, object>
{
    private readonly ISchedulingService _schedulingService;
    private readonly ILogger<ScheduleMessageEndpoint> _logger;

    public ScheduleMessageEndpoint(ISchedulingService schedulingService, ILogger<ScheduleMessageEndpoint> logger)
    {
        _schedulingService = schedulingService;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/messages/schedule");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Schedule message";
            s.Description = "Schedule a message for future delivery with optional cron-style recurring schedule";
            s.Responses[200] = "Message scheduled successfully";
            s.Responses[400] = "Invalid request";
        });
        Tags("Scheduling");
    }

    public override async Task HandleAsync(ScheduleMessageRequest req, CancellationToken ct)
    {
        try
        {
            if (!req.ScheduledFor.HasValue && string.IsNullOrEmpty(req.CronExpression))
            {
                await SendAsync(new { Error = "Either ScheduledFor or CronExpression must be provided" }, 400, ct);
                return;
            }

            var scheduledMessage = new ScheduledMessage
            {
                TenantId = req.TenantId,
                Channel = req.Channel,
                Payload = JsonSerializer.Serialize(req.Payload),
                ScheduledFor = req.ScheduledFor ?? DateTime.UtcNow.AddMinutes(1),
                CronExpression = req.CronExpression,
                Metadata = req.Metadata
            };

            var messageId = await _schedulingService.ScheduleMessageAsync(scheduledMessage);

            await SendOkAsync(new
            {
                MessageId = messageId,
                Status = "Scheduled",
                ScheduledFor = scheduledMessage.ScheduledFor,
                CronExpression = scheduledMessage.CronExpression,
                Timestamp = DateTime.UtcNow
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling message");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetScheduledMessagesRequest
{
    public string? TenantId { get; set; }
}

public class GetScheduledMessagesEndpoint : Endpoint<GetScheduledMessagesRequest, object>
{
    private readonly ISchedulingService _schedulingService;

    public GetScheduledMessagesEndpoint(ISchedulingService schedulingService)
    {
        _schedulingService = schedulingService;
    }

    public override void Configure()
    {
        Get("/api/messages/scheduled");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get scheduled messages";
            s.Description = "Get list of scheduled messages with optional tenant filtering";
            s.Responses[200] = "Scheduled messages retrieved successfully";
        });
        Tags("Scheduling");
    }

    public override async Task HandleAsync(GetScheduledMessagesRequest req, CancellationToken ct)
    {
        var messages = await _schedulingService.GetScheduledMessagesAsync(req.TenantId);
        await SendOkAsync(new
        {
            Messages = messages,
            Count = messages.Count,
            Timestamp = DateTime.UtcNow
        }, ct);
    }
}

public class CancelScheduledMessageRequest
{
    public string MessageId { get; set; } = string.Empty;
}

public class CancelScheduledMessageEndpoint : Endpoint<CancelScheduledMessageRequest, object>
{
    private readonly ISchedulingService _schedulingService;

    public CancelScheduledMessageEndpoint(ISchedulingService schedulingService)
    {
        _schedulingService = schedulingService;
    }

    public override void Configure()
    {
        Delete("/api/messages/scheduled/{messageId}");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Cancel scheduled message";
            s.Description = "Cancel a scheduled message before it's sent";
            s.Responses[200] = "Message cancelled successfully";
            s.Responses[404] = "Message not found";
            s.Responses[400] = "Message cannot be cancelled";
        });
        Tags("Scheduling");
    }

    public override async Task HandleAsync(CancelScheduledMessageRequest req, CancellationToken ct)
    {
        var success = await _schedulingService.CancelScheduledMessageAsync(req.MessageId);
        
        if (!success)
        {
            await SendAsync(new { Error = "Message not found or cannot be cancelled" }, 400, ct);
            return;
        }

        await SendOkAsync(new
        {
            Success = true,
            Message = "Scheduled message cancelled",
            MessageId = req.MessageId,
            Timestamp = DateTime.UtcNow
        }, ct);
    }
}

public class CreateValidationRuleRequest
{
    public string TenantId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public string RuleType { get; set; } = string.Empty;
    public string RuleDefinition { get; set; } = string.Empty;
    public int Priority { get; set; } = 0;
}

public class CreateValidationRuleEndpoint : Endpoint<CreateValidationRuleRequest, object>
{
    private readonly IValidationService _validationService;
    private readonly ILogger<CreateValidationRuleEndpoint> _logger;

    public CreateValidationRuleEndpoint(IValidationService validationService, ILogger<CreateValidationRuleEndpoint> logger)
    {
        _validationService = validationService;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/validation/rules");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Create validation rule";
            s.Description = "Create a custom validation rule for message validation";
            s.Responses[200] = "Validation rule created successfully";
            s.Responses[400] = "Invalid request";
        });
        Tags("Validation");
    }

    public override async Task HandleAsync(CreateValidationRuleRequest req, CancellationToken ct)
    {
        try
        {
            var rule = new ValidationRule
            {
                TenantId = req.TenantId,
                Name = req.Name,
                Channel = req.Channel,
                RuleType = req.RuleType,
                RuleDefinition = req.RuleDefinition,
                Priority = req.Priority,
                IsActive = true
            };

            var createdRule = await _validationService.CreateValidationRuleAsync(rule);
            
            if (createdRule == null)
            {
                await SendAsync(new { Error = "Failed to create validation rule" }, 400, ct);
                return;
            }

            await SendOkAsync(new
            {
                Rule = createdRule,
                Message = "Validation rule created successfully",
                Timestamp = DateTime.UtcNow
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating validation rule");
            await SendErrorsAsync(500, ct);
        }
    }
}

public class GetValidationRulesRequest
{
    public string TenantId { get; set; } = string.Empty;
    public string? Channel { get; set; }
}

public class GetValidationRulesEndpoint : Endpoint<GetValidationRulesRequest, object>
{
    private readonly IValidationService _validationService;

    public GetValidationRulesEndpoint(IValidationService validationService)
    {
        _validationService = validationService;
    }

    public override void Configure()
    {
        Get("/api/validation/rules");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Get validation rules";
            s.Description = "Get validation rules for a tenant with optional channel filtering";
            s.Responses[200] = "Validation rules retrieved successfully";
        });
        Tags("Validation");
    }

    public override async Task HandleAsync(GetValidationRulesRequest req, CancellationToken ct)
    {
        var rules = await _validationService.GetValidationRulesAsync(req.TenantId, req.Channel);
        await SendOkAsync(new
        {
            Rules = rules,
            Count = rules.Count,
            Timestamp = DateTime.UtcNow
        }, ct);
    }
}
