{"version": 3, "targets": {"net9.0": {"AWSSDK.Core/4.0.0.13": {"type": "package", "compile": {"lib/net8.0/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.S3/4.0.3": {"type": "package", "dependencies": {"AWSSDK.Core": "[4.0.0.13, 5.0.0)"}, "compile": {"lib/net8.0/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}}, "Azure.Core/1.44.1": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.1.0", "System.Memory.Data": "6.0.0"}, "compile": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.11.4": {"type": "package", "dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}}, "Azure.Storage.Blobs/12.24.1": {"type": "package", "dependencies": {"Azure.Storage.Common": "12.23.0"}, "compile": {"lib/net8.0/Azure.Storage.Blobs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Azure.Storage.Blobs.dll": {"related": ".xml"}}}, "Azure.Storage.Common/12.23.0": {"type": "package", "dependencies": {"Azure.Core": "1.44.1", "System.IO.Hashing": "6.0.0"}, "compile": {"lib/net8.0/Azure.Storage.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Azure.Storage.Common.dll": {"related": ".xml"}}}, "BCrypt.Net-Next/4.0.3": {"type": "package", "compile": {"lib/net6.0/BCrypt.Net-Next.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"related": ".xml"}}}, "ColorHelper/1.8.1": {"type": "package", "compile": {"lib/netstandard2.0/ColorHelper.dll": {}}, "runtime": {"lib/netstandard2.0/ColorHelper.dll": {}}}, "Dapper/2.0.123": {"type": "package", "compile": {"lib/net5.0/Dapper.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Dapper.dll": {"related": ".xml"}}}, "FastEndpoints/6.2.0": {"type": "package", "dependencies": {"FastEndpoints.Attributes": "6.2.0", "FastEndpoints.Messaging.Core": "6.2.0", "FluentValidation": "12.0.0"}, "compile": {"lib/net9.0/FastEndpoints.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/FastEndpoints.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "FastEndpoints.Attributes/6.2.0": {"type": "package", "compile": {"lib/netstandard2.0/FastEndpoints.Attributes.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/FastEndpoints.Attributes.dll": {"related": ".xml"}}}, "FastEndpoints.Messaging.Core/6.2.0": {"type": "package", "compile": {"lib/netstandard2.1/FastEndpoints.Messaging.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/FastEndpoints.Messaging.Core.dll": {"related": ".xml"}}}, "FluentValidation/12.0.0": {"type": "package", "compile": {"lib/net8.0/FluentValidation.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/FluentValidation.dll": {"related": ".xml"}}}, "Hangfire.AspNetCore/1.8.20": {"type": "package", "dependencies": {"Hangfire.NetCore": "[1.8.20]"}, "compile": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Hangfire.Core/1.8.20": {"type": "package", "dependencies": {"Newtonsoft.Json": "11.0.1"}, "compile": {"lib/netstandard2.0/Hangfire.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Hangfire.Core.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/ca/Hangfire.Core.resources.dll": {"locale": "ca"}, "lib/netstandard2.0/de/Hangfire.Core.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Hangfire.Core.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fa/Hangfire.Core.resources.dll": {"locale": "fa"}, "lib/netstandard2.0/fr/Hangfire.Core.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/nb/Hangfire.Core.resources.dll": {"locale": "nb"}, "lib/netstandard2.0/nl/Hangfire.Core.resources.dll": {"locale": "nl"}, "lib/netstandard2.0/pt-BR/Hangfire.Core.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/pt-PT/Hangfire.Core.resources.dll": {"locale": "pt-PT"}, "lib/netstandard2.0/pt/Hangfire.Core.resources.dll": {"locale": "pt"}, "lib/netstandard2.0/sv/Hangfire.Core.resources.dll": {"locale": "sv"}, "lib/netstandard2.0/tr-TR/Hangfire.Core.resources.dll": {"locale": "tr-TR"}, "lib/netstandard2.0/zh-TW/Hangfire.Core.resources.dll": {"locale": "zh-TW"}, "lib/netstandard2.0/zh/Hangfire.Core.resources.dll": {"locale": "zh"}}}, "Hangfire.InMemory/1.0.0": {"type": "package", "dependencies": {"Hangfire.Core": "1.8.0"}, "compile": {"lib/netstandard2.0/Hangfire.InMemory.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Hangfire.InMemory.dll": {"related": ".xml"}}}, "Hangfire.NetCore/1.8.20": {"type": "package", "dependencies": {"Hangfire.Core": "[1.8.20]"}, "compile": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"related": ".xml"}}}, "Hangfire.PostgreSql/1.20.12": {"type": "package", "dependencies": {"Dapper": "2.0.123", "Hangfire.Core": "1.8.0", "Npgsql": "6.0.11"}, "compile": {"lib/netstandard2.0/Hangfire.PostgreSql.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Hangfire.PostgreSql.dll": {"related": ".xml"}}}, "Hangfire.SqlServer/1.8.20": {"type": "package", "dependencies": {"Hangfire.Core": "[1.8.20]"}, "compile": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"related": ".xml"}}}, "Humanizer.Core/2.14.1": {"type": "package", "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}}, "JetBrains.Annotations/2024.2.0": {"type": "package", "compile": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}}, "MediatR/12.5.0": {"type": "package", "dependencies": {"MediatR.Contracts": "[2.0.1, 3.0.0)"}, "compile": {"lib/net6.0/MediatR.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/MediatR.dll": {"related": ".xml"}}}, "MediatR.Contracts/2.0.1": {"type": "package", "compile": {"lib/netstandard2.0/MediatR.Contracts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.6": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.OpenApi/9.0.6": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.6.17"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.SignalR/1.2.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.SignalR.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Build.Tasks.Git/8.0.0": {"type": "package", "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.CodeAnalysis/4.10.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.CSharp.Workspaces": "[4.10.0]", "Microsoft.CodeAnalysis.VisualBasic.Workspaces": "[4.10.0]", "System.Composition": "8.0.0"}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "build": {"buildTransitive/Microsoft.CodeAnalysis.Analyzers.props": {}, "buildTransitive/Microsoft.CodeAnalysis.Analyzers.targets": {}}}, "Microsoft.CodeAnalysis.Common/4.10.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4"}, "compile": {"lib/net8.0/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net8.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.10.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.Common": "[4.10.0]"}, "compile": {"lib/net8.0/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net8.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.10.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.CSharp": "[4.10.0]", "Microsoft.CodeAnalysis.Common": "[4.10.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.10.0]", "System.Composition": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net8.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.VisualBasic/4.10.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.Common": "[4.10.0]"}, "compile": {"lib/net8.0/Microsoft.CodeAnalysis.VisualBasic.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.VisualBasic.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net8.0/cs/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.VisualBasic.Workspaces/4.10.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.Common": "[4.10.0]", "Microsoft.CodeAnalysis.VisualBasic": "[4.10.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.10.0]", "System.Composition": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net8.0/cs/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.10.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.Common": "[4.10.0]", "System.Composition": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net8.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Data.SqlClient/5.1.6": {"type": "package", "dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.1", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Runtime.Caching": "6.0.0"}, "compile": {"ref/net6.0/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"related": ".pdb;.xml"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"type": "package", "runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Data.Sqlite.Core/9.0.6": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore/9.0.6": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {"type": "package"}, "Microsoft.EntityFrameworkCore.InMemory/9.0.6": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.InMemory.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.InMemory.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.6": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.6": {"type": "package", "dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.6": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient": "5.1.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.StackExchangeRedis/9.0.6": {"type": "package", "dependencies": {"StackExchange.Redis": "2.7.27"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.StackExchangeRedis.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.StackExchangeRedis.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyModel/9.0.6": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/8.12.1": {"type": "package", "compile": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.12.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.12.1"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/8.12.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.12.1"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/8.12.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "8.12.1"}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.OpenApi/1.6.17": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Microsoft.SourceLink.Common/8.0.0": {"type": "package", "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.SourceLink.GitHub/8.0.0": {"type": "package", "dependencies": {"Microsoft.Build.Tasks.Git": "8.0.0", "Microsoft.SourceLink.Common": "8.0.0"}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Newtonsoft.Json/11.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Npgsql/9.0.3": {"type": "package", "compile": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Npgsql.dll": {"related": ".xml"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "[9.0.1, 10.0.0)", "Microsoft.EntityFrameworkCore.Relational": "[9.0.1, 10.0.0)", "Npgsql": "9.0.3"}, "compile": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"related": ".xml"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "compile": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}}, "Scalar.AspNetCore/2.5.3": {"type": "package", "compile": {"lib/net9.0/Scalar.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Scalar.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Serilog/4.2.0": {"type": "package", "compile": {"lib/net9.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.dll": {"related": ".xml"}}}, "Serilog.AspNetCore/9.0.0": {"type": "package", "dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Formatting.Compact": "3.0.0", "Serilog.Settings.Configuration": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0"}, "compile": {"lib/net9.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Serilog.Extensions.Hosting/9.0.0": {"type": "package", "dependencies": {"Serilog": "4.2.0", "Serilog.Extensions.Logging": "9.0.0"}, "compile": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}}, "Serilog.Extensions.Logging/9.0.0": {"type": "package", "dependencies": {"Serilog": "4.2.0"}, "compile": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}}, "Serilog.Formatting.Compact/3.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}}, "Serilog.Settings.Configuration/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.2.0"}, "compile": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/6.0.1-dev-00953": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/6.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "build": {"buildTransitive/net9.0/SQLitePCLRaw.lib.e_sqlite3.targets": {}}, "runtimeTargets": {"runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a": {"assetType": "native", "rid": "browser-wasm"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-armel"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-mips64"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-s390x"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-ppc64le"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-s390x"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x86"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x86"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}}, "StackExchange.Redis/2.7.27": {"type": "package", "dependencies": {"Pipelines.Sockets.Unofficial": "2.2.8"}, "compile": {"lib/net6.0/StackExchange.Redis.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"related": ".xml"}}}, "System.ClientModel/1.1.0": {"type": "package", "dependencies": {"System.Memory.Data": "1.0.2"}, "compile": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}}, "System.Composition/8.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "8.0.0", "System.Composition.Convention": "8.0.0", "System.Composition.Hosting": "8.0.0", "System.Composition.Runtime": "8.0.0", "System.Composition.TypedParts": "8.0.0"}, "compile": {"lib/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.AttributedModel/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Convention/8.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "8.0.0"}, "compile": {"lib/net8.0/System.Composition.Convention.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Composition.Convention.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Hosting/8.0.0": {"type": "package", "dependencies": {"System.Composition.Runtime": "8.0.0"}, "compile": {"lib/net8.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Runtime/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.TypedParts/8.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "8.0.0", "System.Composition.Hosting": "8.0.0", "System.Composition.Runtime": "8.0.0"}, "compile": {"lib/net8.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Drawing.Common/6.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IdentityModel.Tokens.Jwt/8.12.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.12.1", "Microsoft.IdentityModel.Tokens": "8.12.1"}, "compile": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO.Abstractions/21.0.22": {"type": "package", "dependencies": {"TestableIO.System.IO.Abstractions": "21.0.22", "TestableIO.System.IO.Abstractions.Wrappers": "21.0.22"}, "compile": {"lib/net8.0/System.IO.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Abstractions.dll": {"related": ".xml"}}}, "System.IO.Hashing/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.IO.Hashing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Hashing.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Memory.Data/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Memory.Data.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Runtime.Caching/6.0.0": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Permissions/6.0.0": {"type": "package", "dependencies": {"System.Windows.Extensions": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Json/8.0.5": {"type": "package", "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Windows.Extensions/6.0.0": {"type": "package", "dependencies": {"System.Drawing.Common": "6.0.0"}, "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "Terminal.Gui/2.0.0": {"type": "package", "dependencies": {"ColorHelper": "[1.8.1, 2.0.0)", "JetBrains.Annotations": "2024.2.0", "Microsoft.CodeAnalysis": "[4.10.0, 5.0.0)", "Microsoft.CodeAnalysis.CSharp": "[4.10.0, 5.0.0)", "Microsoft.CodeAnalysis.Common": "[4.10.0, 5.0.0)", "Microsoft.SourceLink.GitHub": "[8.0.0, 9.0.0)", "System.IO.Abstractions": "[21.0.22, 22.0.0)", "System.Text.Json": "[8.0.5, 9.0.0)", "Wcwidth": "[2.0.0, 3.0.0)"}, "compile": {"lib/net8.0/Terminal.Gui.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Terminal.Gui.dll": {"related": ".xml"}}, "resource": {"lib/net8.0/fr-FR/Terminal.Gui.resources.dll": {"locale": "fr-FR"}, "lib/net8.0/ja-JP/Terminal.Gui.resources.dll": {"locale": "ja-<PERSON>"}, "lib/net8.0/pt-PT/Terminal.Gui.resources.dll": {"locale": "pt-PT"}, "lib/net8.0/zh-Hans/Terminal.Gui.resources.dll": {"locale": "zh-Hans"}}}, "TestableIO.System.IO.Abstractions/21.0.22": {"type": "package", "compile": {"lib/net8.0/TestableIO.System.IO.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/TestableIO.System.IO.Abstractions.dll": {"related": ".xml"}}}, "TestableIO.System.IO.Abstractions.Wrappers/21.0.22": {"type": "package", "dependencies": {"TestableIO.System.IO.Abstractions": "21.0.22"}, "compile": {"lib/net8.0/TestableIO.System.IO.Abstractions.Wrappers.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/TestableIO.System.IO.Abstractions.Wrappers.dll": {"related": ".xml"}}}, "Wcwidth/2.0.0": {"type": "package", "compile": {"lib/net8.0/Wcwidth.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Wcwidth.dll": {"related": ".xml"}}}, "NotifyMaster.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"BCrypt.Net-Next": "4.0.3", "Hangfire.Core": "1.8.20", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.6", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.InMemory": "9.0.6", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.6", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.6", "NotifyMaster.Database": "1.0.0", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "PluginCore": "1.0.0", "System.IdentityModel.Tokens.Jwt": "8.12.1"}, "compile": {"bin/placeholder/NotifyMaster.Core.dll": {}}, "runtime": {"bin/placeholder/NotifyMaster.Core.dll": {}}}, "NotifyMaster.Database/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.EntityFrameworkCore.InMemory": "9.0.6", "NotifyMaster.Entities": "1.0.0", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "PluginCore": "1.0.0"}, "compile": {"bin/placeholder/NotifyMaster.Database.dll": {}}, "runtime": {"bin/placeholder/NotifyMaster.Database.dll": {}}}, "NotifyMaster.Entities/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6"}, "compile": {"bin/placeholder/NotifyMaster.Entities.dll": {}}, "runtime": {"bin/placeholder/NotifyMaster.Entities.dll": {}}}, "PluginCore/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "dependencies": {"BCrypt.Net-Next": "4.0.3", "Microsoft.IdentityModel.Tokens": "8.12.1", "System.IdentityModel.Tokens.Jwt": "8.12.1"}, "compile": {"bin/placeholder/PluginCore.dll": {}}, "runtime": {"bin/placeholder/PluginCore.dll": {}}}}}, "libraries": {"AWSSDK.Core/4.0.0.13": {"sha512": "AXofl5oANEHo9B61yi6rpu/JazjwqkuHY9JmJfdf8+FSmkFPbOmKoEb6kUgO2On8PCtBeUOEIfhZdzjJbxRAyg==", "type": "package", "path": "awssdk.core/4.0.0.13", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "awssdk.core.4.0.0.13.nupkg.sha512", "awssdk.core.nuspec", "images/AWSLogo.png", "lib/net472/AWSSDK.Core.dll", "lib/net472/AWSSDK.Core.pdb", "lib/net472/AWSSDK.Core.xml", "lib/net8.0/AWSSDK.Core.dll", "lib/net8.0/AWSSDK.Core.pdb", "lib/net8.0/AWSSDK.Core.xml", "lib/netcoreapp3.1/AWSSDK.Core.dll", "lib/netcoreapp3.1/AWSSDK.Core.pdb", "lib/netcoreapp3.1/AWSSDK.Core.xml", "lib/netstandard2.0/AWSSDK.Core.dll", "lib/netstandard2.0/AWSSDK.Core.pdb", "lib/netstandard2.0/AWSSDK.Core.xml", "tools/account-management.ps1"]}, "AWSSDK.S3/4.0.3": {"sha512": "xSl7a7q8sBcBftAzwLZDKo7V73xq8To58C9nO3T6yR5vD6guIfbi3K978iY1AZ1Q0hrR/bOkgtlhQbuih16O0Q==", "type": "package", "path": "awssdk.s3/4.0.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.S3.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.s3.4.0.3.nupkg.sha512", "awssdk.s3.nuspec", "images/AWSLogo.png", "lib/net472/AWSSDK.S3.dll", "lib/net472/AWSSDK.S3.pdb", "lib/net472/AWSSDK.S3.xml", "lib/net8.0/AWSSDK.S3.dll", "lib/net8.0/AWSSDK.S3.pdb", "lib/net8.0/AWSSDK.S3.xml", "lib/netcoreapp3.1/AWSSDK.S3.dll", "lib/netcoreapp3.1/AWSSDK.S3.pdb", "lib/netcoreapp3.1/AWSSDK.S3.xml", "lib/netstandard2.0/AWSSDK.S3.dll", "lib/netstandard2.0/AWSSDK.S3.pdb", "lib/netstandard2.0/AWSSDK.S3.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "Azure.Core/1.44.1": {"sha512": "YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "type": "package", "path": "azure.core/1.44.1", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.44.1.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Identity/1.11.4": {"sha512": "Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "type": "package", "path": "azure.identity/1.11.4", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.11.4.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "Azure.Storage.Blobs/12.24.1": {"sha512": "479Z9ps9yl9XyhU45bbU2CU4e2B23S6FJiSiL9LpfZHU6eNXXD9Jb6rYdwY+qqmm852RhqICXBpX3Sql4DLBew==", "type": "package", "path": "azure.storage.blobs/12.24.1", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.storage.blobs.12.24.1.nupkg.sha512", "azure.storage.blobs.nuspec", "azureicon.png", "lib/net6.0/Azure.Storage.Blobs.dll", "lib/net6.0/Azure.Storage.Blobs.xml", "lib/net8.0/Azure.Storage.Blobs.dll", "lib/net8.0/Azure.Storage.Blobs.xml", "lib/netstandard2.0/Azure.Storage.Blobs.dll", "lib/netstandard2.0/Azure.Storage.Blobs.xml", "lib/netstandard2.1/Azure.Storage.Blobs.dll", "lib/netstandard2.1/Azure.Storage.Blobs.xml"]}, "Azure.Storage.Common/12.23.0": {"sha512": "X/pe1LS3lC6s6MSL7A6FzRfnB6P72rNBt5oSuyan6Q4Jxr+KiN9Ufwqo32YLHOVfPcB8ESZZ4rBDketn+J37Rw==", "type": "package", "path": "azure.storage.common/12.23.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.storage.common.12.23.0.nupkg.sha512", "azure.storage.common.nuspec", "azureicon.png", "lib/net6.0/Azure.Storage.Common.dll", "lib/net6.0/Azure.Storage.Common.xml", "lib/net8.0/Azure.Storage.Common.dll", "lib/net8.0/Azure.Storage.Common.xml", "lib/netstandard2.0/Azure.Storage.Common.dll", "lib/netstandard2.0/Azure.Storage.Common.xml"]}, "BCrypt.Net-Next/4.0.3": {"sha512": "W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "type": "package", "path": "bcrypt.net-next/4.0.3", "files": [".nupkg.metadata", ".signature.p7s", "bcrypt.net-next.4.0.3.nupkg.sha512", "bcrypt.net-next.nuspec", "ico.png", "lib/net20/BCrypt.Net-Next.dll", "lib/net20/BCrypt.Net-Next.xml", "lib/net35/BCrypt.Net-Next.dll", "lib/net35/BCrypt.Net-Next.xml", "lib/net462/BCrypt.Net-Next.dll", "lib/net462/BCrypt.Net-Next.xml", "lib/net472/BCrypt.Net-Next.dll", "lib/net472/BCrypt.Net-Next.xml", "lib/net48/BCrypt.Net-Next.dll", "lib/net48/BCrypt.Net-Next.xml", "lib/net5.0/BCrypt.Net-Next.dll", "lib/net5.0/BCrypt.Net-Next.xml", "lib/net6.0/BCrypt.Net-Next.dll", "lib/net6.0/BCrypt.Net-Next.xml", "lib/netstandard2.0/BCrypt.Net-Next.dll", "lib/netstandard2.0/BCrypt.Net-Next.xml", "lib/netstandard2.1/BCrypt.Net-Next.dll", "lib/netstandard2.1/BCrypt.Net-Next.xml", "readme.md"]}, "ColorHelper/1.8.1": {"sha512": "rblWXd/02TfwJjX53xEfQnbnDRKxCm09VtavV8JaIcmIFWt9efNf6xTQ3WeXgy/she5R31eRTtnizFoUoDrs5w==", "type": "package", "path": "colorhelper/1.8.1", "files": [".nupkg.metadata", ".signature.p7s", "colorhelper.1.8.1.nupkg.sha512", "colorhelper.nuspec", "lib/netstandard2.0/ColorHelper.dll", "logo.jpg"]}, "Dapper/2.0.123": {"sha512": "RDFF4rBLLmbpi6pwkY7q/M6UXHRJEOerplDGE5jwEkP/JGJnBauAClYavNKJPW1yOTWRPIyfj4is3EaJxQXILQ==", "type": "package", "path": "dapper/2.0.123", "files": [".nupkg.metadata", ".signature.p7s", "Dapper.png", "dapper.2.0.123.nupkg.sha512", "dapper.nuspec", "lib/net461/Dapper.dll", "lib/net461/Dapper.xml", "lib/net5.0/Dapper.dll", "lib/net5.0/Dapper.xml", "lib/netstandard2.0/Dapper.dll", "lib/netstandard2.0/Dapper.xml"]}, "FastEndpoints/6.2.0": {"sha512": "KhWPR4sGhuj6/qBksmAIR60zQtVDYMR/HWSKqtZhDUN/gnhJpq9/9nCpKw7Cssq8rbw68AmVBFzLKWsfAe4dZQ==", "type": "package", "path": "fastendpoints/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fastendpoints.6.2.0.nupkg.sha512", "fastendpoints.nuspec", "icon.png", "lib/net10.0/FastEndpoints.dll", "lib/net10.0/FastEndpoints.xml", "lib/net8.0/FastEndpoints.dll", "lib/net8.0/FastEndpoints.xml", "lib/net9.0/FastEndpoints.dll", "lib/net9.0/FastEndpoints.xml"]}, "FastEndpoints.Attributes/6.2.0": {"sha512": "Z2tWmd7tA8cVuHl6WsHZVIhdI2Dy++SddEgswoefpoZjOFMtkORKIOYpGec/MyaMqQpCRm/zgWfXjFvZrm/Oag==", "type": "package", "path": "fastendpoints.attributes/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "fastendpoints.attributes.6.2.0.nupkg.sha512", "fastendpoints.attributes.nuspec", "icon.png", "lib/netstandard2.0/FastEndpoints.Attributes.dll", "lib/netstandard2.0/FastEndpoints.Attributes.xml"]}, "FastEndpoints.Messaging.Core/6.2.0": {"sha512": "ycdbVd1IedyVFUpthcVa6NEJ4J1/JjEFHdfVEsBtO4Qts5IBQuqcirbyx7GoogCwqrgQapru5sZ/9Dns/cLPkA==", "type": "package", "path": "fastendpoints.messaging.core/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "fastendpoints.messaging.core.6.2.0.nupkg.sha512", "fastendpoints.messaging.core.nuspec", "icon.png", "lib/netstandard2.1/FastEndpoints.Messaging.Core.dll", "lib/netstandard2.1/FastEndpoints.Messaging.Core.xml"]}, "FluentValidation/12.0.0": {"sha512": "8NVLxtMUXynRHJIX3Hn1ACovaqZIJASufXIIFkD0EUbcd5PmMsL1xUD5h548gCezJ5BzlITaR9CAMrGe29aWpA==", "type": "package", "path": "fluentvalidation/12.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.12.0.0.nupkg.sha512", "fluentvalidation.nuspec", "lib/net8.0/FluentValidation.dll", "lib/net8.0/FluentValidation.xml"]}, "Hangfire.AspNetCore/1.8.20": {"sha512": "OoVxOZanKlnpyAuCAYWKGztQHYw6GTQxQD/W3bfOfFsD+8fYp7FEDKbMEsrrMvAtF+gLiY1HNk9xYjUpNnSxGA==", "type": "package", "path": "hangfire.aspnetcore/1.8.20", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "hangfire.aspnetcore.1.8.20.nupkg.sha512", "hangfire.aspnetcore.nuspec", "icon.png", "lib/net451/Hangfire.AspNetCore.dll", "lib/net451/Hangfire.AspNetCore.xml", "lib/net461/Hangfire.AspNetCore.dll", "lib/net461/Hangfire.AspNetCore.xml", "lib/netcoreapp3.0/Hangfire.AspNetCore.dll", "lib/netcoreapp3.0/Hangfire.AspNetCore.xml", "lib/netstandard1.3/Hangfire.AspNetCore.dll", "lib/netstandard1.3/Hangfire.AspNetCore.xml", "lib/netstandard2.0/Hangfire.AspNetCore.dll", "lib/netstandard2.0/Hangfire.AspNetCore.xml"]}, "Hangfire.Core/1.8.20": {"sha512": "PSk0daUo3WCcnh89Bydj/xJ+M7GA+eR4nXXD5v/CIBOTCAx+oa3/DNjqLJPC9QHojsKXt0DO6u87aGxCQZ78Og==", "type": "package", "path": "hangfire.core/1.8.20", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "README.md", "hangfire.core.1.8.20.nupkg.sha512", "hangfire.core.nuspec", "icon.png", "lib/net451/Hangfire.Core.dll", "lib/net451/Hangfire.Core.xml", "lib/net451/ca/Hangfire.Core.resources.dll", "lib/net451/de/Hangfire.Core.resources.dll", "lib/net451/es/Hangfire.Core.resources.dll", "lib/net451/fa/Hangfire.Core.resources.dll", "lib/net451/fr/Hangfire.Core.resources.dll", "lib/net451/nb/Hangfire.Core.resources.dll", "lib/net451/nl/Hangfire.Core.resources.dll", "lib/net451/pt-BR/Hangfire.Core.resources.dll", "lib/net451/pt-PT/Hangfire.Core.resources.dll", "lib/net451/pt/Hangfire.Core.resources.dll", "lib/net451/sv/Hangfire.Core.resources.dll", "lib/net451/tr-TR/Hangfire.Core.resources.dll", "lib/net451/zh-TW/Hangfire.Core.resources.dll", "lib/net451/zh/Hangfire.Core.resources.dll", "lib/net46/Hangfire.Core.dll", "lib/net46/Hangfire.Core.xml", "lib/net46/ca/Hangfire.Core.resources.dll", "lib/net46/de/Hangfire.Core.resources.dll", "lib/net46/es/Hangfire.Core.resources.dll", "lib/net46/fa/Hangfire.Core.resources.dll", "lib/net46/fr/Hangfire.Core.resources.dll", "lib/net46/nb/Hangfire.Core.resources.dll", "lib/net46/nl/Hangfire.Core.resources.dll", "lib/net46/pt-BR/Hangfire.Core.resources.dll", "lib/net46/pt-PT/Hangfire.Core.resources.dll", "lib/net46/pt/Hangfire.Core.resources.dll", "lib/net46/sv/Hangfire.Core.resources.dll", "lib/net46/tr-TR/Hangfire.Core.resources.dll", "lib/net46/zh-TW/Hangfire.Core.resources.dll", "lib/net46/zh/Hangfire.Core.resources.dll", "lib/netstandard1.3/Hangfire.Core.dll", "lib/netstandard1.3/Hangfire.Core.xml", "lib/netstandard1.3/ca/Hangfire.Core.resources.dll", "lib/netstandard1.3/de/Hangfire.Core.resources.dll", "lib/netstandard1.3/es/Hangfire.Core.resources.dll", "lib/netstandard1.3/fa/Hangfire.Core.resources.dll", "lib/netstandard1.3/fr/Hangfire.Core.resources.dll", "lib/netstandard1.3/nb/Hangfire.Core.resources.dll", "lib/netstandard1.3/nl/Hangfire.Core.resources.dll", "lib/netstandard1.3/pt-BR/Hangfire.Core.resources.dll", "lib/netstandard1.3/pt-PT/Hangfire.Core.resources.dll", "lib/netstandard1.3/pt/Hangfire.Core.resources.dll", "lib/netstandard1.3/sv/Hangfire.Core.resources.dll", "lib/netstandard1.3/tr-TR/Hangfire.Core.resources.dll", "lib/netstandard1.3/zh-TW/Hangfire.Core.resources.dll", "lib/netstandard1.3/zh/Hangfire.Core.resources.dll", "lib/netstandard2.0/Hangfire.Core.dll", "lib/netstandard2.0/Hangfire.Core.xml", "lib/netstandard2.0/ca/Hangfire.Core.resources.dll", "lib/netstandard2.0/de/Hangfire.Core.resources.dll", "lib/netstandard2.0/es/Hangfire.Core.resources.dll", "lib/netstandard2.0/fa/Hangfire.Core.resources.dll", "lib/netstandard2.0/fr/Hangfire.Core.resources.dll", "lib/netstandard2.0/nb/Hangfire.Core.resources.dll", "lib/netstandard2.0/nl/Hangfire.Core.resources.dll", "lib/netstandard2.0/pt-BR/Hangfire.Core.resources.dll", "lib/netstandard2.0/pt-PT/Hangfire.Core.resources.dll", "lib/netstandard2.0/pt/Hangfire.Core.resources.dll", "lib/netstandard2.0/sv/Hangfire.Core.resources.dll", "lib/netstandard2.0/tr-TR/Hangfire.Core.resources.dll", "lib/netstandard2.0/zh-TW/Hangfire.Core.resources.dll", "lib/netstandard2.0/zh/Hangfire.Core.resources.dll"]}, "Hangfire.InMemory/1.0.0": {"sha512": "56H71lfcqn5sN/8Bjj9hOLGTG5HIERLRuMsRJTFpw0Tsq5ck5OUkNvtUw92s7bwD3PRKOo4PkDGqNs9KugaqoQ==", "type": "package", "path": "hangfire.inmemory/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "README.md", "hangfire.inmemory.1.0.0.nupkg.sha512", "hangfire.inmemory.nuspec", "icon.png", "lib/net451/Hangfire.InMemory.dll", "lib/net451/Hangfire.InMemory.xml", "lib/netstandard2.0/Hangfire.InMemory.dll", "lib/netstandard2.0/Hangfire.InMemory.xml"]}, "Hangfire.NetCore/1.8.20": {"sha512": "QCMoUaOokUsScJIyyo9SDVaAOpPIaIpBhGzN7M9GgZI9Kzetd7Y+hmFlQUTpROi7bcASIGoTOPnPoqdNA///Rw==", "type": "package", "path": "hangfire.netcore/1.8.20", "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "hangfire.netcore.1.8.20.nupkg.sha512", "hangfire.netcore.nuspec", "icon.png", "lib/net451/Hangfire.NetCore.dll", "lib/net451/Hangfire.NetCore.xml", "lib/net461/Hangfire.NetCore.dll", "lib/net461/Hangfire.NetCore.xml", "lib/netstandard1.3/Hangfire.NetCore.dll", "lib/netstandard1.3/Hangfire.NetCore.xml", "lib/netstandard2.0/Hangfire.NetCore.dll", "lib/netstandard2.0/Hangfire.NetCore.xml", "lib/netstandard2.1/Hangfire.NetCore.dll", "lib/netstandard2.1/Hangfire.NetCore.xml"]}, "Hangfire.PostgreSql/1.20.12": {"sha512": "KvozigeVgbYSinFmaj5qQWRaBG7ey/S73UP6VLIOPcP1UrZO0/6hSw4jN53TKpWP2UsiMuYONRmQbFDxGAi4ug==", "type": "package", "path": "hangfire.postgresql/1.20.12", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "hangfire.postgresql.1.20.12.nupkg.sha512", "hangfire.postgresql.nuspec", "lib/netstandard2.0/Hangfire.PostgreSql.dll", "lib/netstandard2.0/Hangfire.PostgreSql.xml"]}, "Hangfire.SqlServer/1.8.20": {"sha512": "O+0A2dEf+cbKnJQRxK5jF5c4aLDCOf2+KllYBwjdO82KRVKz0nox8wirWQP/AKW85f4NJ93ZKAEtAWOFk6DLxw==", "type": "package", "path": "hangfire.sqlserver/1.8.20", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "COPYING", "COPYING.LESSER", "LICENSE.md", "LICENSE_ROYALTYFREE", "LICENSE_STANDARD", "NOTICES", "hangfire.sqlserver.1.8.20.nupkg.sha512", "hangfire.sqlserver.nuspec", "icon.png", "lib/net451/Hangfire.SqlServer.dll", "lib/net451/Hangfire.SqlServer.xml", "lib/netstandard1.3/Hangfire.SqlServer.dll", "lib/netstandard1.3/Hangfire.SqlServer.xml", "lib/netstandard2.0/Hangfire.SqlServer.dll", "lib/netstandard2.0/Hangfire.SqlServer.xml", "tools/install.sql"]}, "Humanizer.Core/2.14.1": {"sha512": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "type": "package", "path": "humanizer.core/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.2.14.1.nupkg.sha512", "humanizer.core.nuspec", "lib/net6.0/Humanizer.dll", "lib/net6.0/Humanizer.xml", "lib/netstandard1.0/Humanizer.dll", "lib/netstandard1.0/Humanizer.xml", "lib/netstandard2.0/Humanizer.dll", "lib/netstandard2.0/Humanizer.xml", "logo.png"]}, "JetBrains.Annotations/2024.2.0": {"sha512": "GNnqCFW/163p1fOehKx0CnAqjmpPrUSqrgfHM6qca+P+RN39C9rhlfZHQpJhxmQG/dkOYe/b3Z0P8b6Kv5m1qw==", "type": "package", "path": "jetbrains.annotations/2024.2.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "jetbrains.annotations.2024.2.0.nupkg.sha512", "jetbrains.annotations.nuspec", "lib/JetBrains.Annotations.2024.2.0.snupkg", "lib/net20/JetBrains.Annotations.dll", "lib/net20/JetBrains.Annotations.xml", "lib/netstandard1.0/JetBrains.Annotations.deps.json", "lib/netstandard1.0/JetBrains.Annotations.dll", "lib/netstandard1.0/JetBrains.Annotations.xml", "lib/netstandard2.0/JetBrains.Annotations.deps.json", "lib/netstandard2.0/JetBrains.Annotations.dll", "lib/netstandard2.0/JetBrains.Annotations.xml", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.dll", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.xml"]}, "MediatR/12.5.0": {"sha512": "vqm2H8/nqL5NAJHPhsG1JOPwfkmbVrPyh4svdoRzu+uZh6Ex7PRoHBGsLYC0/RWCEJFqD1ohHNpteQvql9OktA==", "type": "package", "path": "mediatr/12.5.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "gradient_128x128.png", "lib/net6.0/MediatR.dll", "lib/net6.0/MediatR.xml", "lib/netstandard2.0/MediatR.dll", "lib/netstandard2.0/MediatR.xml", "mediatr.12.5.0.nupkg.sha512", "mediatr.nuspec"]}, "MediatR.Contracts/2.0.1": {"sha512": "FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "type": "package", "path": "mediatr.contracts/2.0.1", "files": [".nupkg.metadata", ".signature.p7s", "gradient_128x128.png", "lib/netstandard2.0/MediatR.Contracts.dll", "lib/netstandard2.0/MediatR.Contracts.xml", "mediatr.contracts.2.0.1.nupkg.sha512", "mediatr.contracts.nuspec"]}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.6": {"sha512": "nH1mrzr77pk+n1E5+A/0KlzkNhqy3LS3gUGEjJf0PQE6PZAc3pr8rLwUATcaJMr/12qsxHT+kcvRZMxc4bxFpA==", "type": "package", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll", "lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.xml", "microsoft.aspnetcore.authentication.jwtbearer.9.0.6.nupkg.sha512", "microsoft.aspnetcore.authentication.jwtbearer.nuspec"]}, "Microsoft.AspNetCore.OpenApi/9.0.6": {"sha512": "MOJ4DG1xd3NlWMYh+JdGNT9uvBtEk1XQU/FQlpNZFlAzM8t0oB5IimvnGlnK7jmyY4vQagLPB1xw1HjJ8CHrZg==", "type": "package", "path": "microsoft.aspnetcore.openapi/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0/Microsoft.AspNetCore.OpenApi.dll", "lib/net9.0/Microsoft.AspNetCore.OpenApi.xml", "microsoft.aspnetcore.openapi.9.0.6.nupkg.sha512", "microsoft.aspnetcore.openapi.nuspec"]}, "Microsoft.AspNetCore.SignalR/1.2.0": {"sha512": "XoCcsOTdtBiXyOzUtpbCl0IaqMOYjnr+6dbDxvUCFn7NR6bu7CwrlQ3oQzkltTwDZH0b6VEUN9wZPOYvPHi+Lg==", "type": "package", "path": "microsoft.aspnetcore.signalr/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.dll", "lib/netstandard2.0/Microsoft.AspNetCore.SignalR.xml", "microsoft.aspnetcore.signalr.1.2.0.nupkg.sha512", "microsoft.aspnetcore.signalr.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"sha512": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Build.Tasks.Git/8.0.0": {"sha512": "bZKfSIKJRXLTuSzLudMFte/8CempWjVamNUR5eHJizsy+iuOuO/k2gnh7W0dHJmYY0tBf+gUErfluCv5mySAOQ==", "type": "package", "path": "microsoft.build.tasks.git/8.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/Microsoft.Build.Tasks.Git.props", "build/Microsoft.Build.Tasks.Git.targets", "buildMultiTargeting/Microsoft.Build.Tasks.Git.props", "buildMultiTargeting/Microsoft.Build.Tasks.Git.targets", "microsoft.build.tasks.git.8.0.0.nupkg.sha512", "microsoft.build.tasks.git.nuspec", "tools/core/Microsoft.Build.Tasks.Git.dll", "tools/core/cs/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/de/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/es/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/fr/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/it/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/ja/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/ko/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/pl/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/pt-BR/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/ru/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/tr/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/zh-Hans/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/zh-Hant/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/Microsoft.Build.Tasks.Git.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/cs/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/de/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/es/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/fr/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/it/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/ja/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/ko/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/pl/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/pt-BR/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/ru/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/tr/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/zh-Hans/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/zh-Hant/Microsoft.Build.Tasks.Git.resources.dll"]}, "Microsoft.CodeAnalysis/4.10.0": {"sha512": "UcAcN8FmV9Xesj9XQKqpeJxyOqofb0fsgMn97gnTeSQINrmbxMe5j2NlhHGkVl2qEZ/rXQcRcGth8K4AXdbprQ==", "type": "package", "path": "microsoft.codeanalysis/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "microsoft.codeanalysis.4.10.0.nupkg.sha512", "microsoft.codeanalysis.nuspec"]}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"sha512": "AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "type": "package", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.txt", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "buildTransitive/Microsoft.CodeAnalysis.Analyzers.props", "buildTransitive/Microsoft.CodeAnalysis.Analyzers.targets", "buildTransitive/config/analysislevel_2_9_8_all.globalconfig", "buildTransitive/config/analysislevel_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_default.globalconfig", "buildTransitive/config/analysislevel_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevel_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_none.globalconfig", "buildTransitive/config/analysislevel_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevel_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_all.globalconfig", "buildTransitive/config/analysislevel_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_default.globalconfig", "buildTransitive/config/analysislevel_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_none.globalconfig", "buildTransitive/config/analysislevel_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_all.globalconfig", "buildTransitive/config/analysislevel_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_default.globalconfig", "buildTransitive/config/analysislevel_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_none.globalconfig", "buildTransitive/config/analysislevel_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_all.globalconfig", "buildTransitive/config/analysislevel_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_default.globalconfig", "buildTransitive/config/analysislevel_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_none.globalconfig", "buildTransitive/config/analysislevel_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_all.globalconfig", "buildTransitive/config/analysislevel_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_default.globalconfig", "buildTransitive/config/analysislevel_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_minimum.globalconfig", "buildTransitive/config/analysislevel_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_none.globalconfig", "buildTransitive/config/analysislevel_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_recommended.globalconfig", "buildTransitive/config/analysislevel_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_all.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_default.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_none.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_recommended_warnaserror.globalconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.Analyzers.md", "documentation/Microsoft.CodeAnalysis.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/CorrectnessRulesDefault/.editorconfig", "editorconfig/CorrectnessRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/LibraryRulesDefault/.editorconfig", "editorconfig/LibraryRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "microsoft.codeanalysis.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/CorrectnessRulesDefault.ruleset", "rulesets/CorrectnessRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/LibraryRulesDefault.ruleset", "rulesets/LibraryRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.Common/4.10.0": {"sha512": "7O4+dn0fNKykPpEB1i8/5EKzwD3fuu/shdbbnnsBmdiHMaBz6telOubDFwPwLQQ/PvOAWTFIWWTyAOmWvXRD2g==", "type": "package", "path": "microsoft.codeanalysis.common/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net7.0/Microsoft.CodeAnalysis.dll", "lib/net7.0/Microsoft.CodeAnalysis.pdb", "lib/net7.0/Microsoft.CodeAnalysis.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/Microsoft.CodeAnalysis.dll", "lib/net8.0/Microsoft.CodeAnalysis.pdb", "lib/net8.0/Microsoft.CodeAnalysis.xml", "lib/net8.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "microsoft.codeanalysis.common.4.10.0.nupkg.sha512", "microsoft.codeanalysis.common.nuspec"]}, "Microsoft.CodeAnalysis.CSharp/4.10.0": {"sha512": "iifqKy3KvCgPABHFbFlSxjEoE+OItZGuZ191NM/TWV750m1jMypr7BtrP65ET+OK2KNVupO8S8xCtxbNqw056A==", "type": "package", "path": "microsoft.codeanalysis.csharp/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/net8.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/net8.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/net8.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "microsoft.codeanalysis.csharp.4.10.0.nupkg.sha512", "microsoft.codeanalysis.csharp.nuspec"]}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.10.0": {"sha512": "s8qbD2i3zdol8QNcrCVw9URW71DUdg1UF0XCxxIaQoYbdpcKVy2DG127560psiqLEKxAEWA/DOFwL9CY2qGq1g==", "type": "package", "path": "microsoft.codeanalysis.csharp.workspaces/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/net8.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/net8.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/net8.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "microsoft.codeanalysis.csharp.workspaces.4.10.0.nupkg.sha512", "microsoft.codeanalysis.csharp.workspaces.nuspec"]}, "Microsoft.CodeAnalysis.VisualBasic/4.10.0": {"sha512": "rqdpLqrACQwhr7pr21OCEpmSZthdWF7TfimCH9IUt+FCXLfpqNTkgB7qAF2ypVJTT5sc+hY1IQWeDPjSyJ3REg==", "type": "package", "path": "microsoft.codeanalysis.visualbasic/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net7.0/Microsoft.CodeAnalysis.VisualBasic.dll", "lib/net7.0/Microsoft.CodeAnalysis.VisualBasic.pdb", "lib/net7.0/Microsoft.CodeAnalysis.VisualBasic.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/Microsoft.CodeAnalysis.VisualBasic.dll", "lib/net8.0/Microsoft.CodeAnalysis.VisualBasic.pdb", "lib/net8.0/Microsoft.CodeAnalysis.VisualBasic.xml", "lib/net8.0/cs/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/de/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/es/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/fr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/it/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/ja/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/ko/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/pl/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/ru/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/tr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.VisualBasic.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.VisualBasic.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.VisualBasic.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.resources.dll", "microsoft.codeanalysis.visualbasic.4.10.0.nupkg.sha512", "microsoft.codeanalysis.visualbasic.nuspec"]}, "Microsoft.CodeAnalysis.VisualBasic.Workspaces/4.10.0": {"sha512": "i0dtZ531kx7DiCBAzyrvEcYpK/tZQAJDmSKIQW3kENl5wPenOkQePvYNFZRrvOGzSgK5uZVs0Y3xW/B1ZCcQFA==", "type": "package", "path": "microsoft.codeanalysis.visualbasic.workspaces/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net7.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.dll", "lib/net7.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.pdb", "lib/net7.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.dll", "lib/net8.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.pdb", "lib/net8.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.xml", "lib/net8.0/cs/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/de/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/es/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/fr/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/it/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/ja/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/ko/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/pl/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/ru/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/tr/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.VisualBasic.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.VisualBasic.Workspaces.resources.dll", "microsoft.codeanalysis.visualbasic.workspaces.4.10.0.nupkg.sha512", "microsoft.codeanalysis.visualbasic.workspaces.nuspec"]}, "Microsoft.CodeAnalysis.Workspaces.Common/4.10.0": {"sha512": "lSMNGNeROSbxvbgzJyQfJpLJM0BFRrSgxYs4BZuZvpL8TuyUorEYa/HCJDcclhSRhr76LGiTT5lfLu5QFoFF6A==", "type": "package", "path": "microsoft.codeanalysis.workspaces.common/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/net8.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/net8.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/net8.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "microsoft.codeanalysis.workspaces.common.4.10.0.nupkg.sha512", "microsoft.codeanalysis.workspaces.common.nuspec"]}, "Microsoft.Data.SqlClient/5.1.6": {"sha512": "+pz7gIPh5ydsBcQvivt4R98PwJXer86fyQBBToIBLxZ5kuhW4N13Ijz87s9WpuPtF1vh4JesYCgpDPAOgkMhdg==", "type": "package", "path": "microsoft.data.sqlclient/5.1.6", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net462/Microsoft.Data.SqlClient.dll", "lib/net462/Microsoft.Data.SqlClient.pdb", "lib/net462/Microsoft.Data.SqlClient.xml", "lib/net462/de/Microsoft.Data.SqlClient.resources.dll", "lib/net462/es/Microsoft.Data.SqlClient.resources.dll", "lib/net462/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/it/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net6.0/Microsoft.Data.SqlClient.dll", "lib/net6.0/Microsoft.Data.SqlClient.pdb", "lib/net6.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.0/Microsoft.Data.SqlClient.xml", "lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "lib/netstandard2.1/Microsoft.Data.SqlClient.xml", "microsoft.data.sqlclient.5.1.6.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net462/Microsoft.Data.SqlClient.dll", "ref/net462/Microsoft.Data.SqlClient.pdb", "ref/net462/Microsoft.Data.SqlClient.xml", "ref/net6.0/Microsoft.Data.SqlClient.dll", "ref/net6.0/Microsoft.Data.SqlClient.pdb", "ref/net6.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.0/Microsoft.Data.SqlClient.dll", "ref/netstandard2.0/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.1/Microsoft.Data.SqlClient.dll", "ref/netstandard2.1/Microsoft.Data.SqlClient.pdb", "ref/netstandard2.1/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Data.SqlClient.pdb", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.1/Microsoft.Data.SqlClient.pdb"]}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"sha512": "wNGM5ZTQCa2blc9ikXQouybGiyMd6IHPVJvAlBEPtr6JepZEOYeDxGyprYvFVeOxlCXs7avridZQ0nYkHzQWCQ==", "type": "package", "path": "microsoft.data.sqlclient.sni.runtime/5.1.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "dotnet.png", "microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512", "microsoft.data.sqlclient.sni.runtime.nuspec", "runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll"]}, "Microsoft.Data.Sqlite.Core/9.0.6": {"sha512": "3auiudiViGzj1TidUdjuDqtP3+f6PBk4xdw6r9sBaTtkYoGc3AZn0cP8LgYZaLRnJBqY5bXRLB+qhjoB+iATzA==", "type": "package", "path": "microsoft.data.sqlite.core/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Data.Sqlite.dll", "lib/net6.0/Microsoft.Data.Sqlite.xml", "lib/net8.0/Microsoft.Data.Sqlite.dll", "lib/net8.0/Microsoft.Data.Sqlite.xml", "lib/netstandard2.0/Microsoft.Data.Sqlite.dll", "lib/netstandard2.0/Microsoft.Data.Sqlite.xml", "microsoft.data.sqlite.core.9.0.6.nupkg.sha512", "microsoft.data.sqlite.core.nuspec"]}, "Microsoft.EntityFrameworkCore/9.0.6": {"sha512": "r5hzM6Bhw4X3z28l5vmsaCPjk9VsQP4zaaY01THh1SAYjgTMVadYIvpNkCfmrv/Klks6aIf2A9eY7cpGZab/hg==", "type": "package", "path": "microsoft.entityframeworkcore/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.9.0.6.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"sha512": "7MkhPK8emb8hfOx/mFVvHuIHxQ+mH2YdlK4sFUXgsGlvR0A44vsmd2wcHavZOTTzaKhN+aFUVy3zmkztKmTo+A==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {"sha512": "VKggHNQC5FCn3/vooaIM/4aEjGmrmWm78IrdRLz9lLV0Rm9bVHEr/jiWApDkU0U9ec2xGAilvQqJ5mMX7QC2cw==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "docs/PACKAGE.md", "microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.InMemory/9.0.6": {"sha512": "y7cpyB/aYVi4VZiVoUcIA2vQLlypQv1wGmly47RwXvPX15S15mooBsxyVmw3BShBXbdnK5wxfslihIoXzplnGg==", "type": "package", "path": "microsoft.entityframeworkcore.inmemory/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.InMemory.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.InMemory.xml", "microsoft.entityframeworkcore.inmemory.9.0.6.nupkg.sha512", "microsoft.entityframeworkcore.inmemory.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"sha512": "Ht6OT17sYnO31Dx+hX72YHrc5kZt53g5napaw0FpyIekXCvb+gUVvufEG55Fa7taFm8ccy0Vzs+JVNR9NL0JlA==", "type": "package", "path": "microsoft.entityframeworkcore.relational/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.9.0.6.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.6": {"sha512": "bVSdfFrqIo3ZeQfWYYfnVVanP1GWghkdw+MnEmZJz7jUwtdPQpBKHr0BW9dMizPamzU+SMA1Qu4nXuRTlKVAGQ==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/_._", "microsoft.entityframeworkcore.sqlite.9.0.6.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.6": {"sha512": "xP+SvMDR/GZCDNXFw7z4WYbO2sYpECvht3+lqejg+Md8vLtURwTBvdsOUAnY4jBGmNFqHeh87hZSmUGmuxyqMA==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.xml", "microsoft.entityframeworkcore.sqlite.core.9.0.6.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.core.nuspec"]}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.6": {"sha512": "hr8vJSL1KXkXdgvNYY5peSygSZkoKQ+r6umXGMLoggBQ9NMbf0jo8p13Hy0biON2IS03ixOl0g4Mgw0hjgTksw==", "type": "package", "path": "microsoft.entityframeworkcore.sqlserver/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.xml", "microsoft.entityframeworkcore.sqlserver.9.0.6.nupkg.sha512", "microsoft.entityframeworkcore.sqlserver.nuspec"]}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"sha512": "qPW2d798tBPZcRmrlaBJqyChf2+0odDdE+0Lxvrr0ywkSNl1oNMK8AKrOfDwyXyjuLCv0ua7p6nrUExCeXhCcg==", "type": "package", "path": "microsoft.extensions.caching.memory/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net9.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net9.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.9.0.6.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.StackExchangeRedis/9.0.6": {"sha512": "gqKiiGz5JylPuIy6/jl/j0ufeNvdKTAKcp+Q487cxxtcDciBHSywNh7qrmygCnFCA2ytnJtjdgRZg0AzkCK2oQ==", "type": "package", "path": "microsoft.extensions.caching.stackexchangeredis/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Caching.StackExchangeRedis.dll", "lib/net462/Microsoft.Extensions.Caching.StackExchangeRedis.xml", "lib/net8.0/Microsoft.Extensions.Caching.StackExchangeRedis.dll", "lib/net8.0/Microsoft.Extensions.Caching.StackExchangeRedis.xml", "lib/net9.0/Microsoft.Extensions.Caching.StackExchangeRedis.dll", "lib/net9.0/Microsoft.Extensions.Caching.StackExchangeRedis.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.StackExchangeRedis.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.StackExchangeRedis.xml", "microsoft.extensions.caching.stackexchangeredis.9.0.6.nupkg.sha512", "microsoft.extensions.caching.stackexchangeredis.nuspec"]}, "Microsoft.Extensions.DependencyModel/9.0.6": {"sha512": "grVU1ixgMHp+kuhIgvEzhE73jXRY6XmxNBPWrotmbjB9AvJvkwHnIzm1JlOsPpyixFgnzreh/bFBMJAjveX+fQ==", "type": "package", "path": "microsoft.extensions.dependencymodel/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/net9.0/Microsoft.Extensions.DependencyModel.dll", "lib/net9.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.9.0.6.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Identity.Client/4.61.3": {"sha512": "naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "type": "package", "path": "microsoft.identity.client/4.61.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.Identity.Client.dll", "lib/net462/Microsoft.Identity.Client.xml", "lib/net6.0-android31.0/Microsoft.Identity.Client.dll", "lib/net6.0-android31.0/Microsoft.Identity.Client.xml", "lib/net6.0-ios15.4/Microsoft.Identity.Client.dll", "lib/net6.0-ios15.4/Microsoft.Identity.Client.xml", "lib/net6.0/Microsoft.Identity.Client.dll", "lib/net6.0/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.61.3.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"sha512": "PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "type": "package", "path": "microsoft.identity.client.extensions.msal/4.61.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.IdentityModel.Abstractions/8.12.1": {"sha512": "JzWhET0VOCORyJbqDc1Wtdl8Q/l+I1MjFB0I/Jko+Ma691JZll8X6o9XwZtUce8FkqGuV4uY4/V1808XZOpDVg==", "type": "package", "path": "microsoft.identitymodel.abstractions/8.12.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net9.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net9.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.8.12.1.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/8.12.1": {"sha512": "imi3xiRLzzKxN4m1aR9Z2X8GUmNsVH7GLA6AkwYStNnh3UzupFtHEEVk3GK1fCvnYdRbpnCGNYY6WQb9AfDAKg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/8.12.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.8.12.1.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/8.12.1": {"sha512": "39HjVkU7Voe2jRLmORRB5PoTmta1ZPKzUZCc6ldlNlLzdx+um0+fAnvfk05LUQPrNxpvb5ZoqF00SrNvyO2Fzg==", "type": "package", "path": "microsoft.identitymodel.logging/8.12.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/net9.0/Microsoft.IdentityModel.Logging.dll", "lib/net9.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.8.12.1.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/8.0.1": {"sha512": "uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "type": "package", "path": "microsoft.identitymodel.protocols/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.xml", "lib/net9.0/Microsoft.IdentityModel.Protocols.dll", "lib/net9.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"sha512": "AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/8.12.1": {"sha512": "DzgPEABn3eZmIk4lhov0QPcoHkIbnAfgkyDPM7uGuWDHeockR9DdqNCD9Zy30hPfExu5VhbOXn9oPRi+tFUhEQ==", "type": "package", "path": "microsoft.identitymodel.tokens/8.12.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/net9.0/Microsoft.IdentityModel.Tokens.dll", "lib/net9.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.8.12.1.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.OpenApi/1.6.17": {"sha512": "Le+kehlmrlQfuDFUt1zZ2dVwrhFQtKREdKBo+rexOwaCoYP0/qpgT9tLxCsZjsgR5Itk1UKPcbgO+FyaNid/bA==", "type": "package", "path": "microsoft.openapi/1.6.17", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.6.17.nupkg.sha512", "microsoft.openapi.nuspec"]}, "Microsoft.SourceLink.Common/8.0.0": {"sha512": "dk9JPxTCIevS75HyEQ0E4OVAFhB2N+V9ShCXf8Q6FkUQZDkgLI12y679Nym1YqsiSysuQskT7Z+6nUf3yab6Vw==", "type": "package", "path": "microsoft.sourcelink.common/8.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/InitializeSourceControlInformation.targets", "build/Microsoft.SourceLink.Common.props", "build/Microsoft.SourceLink.Common.targets", "buildMultiTargeting/Microsoft.SourceLink.Common.props", "buildMultiTargeting/Microsoft.SourceLink.Common.targets", "microsoft.sourcelink.common.8.0.0.nupkg.sha512", "microsoft.sourcelink.common.nuspec", "tools/core/Microsoft.SourceLink.Common.dll", "tools/core/cs/Microsoft.SourceLink.Common.resources.dll", "tools/core/de/Microsoft.SourceLink.Common.resources.dll", "tools/core/es/Microsoft.SourceLink.Common.resources.dll", "tools/core/fr/Microsoft.SourceLink.Common.resources.dll", "tools/core/it/Microsoft.SourceLink.Common.resources.dll", "tools/core/ja/Microsoft.SourceLink.Common.resources.dll", "tools/core/ko/Microsoft.SourceLink.Common.resources.dll", "tools/core/pl/Microsoft.SourceLink.Common.resources.dll", "tools/core/pt-BR/Microsoft.SourceLink.Common.resources.dll", "tools/core/ru/Microsoft.SourceLink.Common.resources.dll", "tools/core/tr/Microsoft.SourceLink.Common.resources.dll", "tools/core/zh-Hans/Microsoft.SourceLink.Common.resources.dll", "tools/core/zh-Hant/Microsoft.SourceLink.Common.resources.dll", "tools/net472/Microsoft.SourceLink.Common.dll", "tools/net472/cs/Microsoft.SourceLink.Common.resources.dll", "tools/net472/de/Microsoft.SourceLink.Common.resources.dll", "tools/net472/es/Microsoft.SourceLink.Common.resources.dll", "tools/net472/fr/Microsoft.SourceLink.Common.resources.dll", "tools/net472/it/Microsoft.SourceLink.Common.resources.dll", "tools/net472/ja/Microsoft.SourceLink.Common.resources.dll", "tools/net472/ko/Microsoft.SourceLink.Common.resources.dll", "tools/net472/pl/Microsoft.SourceLink.Common.resources.dll", "tools/net472/pt-BR/Microsoft.SourceLink.Common.resources.dll", "tools/net472/ru/Microsoft.SourceLink.Common.resources.dll", "tools/net472/tr/Microsoft.SourceLink.Common.resources.dll", "tools/net472/zh-<PERSON>/Microsoft.SourceLink.Common.resources.dll", "tools/net472/zh-Hant/Microsoft.SourceLink.Common.resources.dll"]}, "Microsoft.SourceLink.GitHub/8.0.0": {"sha512": "G5q7OqtwIyGTkeIOAc3u2ZuV/kicQaec5EaRnc0pIeSnh9LUjj+PYQrJYBURvDt7twGl2PKA7nSN0kz1Zw5bnQ==", "type": "package", "path": "microsoft.sourcelink.github/8.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/Microsoft.SourceLink.GitHub.props", "build/Microsoft.SourceLink.GitHub.targets", "buildMultiTargeting/Microsoft.SourceLink.GitHub.props", "buildMultiTargeting/Microsoft.SourceLink.GitHub.targets", "microsoft.sourcelink.github.8.0.0.nupkg.sha512", "microsoft.sourcelink.github.nuspec", "tools/core/Microsoft.SourceLink.GitHub.dll", "tools/core/cs/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/de/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/es/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/fr/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/it/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/ja/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/ko/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/pl/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/pt-BR/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/ru/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/tr/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/zh-Hans/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/zh-Hant/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/Microsoft.SourceLink.GitHub.dll", "tools/net472/cs/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/de/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/es/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/fr/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/it/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/ja/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/ko/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/pl/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/pt-BR/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/ru/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/tr/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/zh-<PERSON>/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/zh-Hant/Microsoft.SourceLink.GitHub.resources.dll"]}, "Microsoft.SqlServer.Server/1.0.0": {"sha512": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "type": "package", "path": "microsoft.sqlserver.server/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net46/Microsoft.SqlServer.Server.dll", "lib/net46/Microsoft.SqlServer.Server.pdb", "lib/net46/Microsoft.SqlServer.Server.xml", "lib/netstandard2.0/Microsoft.SqlServer.Server.dll", "lib/netstandard2.0/Microsoft.SqlServer.Server.pdb", "lib/netstandard2.0/Microsoft.SqlServer.Server.xml", "microsoft.sqlserver.server.1.0.0.nupkg.sha512", "microsoft.sqlserver.server.nuspec"]}, "Microsoft.Win32.SystemEvents/6.0.0": {"sha512": "hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "type": "package", "path": "microsoft.win32.systemevents/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Win32.SystemEvents.dll", "lib/net461/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.6.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Newtonsoft.Json/11.0.1": {"sha512": "pNN4l+J6LlpIvHOeNdXlwxv39NPJ2B5klz+Rd2UQZIx30Squ5oND1Yy3wEAUoKn0GPUj6Yxt9lxlYWQqfZcvKg==", "type": "package", "path": "newtonsoft.json/11.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.xml", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.xml", "newtonsoft.json.11.0.1.nupkg.sha512", "newtonsoft.json.nuspec"]}, "Npgsql/9.0.3": {"sha512": "tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "type": "package", "path": "npgsql/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Npgsql.dll", "lib/net6.0/Npgsql.xml", "lib/net8.0/Npgsql.dll", "lib/net8.0/Npgsql.xml", "npgsql.9.0.3.nupkg.sha512", "npgsql.nuspec", "postgresql.png"]}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"sha512": "mw5vcY2IEc7L+IeGrxpp/J5OSnCcjkjAgJYCm/eD52wpZze8zsSifdqV7zXslSMmfJG2iIUGZyo3KuDtEFKwMQ==", "type": "package", "path": "npgsql.entityframeworkcore.postgresql/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll", "lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.xml", "npgsql.entityframeworkcore.postgresql.9.0.4.nupkg.sha512", "npgsql.entityframeworkcore.postgresql.nuspec", "postgresql.png"]}, "Pipelines.Sockets.Unofficial/2.2.8": {"sha512": "zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "type": "package", "path": "pipelines.sockets.unofficial/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Pipelines.Sockets.Unofficial.dll", "lib/net461/Pipelines.Sockets.Unofficial.xml", "lib/net472/Pipelines.Sockets.Unofficial.dll", "lib/net472/Pipelines.Sockets.Unofficial.xml", "lib/net5.0/Pipelines.Sockets.Unofficial.dll", "lib/net5.0/Pipelines.Sockets.Unofficial.xml", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.dll", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.xml", "pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "pipelines.sockets.unofficial.nuspec"]}, "Scalar.AspNetCore/2.5.3": {"sha512": "GhMaITeqvKVDLH/RyAH93n1uSkrP/JNI7cRIPUr5bsHjSiVSmhM68Ni+0x1IORnU5O68YiZJX2DcdGbLuEe9aQ==", "type": "package", "path": "scalar.aspnetcore/2.5.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net8.0/Scalar.AspNetCore.dll", "lib/net8.0/Scalar.AspNetCore.xml", "lib/net9.0/Scalar.AspNetCore.dll", "lib/net9.0/Scalar.AspNetCore.xml", "scalar.aspnetcore.2.5.3.nupkg.sha512", "scalar.aspnetcore.nuspec"]}, "Serilog/4.2.0": {"sha512": "gmoWVOvKgbME8TYR+gwMf7osROiWAURterc6Rt2dQyX7wtjZYpqFiA/pY6ztjGQKKV62GGCyOcmtP1UKMHgSmA==", "type": "package", "path": "serilog/4.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.dll", "lib/net462/Serilog.xml", "lib/net471/Serilog.dll", "lib/net471/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/net8.0/Serilog.dll", "lib/net8.0/Serilog.xml", "lib/net9.0/Serilog.dll", "lib/net9.0/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "serilog.4.2.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.AspNetCore/9.0.0": {"sha512": "JslDajPlBsn3Pww1554flJFTqROvK9zz9jONNQgn0D8Lx2Trw8L0A8/n6zEQK1DAZWXrJwiVLw8cnTR3YFuYsg==", "type": "package", "path": "serilog.aspnetcore/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.AspNetCore.dll", "lib/net462/Serilog.AspNetCore.xml", "lib/net8.0/Serilog.AspNetCore.dll", "lib/net8.0/Serilog.AspNetCore.xml", "lib/net9.0/Serilog.AspNetCore.dll", "lib/net9.0/Serilog.AspNetCore.xml", "lib/netstandard2.0/Serilog.AspNetCore.dll", "lib/netstandard2.0/Serilog.AspNetCore.xml", "lib/netstandard2.1/Serilog.AspNetCore.dll", "lib/netstandard2.1/Serilog.AspNetCore.xml", "serilog.aspnetcore.9.0.0.nupkg.sha512", "serilog.aspnetcore.nuspec"]}, "Serilog.Extensions.Hosting/9.0.0": {"sha512": "u2TRxuxbjvTAldQn7uaAwePkWxTHIqlgjelekBtilAGL5sYyF3+65NWctN4UrwwGLsDC7c3Vz3HnOlu+PcoxXg==", "type": "package", "path": "serilog.extensions.hosting/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Extensions.Hosting.dll", "lib/net462/Serilog.Extensions.Hosting.xml", "lib/net8.0/Serilog.Extensions.Hosting.dll", "lib/net8.0/Serilog.Extensions.Hosting.xml", "lib/net9.0/Serilog.Extensions.Hosting.dll", "lib/net9.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.0/Serilog.Extensions.Hosting.dll", "lib/netstandard2.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.1/Serilog.Extensions.Hosting.dll", "lib/netstandard2.1/Serilog.Extensions.Hosting.xml", "serilog.extensions.hosting.9.0.0.nupkg.sha512", "serilog.extensions.hosting.nuspec"]}, "Serilog.Extensions.Logging/9.0.0": {"sha512": "NwSSYqPJeKNzl5AuXVHpGbr6PkZJFlNa14CdIebVjK3k/76kYj/mz5kiTRNVSsSaxM8kAIa1kpy/qyT9E4npRQ==", "type": "package", "path": "serilog.extensions.logging/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Extensions.Logging.dll", "lib/net462/Serilog.Extensions.Logging.xml", "lib/net8.0/Serilog.Extensions.Logging.dll", "lib/net8.0/Serilog.Extensions.Logging.xml", "lib/net9.0/Serilog.Extensions.Logging.dll", "lib/net9.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.0/Serilog.Extensions.Logging.dll", "lib/netstandard2.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.1/Serilog.Extensions.Logging.dll", "lib/netstandard2.1/Serilog.Extensions.Logging.xml", "serilog-extension-nuget.png", "serilog.extensions.logging.9.0.0.nupkg.sha512", "serilog.extensions.logging.nuspec"]}, "Serilog.Formatting.Compact/3.0.0": {"sha512": "wQsv14w9cqlfB5FX2MZpNsTawckN4a8dryuNGbebB/3Nh1pXnROHZov3swtu3Nj5oNG7Ba+xdu7Et/ulAUPanQ==", "type": "package", "path": "serilog.formatting.compact/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Formatting.Compact.dll", "lib/net462/Serilog.Formatting.Compact.xml", "lib/net471/Serilog.Formatting.Compact.dll", "lib/net471/Serilog.Formatting.Compact.xml", "lib/net6.0/Serilog.Formatting.Compact.dll", "lib/net6.0/Serilog.Formatting.Compact.xml", "lib/net8.0/Serilog.Formatting.Compact.dll", "lib/net8.0/Serilog.Formatting.Compact.xml", "lib/netstandard2.0/Serilog.Formatting.Compact.dll", "lib/netstandard2.0/Serilog.Formatting.Compact.xml", "serilog-extension-nuget.png", "serilog.formatting.compact.3.0.0.nupkg.sha512", "serilog.formatting.compact.nuspec"]}, "Serilog.Settings.Configuration/9.0.0": {"sha512": "4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "type": "package", "path": "serilog.settings.configuration/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Settings.Configuration.dll", "lib/net462/Serilog.Settings.Configuration.xml", "lib/net8.0/Serilog.Settings.Configuration.dll", "lib/net8.0/Serilog.Settings.Configuration.xml", "lib/net9.0/Serilog.Settings.Configuration.dll", "lib/net9.0/Serilog.Settings.Configuration.xml", "lib/netstandard2.0/Serilog.Settings.Configuration.dll", "lib/netstandard2.0/Serilog.Settings.Configuration.xml", "serilog.settings.configuration.9.0.0.nupkg.sha512", "serilog.settings.configuration.nuspec"]}, "Serilog.Sinks.Console/6.0.1-dev-00953": {"sha512": "Goi2B0Je0X0NvWYUi0SiU9MJNF2957Kfjmc6VPZ2hNl6Lmj9he6laxmDuQU/c0fBdAFnNiEUPPcHd/NJVyfbkA==", "type": "package", "path": "serilog.sinks.console/6.0.1-dev-00953", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Console.dll", "lib/net462/Serilog.Sinks.Console.xml", "lib/net471/Serilog.Sinks.Console.dll", "lib/net471/Serilog.Sinks.Console.xml", "lib/net6.0/Serilog.Sinks.Console.dll", "lib/net6.0/Serilog.Sinks.Console.xml", "lib/net8.0/Serilog.Sinks.Console.dll", "lib/net8.0/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.6.0.1-dev-00953.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.Debug/3.0.0": {"sha512": "4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "type": "package", "path": "serilog.sinks.debug/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Debug.dll", "lib/net462/Serilog.Sinks.Debug.xml", "lib/net471/Serilog.Sinks.Debug.dll", "lib/net471/Serilog.Sinks.Debug.xml", "lib/net6.0/Serilog.Sinks.Debug.dll", "lib/net6.0/Serilog.Sinks.Debug.xml", "lib/net8.0/Serilog.Sinks.Debug.dll", "lib/net8.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.0/Serilog.Sinks.Debug.dll", "lib/netstandard2.0/Serilog.Sinks.Debug.xml", "serilog.sinks.debug.3.0.0.nupkg.sha512", "serilog.sinks.debug.nuspec"]}, "Serilog.Sinks.File/6.0.0": {"sha512": "lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "type": "package", "path": "serilog.sinks.file/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Sinks.File.dll", "lib/net462/Serilog.Sinks.File.xml", "lib/net471/Serilog.Sinks.File.dll", "lib/net471/Serilog.Sinks.File.xml", "lib/net6.0/Serilog.Sinks.File.dll", "lib/net6.0/Serilog.Sinks.File.xml", "lib/net8.0/Serilog.Sinks.File.dll", "lib/net8.0/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.xml", "serilog-sink-nuget.png", "serilog.sinks.file.6.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"sha512": "UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "type": "package", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid90/SQLitePCLRaw.batteries_v2.dll", "lib/net461/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.xml", "lib/net6.0-ios14.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-ios14.2/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-tvos10.0/SQLitePCLRaw.batteries_v2.dll", "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll", "lib/xamarinios10/SQLitePCLRaw.batteries_v2.dll", "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.bundle_e_sqlite3.nuspec"]}, "SQLitePCLRaw.core/2.1.10": {"sha512": "Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "type": "package", "path": "sqlitepclraw.core/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SQLitePCLRaw.core.dll", "sqlitepclraw.core.2.1.10.nupkg.sha512", "sqlitepclraw.core.nuspec"]}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"sha512": "mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "type": "package", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/net461/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net6.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net7.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net8.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net9.0/SQLitePCLRaw.lib.e_sqlite3.targets", "lib/net461/_._", "lib/netstandard2.0/_._", "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net7.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a", "runtimes/linux-arm/native/libe_sqlite3.so", "runtimes/linux-arm64/native/libe_sqlite3.so", "runtimes/linux-armel/native/libe_sqlite3.so", "runtimes/linux-mips64/native/libe_sqlite3.so", "runtimes/linux-musl-arm/native/libe_sqlite3.so", "runtimes/linux-musl-arm64/native/libe_sqlite3.so", "runtimes/linux-musl-s390x/native/libe_sqlite3.so", "runtimes/linux-musl-x64/native/libe_sqlite3.so", "runtimes/linux-ppc64le/native/libe_sqlite3.so", "runtimes/linux-s390x/native/libe_sqlite3.so", "runtimes/linux-x64/native/libe_sqlite3.so", "runtimes/linux-x86/native/libe_sqlite3.so", "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib", "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib", "runtimes/osx-arm64/native/libe_sqlite3.dylib", "runtimes/osx-x64/native/libe_sqlite3.dylib", "runtimes/win-arm/native/e_sqlite3.dll", "runtimes/win-arm64/native/e_sqlite3.dll", "runtimes/win-x64/native/e_sqlite3.dll", "runtimes/win-x86/native/e_sqlite3.dll", "runtimes/win10-arm/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-arm64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x86/nativeassets/uap10.0/e_sqlite3.dll", "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.lib.e_sqlite3.nuspec"]}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"sha512": "uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "type": "package", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/netstandard2.0/SQLitePCLRaw.provider.e_sqlite3.dll", "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.provider.e_sqlite3.nuspec"]}, "StackExchange.Redis/2.7.27": {"sha512": "Uqc2OQHglqj9/FfGQ6RkKFkZfHySfZlfmbCl+hc+u2I/IqunfelQ7QJi7ZhvAJxUtu80pildVX6NPLdDaUffOw==", "type": "package", "path": "stackexchange.redis/2.7.27", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/StackExchange.Redis.dll", "lib/net461/StackExchange.Redis.xml", "lib/net472/StackExchange.Redis.dll", "lib/net472/StackExchange.Redis.xml", "lib/net6.0/StackExchange.Redis.dll", "lib/net6.0/StackExchange.Redis.xml", "lib/netcoreapp3.1/StackExchange.Redis.dll", "lib/netcoreapp3.1/StackExchange.Redis.xml", "lib/netstandard2.0/StackExchange.Redis.dll", "lib/netstandard2.0/StackExchange.Redis.xml", "stackexchange.redis.2.7.27.nupkg.sha512", "stackexchange.redis.nuspec"]}, "System.ClientModel/1.1.0": {"sha512": "UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "type": "package", "path": "system.clientmodel/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net6.0/System.ClientModel.dll", "lib/net6.0/System.ClientModel.xml", "lib/netstandard2.0/System.ClientModel.dll", "lib/netstandard2.0/System.ClientModel.xml", "system.clientmodel.1.1.0.nupkg.sha512", "system.clientmodel.nuspec"]}, "System.Composition/8.0.0": {"sha512": "E9oO9olNNxA39J8CxQwf7ceIPm+j/B/PhYpyK9M4LhN/OLLRw6u5fNInkhVqaWueMB9iXxYqnwqwgz+W91loIA==", "type": "package", "path": "system.composition/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.targets", "lib/net461/_._", "lib/netcoreapp2.0/_._", "lib/netstandard2.0/_._", "system.composition.8.0.0.nupkg.sha512", "system.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.AttributedModel/8.0.0": {"sha512": "NyElSuvmBMYdn2iPG0n29i7Igu0bq99izOP3MAtEwskY3OP9jqsavvVmPn9lesVaj/KT/o/QkNjA43dOJTsDQw==", "type": "package", "path": "system.composition.attributedmodel/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.AttributedModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.AttributedModel.targets", "lib/net462/System.Composition.AttributedModel.dll", "lib/net462/System.Composition.AttributedModel.xml", "lib/net6.0/System.Composition.AttributedModel.dll", "lib/net6.0/System.Composition.AttributedModel.xml", "lib/net7.0/System.Composition.AttributedModel.dll", "lib/net7.0/System.Composition.AttributedModel.xml", "lib/net8.0/System.Composition.AttributedModel.dll", "lib/net8.0/System.Composition.AttributedModel.xml", "lib/netstandard2.0/System.Composition.AttributedModel.dll", "lib/netstandard2.0/System.Composition.AttributedModel.xml", "system.composition.attributedmodel.8.0.0.nupkg.sha512", "system.composition.attributedmodel.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Convention/8.0.0": {"sha512": "UuVkc1B3vQU/LzEbWLMZ1aYVssv4rpShzf8wPEyrUqoGNqdYKREmB8bXR73heOMKkwS6ZnPz3PjGODT2MenukQ==", "type": "package", "path": "system.composition.convention/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Convention.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Convention.targets", "lib/net462/System.Composition.Convention.dll", "lib/net462/System.Composition.Convention.xml", "lib/net6.0/System.Composition.Convention.dll", "lib/net6.0/System.Composition.Convention.xml", "lib/net7.0/System.Composition.Convention.dll", "lib/net7.0/System.Composition.Convention.xml", "lib/net8.0/System.Composition.Convention.dll", "lib/net8.0/System.Composition.Convention.xml", "lib/netstandard2.0/System.Composition.Convention.dll", "lib/netstandard2.0/System.Composition.Convention.xml", "system.composition.convention.8.0.0.nupkg.sha512", "system.composition.convention.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Hosting/8.0.0": {"sha512": "qwbONqoxlazxcbiohvb3t1JWZgKIKcRdXS5uEeLbo5wtuBupIbAvdC3PYTAeBCZrZeERvrtAbhYHuuS43Zr1bQ==", "type": "package", "path": "system.composition.hosting/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Hosting.targets", "lib/net462/System.Composition.Hosting.dll", "lib/net462/System.Composition.Hosting.xml", "lib/net6.0/System.Composition.Hosting.dll", "lib/net6.0/System.Composition.Hosting.xml", "lib/net7.0/System.Composition.Hosting.dll", "lib/net7.0/System.Composition.Hosting.xml", "lib/net8.0/System.Composition.Hosting.dll", "lib/net8.0/System.Composition.Hosting.xml", "lib/netstandard2.0/System.Composition.Hosting.dll", "lib/netstandard2.0/System.Composition.Hosting.xml", "system.composition.hosting.8.0.0.nupkg.sha512", "system.composition.hosting.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Runtime/8.0.0": {"sha512": "G+kRyB5/6+3ucRRQz+DF4uSHGqpkK8Q4ilVdbt4zvxpmvLVZNmSkyFAQpJLcbOyVF85aomJx0m+TGMDVlwx7ZQ==", "type": "package", "path": "system.composition.runtime/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Runtime.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Runtime.targets", "lib/net462/System.Composition.Runtime.dll", "lib/net462/System.Composition.Runtime.xml", "lib/net6.0/System.Composition.Runtime.dll", "lib/net6.0/System.Composition.Runtime.xml", "lib/net7.0/System.Composition.Runtime.dll", "lib/net7.0/System.Composition.Runtime.xml", "lib/net8.0/System.Composition.Runtime.dll", "lib/net8.0/System.Composition.Runtime.xml", "lib/netstandard2.0/System.Composition.Runtime.dll", "lib/netstandard2.0/System.Composition.Runtime.xml", "system.composition.runtime.8.0.0.nupkg.sha512", "system.composition.runtime.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.TypedParts/8.0.0": {"sha512": "DsSklhuA+Dsgo3ZZrar8hjBFvq1wa1grrkNCTt+6SoX3vq0Vy+HXJnVXrU/nNH1BjlGH684A7h4hJQHZd/u5mA==", "type": "package", "path": "system.composition.typedparts/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.TypedParts.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.TypedParts.targets", "lib/net462/System.Composition.TypedParts.dll", "lib/net462/System.Composition.TypedParts.xml", "lib/net6.0/System.Composition.TypedParts.dll", "lib/net6.0/System.Composition.TypedParts.xml", "lib/net7.0/System.Composition.TypedParts.dll", "lib/net7.0/System.Composition.TypedParts.xml", "lib/net8.0/System.Composition.TypedParts.dll", "lib/net8.0/System.Composition.TypedParts.xml", "lib/netstandard2.0/System.Composition.TypedParts.dll", "lib/netstandard2.0/System.Composition.TypedParts.xml", "system.composition.typedparts.8.0.0.nupkg.sha512", "system.composition.typedparts.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/6.0.1": {"sha512": "jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "type": "package", "path": "system.configuration.configurationmanager/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/net461/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.dll", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.6.0.1.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/6.0.0": {"sha512": "NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "type": "package", "path": "system.drawing.common/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/net461/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.xml", "lib/netcoreapp3.1/System.Drawing.Common.dll", "lib/netcoreapp3.1/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/unix/lib/net6.0/System.Drawing.Common.dll", "runtimes/unix/lib/net6.0/System.Drawing.Common.xml", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.xml", "runtimes/win/lib/net6.0/System.Drawing.Common.dll", "runtimes/win/lib/net6.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.xml", "system.drawing.common.6.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.IdentityModel.Tokens.Jwt/8.12.1": {"sha512": "jlYdVOJrdeyD80ppEqKJ8BhJdrV3MjeA+seURdhC0DnD41GyUA9Ik+P7Sb571ufVVCYIx93GjeqVvY3QyQxZAA==", "type": "package", "path": "system.identitymodel.tokens.jwt/8.12.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net8.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net8.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net9.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net9.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.8.12.1.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO.Abstractions/21.0.22": {"sha512": "DRnFyMIjO1KG9fqUPKV7XDPgECEwrNpsb3OLD0mImViVpRZ5747o7vqrOYbn9AMaQ8eajLeKH0fXQglgH7v42A==", "type": "package", "path": "system.io.abstractions/21.0.22", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon_256x256.png", "lib/net462/System.IO.Abstractions.dll", "lib/net462/System.IO.Abstractions.xml", "lib/net6.0/System.IO.Abstractions.dll", "lib/net6.0/System.IO.Abstractions.xml", "lib/net7.0/System.IO.Abstractions.dll", "lib/net7.0/System.IO.Abstractions.xml", "lib/net8.0/System.IO.Abstractions.dll", "lib/net8.0/System.IO.Abstractions.xml", "lib/netstandard2.0/System.IO.Abstractions.dll", "lib/netstandard2.0/System.IO.Abstractions.xml", "lib/netstandard2.1/System.IO.Abstractions.dll", "lib/netstandard2.1/System.IO.Abstractions.xml", "system.io.abstractions.21.0.22.nupkg.sha512", "system.io.abstractions.nuspec"]}, "System.IO.Hashing/6.0.0": {"sha512": "Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "type": "package", "path": "system.io.hashing/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.IO.Hashing.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.IO.Hashing.dll", "lib/net461/System.IO.Hashing.xml", "lib/net6.0/System.IO.Hashing.dll", "lib/net6.0/System.IO.Hashing.xml", "lib/netstandard2.0/System.IO.Hashing.dll", "lib/netstandard2.0/System.IO.Hashing.xml", "system.io.hashing.6.0.0.nupkg.sha512", "system.io.hashing.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory.Data/6.0.0": {"sha512": "ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "type": "package", "path": "system.memory.data/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Memory.Data.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/net6.0/System.Memory.Data.dll", "lib/net6.0/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.6.0.0.nupkg.sha512", "system.memory.data.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.Caching/6.0.0": {"sha512": "E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "type": "package", "path": "system.runtime.caching/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.Caching.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/_._", "lib/net6.0/System.Runtime.Caching.dll", "lib/net6.0/System.Runtime.Caching.xml", "lib/netcoreapp3.1/System.Runtime.Caching.dll", "lib/netcoreapp3.1/System.Runtime.Caching.xml", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/_._", "runtimes/win/lib/net6.0/System.Runtime.Caching.dll", "runtimes/win/lib/net6.0/System.Runtime.Caching.xml", "runtimes/win/lib/netcoreapp3.1/System.Runtime.Caching.dll", "runtimes/win/lib/netcoreapp3.1/System.Runtime.Caching.xml", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll", "runtimes/win/lib/netstandard2.0/System.Runtime.Caching.xml", "system.runtime.caching.6.0.0.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/6.0.0": {"sha512": "rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "type": "package", "path": "system.security.cryptography.protecteddata/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/6.0.0": {"sha512": "T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "type": "package", "path": "system.security.permissions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Permissions.dll", "lib/net461/System.Security.Permissions.xml", "lib/net5.0/System.Security.Permissions.dll", "lib/net5.0/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/netcoreapp3.1/System.Security.Permissions.dll", "lib/netcoreapp3.1/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "runtimes/win/lib/net461/System.Security.Permissions.dll", "runtimes/win/lib/net461/System.Security.Permissions.xml", "system.security.permissions.6.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.5": {"sha512": "0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "type": "package", "path": "system.text.json/8.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Windows.Extensions/6.0.0": {"sha512": "IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "type": "package", "path": "system.windows.extensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/netcoreapp3.1/System.Windows.Extensions.dll", "lib/netcoreapp3.1/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.dll", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.xml", "system.windows.extensions.6.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}, "Terminal.Gui/2.0.0": {"sha512": "R2a1zJQS1+sHfEw3Dec+WHUrS5tBJt/oHJy4qTU4J5FxsxrmCP28dsBTXeFe/HD51diMTs3O562S+6BCRq/etQ==", "type": "package", "path": "terminal.gui/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0/Terminal.Gui.dll", "lib/net8.0/Terminal.Gui.xml", "lib/net8.0/fr-FR/Terminal.Gui.resources.dll", "lib/net8.0/ja-JP/Terminal.Gui.resources.dll", "lib/net8.0/pt-PT/Terminal.Gui.resources.dll", "lib/net8.0/zh-Hans/Terminal.Gui.resources.dll", "logo.png", "terminal.gui.2.0.0.nupkg.sha512", "terminal.gui.nuspec"]}, "TestableIO.System.IO.Abstractions/21.0.22": {"sha512": "yq3I8rUjNoo4JTOT3jbGfTM5VfowaSYiooDO2bfvQe6XGVyZiSIzaKyCmSnuUEyuktw6zzuVE1lJnUeDhJWtyQ==", "type": "package", "path": "testableio.system.io.abstractions/21.0.22", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon_256x256.png", "lib/net462/TestableIO.System.IO.Abstractions.dll", "lib/net462/TestableIO.System.IO.Abstractions.xml", "lib/net6.0/TestableIO.System.IO.Abstractions.dll", "lib/net6.0/TestableIO.System.IO.Abstractions.xml", "lib/net7.0/TestableIO.System.IO.Abstractions.dll", "lib/net7.0/TestableIO.System.IO.Abstractions.xml", "lib/net8.0/TestableIO.System.IO.Abstractions.dll", "lib/net8.0/TestableIO.System.IO.Abstractions.xml", "lib/netstandard2.0/TestableIO.System.IO.Abstractions.dll", "lib/netstandard2.0/TestableIO.System.IO.Abstractions.xml", "lib/netstandard2.1/TestableIO.System.IO.Abstractions.dll", "lib/netstandard2.1/TestableIO.System.IO.Abstractions.xml", "testableio.system.io.abstractions.21.0.22.nupkg.sha512", "testableio.system.io.abstractions.nuspec"]}, "TestableIO.System.IO.Abstractions.Wrappers/21.0.22": {"sha512": "ETpw1B6VOsQ5zbfqxqLKjITnMvB9M0K5/bK8qJc6jih+tnlKQmtnnOPSS+OeKCqsZDYeHxPNp4ytQPJS3R+WXA==", "type": "package", "path": "testableio.system.io.abstractions.wrappers/21.0.22", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon_256x256.png", "lib/net462/TestableIO.System.IO.Abstractions.Wrappers.dll", "lib/net462/TestableIO.System.IO.Abstractions.Wrappers.xml", "lib/net6.0/TestableIO.System.IO.Abstractions.Wrappers.dll", "lib/net6.0/TestableIO.System.IO.Abstractions.Wrappers.xml", "lib/net7.0/TestableIO.System.IO.Abstractions.Wrappers.dll", "lib/net7.0/TestableIO.System.IO.Abstractions.Wrappers.xml", "lib/net8.0/TestableIO.System.IO.Abstractions.Wrappers.dll", "lib/net8.0/TestableIO.System.IO.Abstractions.Wrappers.xml", "lib/netstandard2.0/TestableIO.System.IO.Abstractions.Wrappers.dll", "lib/netstandard2.0/TestableIO.System.IO.Abstractions.Wrappers.xml", "lib/netstandard2.1/TestableIO.System.IO.Abstractions.Wrappers.dll", "lib/netstandard2.1/TestableIO.System.IO.Abstractions.Wrappers.xml", "testableio.system.io.abstractions.wrappers.21.0.22.nupkg.sha512", "testableio.system.io.abstractions.wrappers.nuspec"]}, "Wcwidth/2.0.0": {"sha512": "eGZYH/V+X1DwEXrPUlUcAp8pU5a38d8IvaQzq4pclJxF1x8bizssIo4uWc3qxmS64BNGK5WnDxyBYJz84+Msqg==", "type": "package", "path": "wcwidth/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Wcwidth.dll", "lib/net6.0/Wcwidth.xml", "lib/net7.0/Wcwidth.dll", "lib/net7.0/Wcwidth.xml", "lib/net8.0/Wcwidth.dll", "lib/net8.0/Wcwidth.xml", "lib/netstandard2.0/Wcwidth.dll", "lib/netstandard2.0/Wcwidth.xml", "wcwidth.2.0.0.nupkg.sha512", "wcwidth.nuspec"]}, "NotifyMaster.Core/1.0.0": {"type": "project", "path": "../../Core/NotifyMaster.Core/NotifyMaster.Core.csproj", "msbuildProject": "../../Core/NotifyMaster.Core/NotifyMaster.Core.csproj"}, "NotifyMaster.Database/1.0.0": {"type": "project", "path": "../../Core/NotifyMaster.Database/NotifyMaster.Database.csproj", "msbuildProject": "../../Core/NotifyMaster.Database/NotifyMaster.Database.csproj"}, "NotifyMaster.Entities/1.0.0": {"type": "project", "path": "../../Core/NotifyMaster.Entities/NotifyMaster.Entities.csproj", "msbuildProject": "../../Core/NotifyMaster.Entities/NotifyMaster.Entities.csproj"}, "PluginCore/1.0.0": {"type": "project", "path": "../../Core/PluginCore/PluginCore.csproj", "msbuildProject": "../../Core/PluginCore/PluginCore.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["AWSSDK.S3 >= 4.0.3", "Azure.Storage.Blobs >= 12.24.1", "FastEndpoints >= 6.2.0", "Hangfire.AspNetCore >= 1.8.20", "Hangfire.Core >= 1.8.20", "Hangfire.InMemory >= 1.0.0", "Hangfire.PostgreSql >= 1.20.12", "Hangfire.SqlServer >= 1.8.20", "MediatR >= 12.5.0", "Microsoft.AspNetCore.Authentication.JwtBearer >= 9.0.6", "Microsoft.AspNetCore.OpenApi >= 9.0.6", "Microsoft.AspNetCore.SignalR >= 1.2.0", "Microsoft.EntityFrameworkCore >= 9.0.6", "Microsoft.EntityFrameworkCore.InMemory >= 9.0.6", "Microsoft.EntityFrameworkCore.SqlServer >= 9.0.6", "Microsoft.EntityFrameworkCore.Sqlite >= 9.0.6", "Microsoft.Extensions.Caching.Memory >= 9.0.6", "Microsoft.Extensions.Caching.StackExchangeRedis >= 9.0.6", "NotifyMaster.Core >= 1.0.0", "NotifyMaster.Database >= 1.0.0", "Npgsql.EntityFrameworkCore.PostgreSQL >= 9.0.4", "PluginCore >= 1.0.0", "Scalar.AspNetCore >= 2.5.3", "Serilog.AspNetCore >= 9.0.0", "Serilog.Sinks.Console >= 6.0.1-dev-00953", "Serilog.Sinks.Debug >= 3.0.0", "System.Text.Json >= 8.0.5", "Terminal.Gui >= 2.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\NotifyMasterApi.csproj", "projectName": "NotifyMasterApi", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\NotifyMasterApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Implementations\\NotifyMasterApi\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Core\\NotifyMaster.Core.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\NotifyMaster.Database\\NotifyMaster.Database.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Documentos\\NotificationService-master\\src\\Core\\PluginCore\\PluginCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[4.0.3, )"}, "Azure.Storage.Blobs": {"target": "Package", "version": "[12.24.1, )"}, "FastEndpoints": {"target": "Package", "version": "[6.2.0, )"}, "Hangfire.AspNetCore": {"target": "Package", "version": "[1.8.20, )"}, "Hangfire.Core": {"target": "Package", "version": "[1.8.20, )"}, "Hangfire.InMemory": {"target": "Package", "version": "[1.0.0, )"}, "Hangfire.PostgreSql": {"target": "Package", "version": "[1.20.12, )"}, "Hangfire.SqlServer": {"target": "Package", "version": "[1.8.20, )"}, "MediatR": {"target": "Package", "version": "[12.5.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[9.0.6, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "Scalar.AspNetCore": {"target": "Package", "version": "[2.5.3, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.1-dev-00953, )"}, "Serilog.Sinks.Debug": {"target": "Package", "version": "[3.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}, "Terminal.Gui": {"target": "Package", "version": "[2.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[9.0.5, 9.0.5]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[9.0.5, 9.0.5]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.AspNetCore": "(, 9.0.32767]", "Microsoft.AspNetCore.Antiforgery": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication.BearerToken": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication.Cookies": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication.Core": "(, 9.0.32767]", "Microsoft.AspNetCore.Authentication.OAuth": "(, 9.0.32767]", "Microsoft.AspNetCore.Authorization": "(, 9.0.32767]", "Microsoft.AspNetCore.Authorization.Policy": "(, 9.0.32767]", "Microsoft.AspNetCore.Components": "(, 9.0.32767]", "Microsoft.AspNetCore.Components.Authorization": "(, 9.0.32767]", "Microsoft.AspNetCore.Components.Endpoints": "(, 9.0.32767]", "Microsoft.AspNetCore.Components.Forms": "(, 9.0.32767]", "Microsoft.AspNetCore.Components.Server": "(, 9.0.32767]", "Microsoft.AspNetCore.Components.Web": "(, 9.0.32767]", "Microsoft.AspNetCore.Connections.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.CookiePolicy": "(, 9.0.32767]", "Microsoft.AspNetCore.Cors": "(, 9.0.32767]", "Microsoft.AspNetCore.Cryptography.Internal": "(, 9.0.32767]", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "(, 9.0.32767]", "Microsoft.AspNetCore.DataProtection": "(, 9.0.32767]", "Microsoft.AspNetCore.DataProtection.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.DataProtection.Extensions": "(, 9.0.32767]", "Microsoft.AspNetCore.Diagnostics": "(, 9.0.32767]", "Microsoft.AspNetCore.Diagnostics.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "(, 9.0.32767]", "Microsoft.AspNetCore.HostFiltering": "(, 9.0.32767]", "Microsoft.AspNetCore.Hosting": "(, 9.0.32767]", "Microsoft.AspNetCore.Hosting.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Html.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Http": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Connections": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Connections.Common": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Extensions": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Features": "(, 9.0.32767]", "Microsoft.AspNetCore.Http.Results": "(, 9.0.32767]", "Microsoft.AspNetCore.HttpLogging": "(, 9.0.32767]", "Microsoft.AspNetCore.HttpOverrides": "(, 9.0.32767]", "Microsoft.AspNetCore.HttpsPolicy": "(, 9.0.32767]", "Microsoft.AspNetCore.Identity": "(, 9.0.32767]", "Microsoft.AspNetCore.Localization": "(, 9.0.32767]", "Microsoft.AspNetCore.Localization.Routing": "(, 9.0.32767]", "Microsoft.AspNetCore.Metadata": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.ApiExplorer": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Core": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Cors": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.DataAnnotations": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Formatters.Json": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Localization": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.Razor": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.RazorPages": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.TagHelpers": "(, 9.0.32767]", "Microsoft.AspNetCore.Mvc.ViewFeatures": "(, 9.0.32767]", "Microsoft.AspNetCore.OutputCaching": "(, 9.0.32767]", "Microsoft.AspNetCore.RateLimiting": "(, 9.0.32767]", "Microsoft.AspNetCore.Razor": "(, 9.0.32767]", "Microsoft.AspNetCore.Razor.Runtime": "(, 9.0.32767]", "Microsoft.AspNetCore.RequestDecompression": "(, 9.0.32767]", "Microsoft.AspNetCore.ResponseCaching": "(, 9.0.32767]", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.ResponseCompression": "(, 9.0.32767]", "Microsoft.AspNetCore.Rewrite": "(, 9.0.32767]", "Microsoft.AspNetCore.Routing": "(, 9.0.32767]", "Microsoft.AspNetCore.Routing.Abstractions": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.HttpSys": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.IIS": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.IISIntegration": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.Kestrel": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Core": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "(, 9.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "(, 9.0.32767]", "Microsoft.AspNetCore.Session": "(, 9.0.32767]", "Microsoft.AspNetCore.SignalR": "(, 9.0.32767]", "Microsoft.AspNetCore.SignalR.Common": "(, 9.0.32767]", "Microsoft.AspNetCore.SignalR.Core": "(, 9.0.32767]", "Microsoft.AspNetCore.SignalR.Protocols.Json": "(, 9.0.32767]", "Microsoft.AspNetCore.StaticAssets": "(, 9.0.32767]", "Microsoft.AspNetCore.StaticFiles": "(, 9.0.32767]", "Microsoft.AspNetCore.WebSockets": "(, 9.0.32767]", "Microsoft.AspNetCore.WebUtilities": "(, 9.0.32767]", "Microsoft.CSharp": "(, 4.7.32767]", "Microsoft.Extensions.Caching.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Caching.Memory": "(, 9.0.32767]", "Microsoft.Extensions.Configuration": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.Binder": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.CommandLine": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.EnvironmentVariables": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.FileExtensions": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.Ini": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.Json": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.KeyPerFile": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.UserSecrets": "(, 9.0.32767]", "Microsoft.Extensions.Configuration.Xml": "(, 9.0.32767]", "Microsoft.Extensions.DependencyInjection": "(, 9.0.32767]", "Microsoft.Extensions.DependencyInjection.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Diagnostics": "(, 9.0.32767]", "Microsoft.Extensions.Diagnostics.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Diagnostics.HealthChecks": "(, 9.0.32767]", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Features": "(, 9.0.32767]", "Microsoft.Extensions.FileProviders.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.FileProviders.Composite": "(, 9.0.32767]", "Microsoft.Extensions.FileProviders.Embedded": "(, 9.0.32767]", "Microsoft.Extensions.FileProviders.Physical": "(, 9.0.32767]", "Microsoft.Extensions.FileSystemGlobbing": "(, 9.0.32767]", "Microsoft.Extensions.Hosting": "(, 9.0.32767]", "Microsoft.Extensions.Hosting.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Http": "(, 9.0.32767]", "Microsoft.Extensions.Identity.Core": "(, 9.0.32767]", "Microsoft.Extensions.Identity.Stores": "(, 9.0.32767]", "Microsoft.Extensions.Localization": "(, 9.0.32767]", "Microsoft.Extensions.Localization.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Logging": "(, 9.0.32767]", "Microsoft.Extensions.Logging.Abstractions": "(, 9.0.32767]", "Microsoft.Extensions.Logging.Configuration": "(, 9.0.32767]", "Microsoft.Extensions.Logging.Console": "(, 9.0.32767]", "Microsoft.Extensions.Logging.Debug": "(, 9.0.32767]", "Microsoft.Extensions.Logging.EventLog": "(, 9.0.32767]", "Microsoft.Extensions.Logging.EventSource": "(, 9.0.32767]", "Microsoft.Extensions.Logging.TraceSource": "(, 9.0.32767]", "Microsoft.Extensions.ObjectPool": "(, 9.0.32767]", "Microsoft.Extensions.Options": "(, 9.0.32767]", "Microsoft.Extensions.Options.ConfigurationExtensions": "(, 9.0.32767]", "Microsoft.Extensions.Options.DataAnnotations": "(, 9.0.32767]", "Microsoft.Extensions.Primitives": "(, 9.0.32767]", "Microsoft.Extensions.WebEncoders": "(, 9.0.32767]", "Microsoft.JSInterop": "(, 9.0.32767]", "Microsoft.Net.Http.Headers": "(, 9.0.32767]", "Microsoft.NETCore.App": "(, 2.1.32767]", "Microsoft.VisualBasic": "(, 10.4.32767]", "Microsoft.Win32.Primitives": "(, 4.3.32767]", "Microsoft.Win32.Registry": "(, 5.0.32767]", "Microsoft.Win32.SystemEvents": "(, 5.0.32767]", "runtime.any.System.Collections": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.any.System.Globalization": "(, 4.3.32767]", "runtime.any.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.any.System.IO": "(, 4.3.32767]", "runtime.any.System.Reflection": "(, 4.3.32767]", "runtime.any.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.any.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.any.System.Runtime": "(, 4.3.32767]", "runtime.any.System.Runtime.Handles": "(, 4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.any.System.Text.Encoding": "(, 4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.any.System.Threading.Tasks": "(, 4.3.32767]", "runtime.any.System.Threading.Timer": "(, 4.3.32767]", "runtime.aot.System.Collections": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.32767]", "runtime.aot.System.Globalization": "(, 4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.32767]", "runtime.aot.System.IO": "(, 4.3.32767]", "runtime.aot.System.Reflection": "(, 4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.32767]", "runtime.aot.System.Runtime": "(, 4.3.32767]", "runtime.aot.System.Runtime.Handles": "(, 4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding": "(, 4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.32767]", "runtime.aot.System.Threading.Tasks": "(, 4.3.32767]", "runtime.aot.System.Threading.Timer": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.unix.System.Console": "(, 4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.unix.System.IO.FileSystem": "(, 4.3.32767]", "runtime.unix.System.Net.Primitives": "(, 4.3.32767]", "runtime.unix.System.Net.Sockets": "(, 4.3.32767]", "runtime.unix.System.Private.Uri": "(, 4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.32767]", "runtime.win.System.Console": "(, 4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.32767]", "runtime.win.System.IO.FileSystem": "(, 4.3.32767]", "runtime.win.System.Net.Primitives": "(, 4.3.32767]", "runtime.win.System.Net.Sockets": "(, 4.3.32767]", "runtime.win.System.Runtime.Extensions": "(, 4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.32767]", "runtime.win7.System.Private.Uri": "(, 4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.32767]", "System.AppContext": "(, 4.3.32767]", "System.Buffers": "(, 5.0.32767]", "System.Collections": "(, 4.3.32767]", "System.Collections.Concurrent": "(, 4.3.32767]", "System.Collections.Immutable": "(, 9.0.32767]", "System.Collections.NonGeneric": "(, 4.3.32767]", "System.Collections.Specialized": "(, 4.3.32767]", "System.ComponentModel": "(, 4.3.32767]", "System.ComponentModel.Annotations": "(, 5.0.32767]", "System.ComponentModel.EventBasedAsync": "(, 4.3.32767]", "System.ComponentModel.Primitives": "(, 4.3.32767]", "System.ComponentModel.TypeConverter": "(, 4.3.32767]", "System.Console": "(, 4.3.32767]", "System.Data.Common": "(, 4.3.32767]", "System.Data.DataSetExtensions": "(, 4.5.32767]", "System.Diagnostics.Contracts": "(, 4.3.32767]", "System.Diagnostics.Debug": "(, 4.3.32767]", "System.Diagnostics.DiagnosticSource": "(, 9.0.32767]", "System.Diagnostics.EventLog": "(, 9.0.32767]", "System.Diagnostics.FileVersionInfo": "(, 4.3.32767]", "System.Diagnostics.Process": "(, 4.3.32767]", "System.Diagnostics.StackTrace": "(, 4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.32767]", "System.Diagnostics.Tools": "(, 4.3.32767]", "System.Diagnostics.TraceSource": "(, 4.3.32767]", "System.Diagnostics.Tracing": "(, 4.3.32767]", "System.Drawing.Common": "(, 5.0.32767]", "System.Drawing.Primitives": "(, 4.3.32767]", "System.Dynamic.Runtime": "(, 4.3.32767]", "System.Formats.Asn1": "(, 9.0.32767]", "System.Formats.Tar": "(, 9.0.32767]", "System.Globalization": "(, 4.3.32767]", "System.Globalization.Calendars": "(, 4.3.32767]", "System.Globalization.Extensions": "(, 4.3.32767]", "System.IO": "(, 4.3.32767]", "System.IO.Compression": "(, 4.3.32767]", "System.IO.Compression.ZipFile": "(, 4.3.32767]", "System.IO.FileSystem": "(, 4.3.32767]", "System.IO.FileSystem.AccessControl": "(, 5.0.32767]", "System.IO.FileSystem.DriveInfo": "(, 4.3.32767]", "System.IO.FileSystem.Primitives": "(, 4.3.32767]", "System.IO.FileSystem.Watcher": "(, 4.3.32767]", "System.IO.IsolatedStorage": "(, 4.3.32767]", "System.IO.MemoryMappedFiles": "(, 4.3.32767]", "System.IO.Pipelines": "(, 9.0.32767]", "System.IO.Pipes": "(, 4.3.32767]", "System.IO.Pipes.AccessControl": "(, 4.6.32767]", "System.IO.UnmanagedMemoryStream": "(, 4.3.32767]", "System.Linq": "(, 4.3.32767]", "System.Linq.Expressions": "(, 4.3.32767]", "System.Linq.Parallel": "(, 4.3.32767]", "System.Linq.Queryable": "(, 4.3.32767]", "System.Memory": "(, 5.0.32767]", "System.Net.Http": "(, 4.3.32767]", "System.Net.Http.Json": "(, 9.0.32767]", "System.Net.NameResolution": "(, 4.3.32767]", "System.Net.NetworkInformation": "(, 4.3.32767]", "System.Net.Ping": "(, 4.3.32767]", "System.Net.Primitives": "(, 4.3.32767]", "System.Net.Requests": "(, 4.3.32767]", "System.Net.Security": "(, 4.3.32767]", "System.Net.Sockets": "(, 4.3.32767]", "System.Net.WebHeaderCollection": "(, 4.3.32767]", "System.Net.WebSockets": "(, 4.3.32767]", "System.Net.WebSockets.Client": "(, 4.3.32767]", "System.Numerics.Vectors": "(, 5.0.32767]", "System.ObjectModel": "(, 4.3.32767]", "System.Private.DataContractSerialization": "(, 4.3.32767]", "System.Private.Uri": "(, 4.3.32767]", "System.Reflection": "(, 4.3.32767]", "System.Reflection.DispatchProxy": "(, 6.0.32767]", "System.Reflection.Emit": "(, 4.7.32767]", "System.Reflection.Emit.ILGeneration": "(, 4.7.32767]", "System.Reflection.Emit.Lightweight": "(, 4.7.32767]", "System.Reflection.Extensions": "(, 4.3.32767]", "System.Reflection.Metadata": "(, 9.0.32767]", "System.Reflection.Primitives": "(, 4.3.32767]", "System.Reflection.TypeExtensions": "(, 4.7.32767]", "System.Resources.Reader": "(, 4.3.32767]", "System.Resources.ResourceManager": "(, 4.3.32767]", "System.Resources.Writer": "(, 4.3.32767]", "System.Runtime": "(, 4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(, 7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.32767]", "System.Runtime.Extensions": "(, 4.3.32767]", "System.Runtime.Handles": "(, 4.3.32767]", "System.Runtime.InteropServices": "(, 4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.32767]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.32767]", "System.Runtime.Loader": "(, 4.3.32767]", "System.Runtime.Numerics": "(, 4.3.32767]", "System.Runtime.Serialization.Formatters": "(, 4.3.32767]", "System.Runtime.Serialization.Json": "(, 4.3.32767]", "System.Runtime.Serialization.Primitives": "(, 4.3.32767]", "System.Runtime.Serialization.Xml": "(, 4.3.32767]", "System.Runtime.WindowsRuntime": "(, 4.7.32767]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.32767]", "System.Security.AccessControl": "(, 6.0.32767]", "System.Security.Claims": "(, 4.3.32767]", "System.Security.Cryptography.Algorithms": "(, 4.3.32767]", "System.Security.Cryptography.Cng": "(, 5.0.32767]", "System.Security.Cryptography.Csp": "(, 4.3.32767]", "System.Security.Cryptography.Encoding": "(, 4.3.32767]", "System.Security.Cryptography.OpenSsl": "(, 5.0.32767]", "System.Security.Cryptography.Pkcs": "(, 8.0.32767]", "System.Security.Cryptography.Primitives": "(, 4.3.32767]", "System.Security.Cryptography.X509Certificates": "(, 4.3.32767]", "System.Security.Cryptography.Xml": "(, 9.0.32767]", "System.Security.Permissions": "(, 5.0.32767]", "System.Security.Principal": "(, 4.3.32767]", "System.Security.Principal.Windows": "(, 5.0.32767]", "System.Security.SecureString": "(, 4.3.32767]", "System.Text.Encoding": "(, 4.3.32767]", "System.Text.Encoding.CodePages": "(, 9.0.32767]", "System.Text.Encoding.Extensions": "(, 4.3.32767]", "System.Text.Encodings.Web": "(, 9.0.32767]", "System.Text.Json": "(, 9.0.32767]", "System.Text.RegularExpressions": "(, 4.3.32767]", "System.Threading": "(, 4.3.32767]", "System.Threading.Channels": "(, 9.0.32767]", "System.Threading.Overlapped": "(, 4.3.32767]", "System.Threading.RateLimiting": "(, 9.0.32767]", "System.Threading.Tasks": "(, 4.3.32767]", "System.Threading.Tasks.Dataflow": "(, 9.0.32767]", "System.Threading.Tasks.Extensions": "(, 5.0.32767]", "System.Threading.Tasks.Parallel": "(, 4.3.32767]", "System.Threading.Thread": "(, 4.3.32767]", "System.Threading.ThreadPool": "(, 4.3.32767]", "System.Threading.Timer": "(, 4.3.32767]", "System.ValueTuple": "(, 4.5.32767]", "System.Windows.Extensions": "(, 5.0.32767]", "System.Xml.ReaderWriter": "(, 4.3.32767]", "System.Xml.XDocument": "(, 4.3.32767]", "System.Xml.XmlDocument": "(, 4.3.32767]", "System.Xml.XmlSerializer": "(, 4.3.32767]", "System.Xml.XPath": "(, 4.3.32767]", "System.Xml.XPath.XDocument": "(, 5.0.32767]"}}}}}