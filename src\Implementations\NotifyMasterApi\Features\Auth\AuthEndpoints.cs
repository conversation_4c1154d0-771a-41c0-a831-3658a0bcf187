namespace NotifyMasterApi.Features.Auth;

/// <summary>
/// Request model for user login
/// </summary>
public class LoginRequest
{
    public string TenantId { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// Request model for token refresh
/// </summary>
public class RefreshTokenRequest
{
    public string RefreshToken { get; set; } = string.Empty;
}

/// <summary>
/// Request model for logout
/// </summary>
public class LogoutRequest
{
    public string RefreshToken { get; set; } = string.Empty;
}

/// <summary>
/// Response model for authentication
/// </summary>
public class AuthResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Token { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? RefreshToken { get; set; }
    public UserInfo? User { get; set; }
    public List<string> Roles { get; set; } = new();
    public List<string> Permissions { get; set; } = new();
}

/// <summary>
/// User information for authentication response
/// </summary>
public class UserInfo
{
    public string Id { get; set; } = string.Empty;
    public string TenantId { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public UserStatus Status { get; set; }
    public DateTime? LastLoginAt { get; set; }
}

/// <summary>
/// Endpoint for user login
/// </summary>
public class LoginEndpoint : Endpoint<LoginRequest, AuthResponse>
{
    private readonly IAuthenticationService _authService;
    private readonly ILogger<LoginEndpoint> _logger;

    public LoginEndpoint(IAuthenticationService authService, ILogger<LoginEndpoint> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/auth/login");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "User Login";
            s.Description = "Authenticate a user and return JWT token";
            s.Response<AuthResponse>(200, "Login successful");
            s.Response(400, "Invalid credentials");
            s.Response(401, "Authentication failed");
        });
    }

    public override async Task HandleAsync(LoginRequest req, CancellationToken ct)
    {
        try
        {
            var result = await _authService.AuthenticateAsync(req.TenantId, req.Email, req.Password, ct);

            if (!result.IsSuccess)
            {
                await SendAsync(new AuthResponse
                {
                    Success = false,
                    Message = result.Message ?? "Authentication failed"
                }, 401, ct);
                return;
            }

            var authResult = result.Data!;
            var response = new AuthResponse
            {
                Success = true,
                Message = "Login successful",
                Token = authResult.Token,
                ExpiresAt = authResult.ExpiresAt,
                RefreshToken = authResult.RefreshToken,
                User = new UserInfo
                {
                    Id = authResult.User.Id,
                    TenantId = authResult.User.TenantId,
                    Username = authResult.User.Username,
                    Email = authResult.User.Email,
                    FirstName = authResult.User.FirstName,
                    LastName = authResult.User.LastName,
                    Status = authResult.User.Status,
                    LastLoginAt = authResult.User.LastLoginAt
                },
                Roles = authResult.Roles.Select(r => r.Name).ToList(),
                Permissions = authResult.Permissions.Select(p => $"{p.Resource}:{p.Action}").ToList()
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for {Email} in tenant {TenantId}", req.Email, req.TenantId);
            await SendAsync(new AuthResponse
            {
                Success = false,
                Message = "Authentication failed"
            }, 500, ct);
        }
    }
}

/// <summary>
/// Endpoint for token refresh
/// </summary>
public class RefreshTokenEndpoint : Endpoint<RefreshTokenRequest, AuthResponse>
{
    private readonly IAuthenticationService _authService;
    private readonly ILogger<RefreshTokenEndpoint> _logger;

    public RefreshTokenEndpoint(IAuthenticationService authService, ILogger<RefreshTokenEndpoint> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/auth/refresh");
        AllowAnonymous();
        Summary(s =>
        {
            s.Summary = "Refresh Token";
            s.Description = "Refresh an expired JWT token using a refresh token";
            s.Response<AuthResponse>(200, "Token refreshed successfully");
            s.Response(400, "Invalid refresh token");
            s.Response(401, "Refresh token expired");
        });
    }

    public override async Task HandleAsync(RefreshTokenRequest req, CancellationToken ct)
    {
        try
        {
            var result = await _authService.RefreshTokenAsync(req.RefreshToken, ct);

            if (!result.IsSuccess)
            {
                await SendAsync(new AuthResponse
                {
                    Success = false,
                    Message = result.Message ?? "Token refresh failed"
                }, 401, ct);
                return;
            }

            var authResult = result.Data!;
            var response = new AuthResponse
            {
                Success = true,
                Message = "Token refreshed successfully",
                Token = authResult.Token,
                ExpiresAt = authResult.ExpiresAt,
                RefreshToken = authResult.RefreshToken,
                User = new UserInfo
                {
                    Id = authResult.User.Id,
                    TenantId = authResult.User.TenantId,
                    Username = authResult.User.Username,
                    Email = authResult.User.Email,
                    FirstName = authResult.User.FirstName,
                    LastName = authResult.User.LastName,
                    Status = authResult.User.Status,
                    LastLoginAt = authResult.User.LastLoginAt
                },
                Roles = authResult.Roles.Select(r => r.Name).ToList(),
                Permissions = authResult.Permissions.Select(p => $"{p.Resource}:{p.Action}").ToList()
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            await SendAsync(new AuthResponse
            {
                Success = false,
                Message = "Token refresh failed"
            }, 500, ct);
        }
    }
}

/// <summary>
/// Endpoint for user logout
/// </summary>
public class LogoutEndpoint : Endpoint<LogoutRequest, object>
{
    private readonly IAuthenticationService _authService;
    private readonly ILogger<LogoutEndpoint> _logger;

    public LogoutEndpoint(IAuthenticationService authService, ILogger<LogoutEndpoint> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    public override void Configure()
    {
        Post("/api/auth/logout");
        Summary(s =>
        {
            s.Summary = "User Logout";
            s.Description = "Logout a user and revoke their refresh token";
            s.Response(200, "Logout successful");
            s.Response(401, "Unauthorized");
        });
    }

    public override async Task HandleAsync(LogoutRequest req, CancellationToken ct)
    {
        try
        {
            await _authService.RevokeTokenAsync(req.RefreshToken, ct);

            await SendOkAsync(new
            {
                Success = true,
                Message = "Logout successful"
            }, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            await SendAsync(new
            {
                Success = false,
                Message = "Logout failed"
            }, 500, ct);
        }
    }
}

/// <summary>
/// Endpoint for getting current user information
/// </summary>
public class GetCurrentUserEndpoint : Endpoint<EmptyRequest, object>
{
    private readonly ITenantContext _tenantContext;
    private readonly IUserService _userService;
    private readonly ILogger<GetCurrentUserEndpoint> _logger;

    public GetCurrentUserEndpoint(ITenantContext tenantContext, IUserService userService, ILogger<GetCurrentUserEndpoint> logger)
    {
        _tenantContext = tenantContext;
        _userService = userService;
        _logger = logger;
    }

    public override void Configure()
    {
        Get("/api/auth/me");
        Summary(s =>
        {
            s.Summary = "Get Current User";
            s.Description = "Get information about the currently authenticated user";
            s.Response(200, "User information retrieved successfully");
            s.Response(401, "Unauthorized");
        });
    }

    public override async Task HandleAsync(EmptyRequest req, CancellationToken ct)
    {
        try
        {
            var userId = _tenantContext.UserId;
            var tenantId = _tenantContext.TenantId;

            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(tenantId))
            {
                await SendUnauthorizedAsync(ct);
                return;
            }

            var user = await _userService.GetUserAsync(userId, ct);
            if (user == null)
            {
                await SendUnauthorizedAsync(ct);
                return;
            }

            var roles = await _tenantContext.GetUserRolesAsync();
            var permissions = await _tenantContext.GetUserPermissionsAsync();

            var response = new
            {
                User = new UserInfo
                {
                    Id = user.Id,
                    TenantId = user.TenantId,
                    Username = user.Username,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Status = user.Status,
                    LastLoginAt = user.LastLoginAt
                },
                Roles = roles.Select(r => r.Name).ToList(),
                Permissions = permissions.Select(p => $"{p.Resource}:{p.Action}").ToList(),
                Tenant = _tenantContext.CurrentTenant
            };

            await SendOkAsync(response, ct);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user information");
            await SendErrorsAsync(500, ct);
        }
    }
}
