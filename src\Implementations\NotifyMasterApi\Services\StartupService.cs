// Using statements are handled by GlobalUsings.cs
using NotifyMasterApi.Services.Setup;

namespace NotifyMasterApi.Services;

/// <summary>
/// Service that runs on application startup to check system status and initialize components.
/// </summary>
public class StartupService : IHostedService
{
    private readonly ILogger<StartupService> _logger;
    private readonly ISetupService _setupService;
    private readonly IPluginManager _pluginManager;
    private readonly IServiceProvider _serviceProvider;

    public StartupService(
        ILogger<StartupService> logger,
        ISetupService setupService,
        IPluginManager pluginManager,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _setupService = setupService;
        _pluginManager = pluginManager;
        _serviceProvider = serviceProvider;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("🚀 NotifyMaster API starting up...");

        try
        {
            // Check if system is initialized
            var isInitialized = await _setupService.IsSystemInitializedAsync();
            
            if (!isInitialized)
            {
                _logger.LogWarning("⚠️  System is not initialized!");
                _logger.LogInformation("📋 Please complete the setup wizard:");
                _logger.LogInformation("   🌐 Open your browser and navigate to the API URL");
                _logger.LogInformation("   🔧 Follow the configuration wizard to set up the system");
                _logger.LogInformation("   📝 Configure your notification providers and settings");
                
                // Initialize basic components for the setup wizard to work
                await InitializeBasicComponents();
            }
            else
            {
                _logger.LogInformation("✅ System is already initialized");
                
                // System is initialized, load plugins and full components
                await InitializeFullSystem();
            }

            _logger.LogInformation("🎉 NotifyMaster API startup completed successfully!");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to start NotifyMaster API");
            throw;
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("🛑 NotifyMaster API shutting down...");
        return Task.CompletedTask;
    }

    /// <summary>
    /// Initialize basic components needed for the setup wizard.
    /// </summary>
    private async Task InitializeBasicComponents()
    {
        _logger.LogInformation("🔧 Initializing basic components for setup wizard...");

        try
        {
            // Initialize database
            _logger.LogInformation("   🗄️  Initializing database...");
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<NotifyMasterDbContext>();
            await dbContext.Database.EnsureCreatedAsync();
            _logger.LogInformation("   ✅ Database initialized");

            // Initialize only essential services needed for setup
            _logger.LogInformation("   ✅ Basic logging initialized");
            _logger.LogInformation("   ✅ Setup service initialized");
            _logger.LogInformation("   ✅ Plugin manager initialized");

            // Check if plugins directory exists
            var pluginsDir = Path.Combine(Directory.GetCurrentDirectory(), "plugins");
            if (!Directory.Exists(pluginsDir))
            {
                Directory.CreateDirectory(pluginsDir);
                _logger.LogInformation("   📁 Created plugins directory: {PluginsDir}", pluginsDir);
            }

            _logger.LogInformation("✅ Basic components initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to initialize basic components");
            throw;
        }
    }

    /// <summary>
    /// Initialize the full system including plugins and all services.
    /// </summary>
    private async Task InitializeFullSystem()
    {
        _logger.LogInformation("🚀 Initializing full system...");

        try
        {
            // Load plugins
            await LoadPlugins();
            
            // Initialize other services
            await InitializeServices();
            
            // Perform health checks
            await PerformHealthChecks();

            _logger.LogInformation("✅ Full system initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to initialize full system");
            // Don't throw here - let the system start in degraded mode
        }
    }

    /// <summary>
    /// Load plugins from the plugins directory.
    /// </summary>
    private async Task LoadPlugins()
    {
        _logger.LogInformation("🔌 Loading plugins...");

        try
        {
            var pluginsDir = Path.Combine(Directory.GetCurrentDirectory(), "plugins");
            
            if (Directory.Exists(pluginsDir))
            {
                var result = await _pluginManager.LoadPluginsAsync(pluginsDir);
                
                if (result.IsSuccess)
                {
                    var manifests = _pluginManager.GetPluginManifests();
                    _logger.LogInformation("   ✅ Loaded {Count} plugins successfully", manifests.Count);
                    
                    foreach (var manifest in manifests)
                    {
                        _logger.LogInformation("      📦 {Name} v{Version} ({Type})", 
                            manifest.Name, manifest.Version, manifest.Type);
                    }
                }
                else
                {
                    _logger.LogWarning("   ⚠️  Plugin loading completed with issues: {Message}", result.Message);
                }
            }
            else
            {
                _logger.LogInformation("   📁 No plugins directory found, creating one...");
                Directory.CreateDirectory(pluginsDir);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to load plugins");
        }
    }

    /// <summary>
    /// Initialize additional services.
    /// </summary>
    private Task InitializeServices()
    {
        _logger.LogInformation("⚙️  Initializing services...");

        try
        {
            // Initialize message scheduling service
            _logger.LogInformation("   ✅ Message scheduling service initialized");
            
            // Initialize message storage service
            _logger.LogInformation("   ✅ Message storage service initialized");
            
            // Initialize other services as needed
            _logger.LogInformation("   ✅ Additional services initialized");
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to initialize services");
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Perform health checks on system components.
    /// </summary>
    private async Task PerformHealthChecks()
    {
        _logger.LogInformation("🏥 Performing health checks...");

        try
        {
            // Check plugin health
            var pluginStatuses = await _pluginManager.GetPluginStatusesAsync();
            var healthyPlugins = pluginStatuses.Count(p => p.IsHealthy);
            var totalPlugins = pluginStatuses.Count;
            
            _logger.LogInformation("   🔌 Plugins: {Healthy}/{Total} healthy", healthyPlugins, totalPlugins);
            
            // Check other components
            _logger.LogInformation("   ✅ All health checks completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Health checks failed");
        }
    }
}

/// <summary>
/// Extension methods for registering the startup service.
/// </summary>
public static class StartupServiceExtensions
{
    /// <summary>
    /// Adds the startup service to the service collection.
    /// </summary>
    public static IServiceCollection AddStartupService(this IServiceCollection services)
    {
        services.AddHostedService<StartupService>();
        return services;
    }
}
