{"sourceFile": "src/Core/NotifyMaster.Database/NotifyMasterDbContext.cs", "activeCommit": 0, "commits": [{"activePatchIndex": 8, "patches": [{"date": 1751218231645, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751225348370, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,5 @@\n-using Microsoft.EntityFrameworkCore;\n-using PluginCore.Models;\n+// Using statements are handled by GlobalUsings.cs\n \n namespace NotifyMaster.Database;\n \n /// <summary>\n"}, {"date": 1751235019661, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,8 +26,11 @@\n     public DbSet<MessageTemplate> MessageTemplates { get; set; }\n     public DbSet<ScheduledMessage> ScheduledMessages { get; set; }\n     public DbSet<WebhookEndpoint> WebhookEndpoints { get; set; }\n \n+    // Audit and Queue Management\n+    public DbSet<RequestAuditLog> RequestAuditLogs { get; set; }\n+\n     protected override void OnModelCreating(ModelBuilder modelBuilder)\n     {\n         base.OnModelCreating(modelBuilder);\n \n"}, {"date": 1751235035598, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -41,9 +41,12 @@\n         ConfigureUserManagement(modelBuilder);\n         \n         // Configure notification entities\n         ConfigureNotificationEntities(modelBuilder);\n-        \n+\n+        // Configure audit entities\n+        ConfigureAuditEntities(modelBuilder);\n+\n         // Seed default data\n         SeedDefaultData(modelBuilder);\n     }\n \n"}, {"date": 1751235069057, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -247,8 +247,44 @@\n             entity.HasIndex(we => new { we.TenantId, we.Name }).IsUnique();\n         });\n     }\n \n+    private void ConfigureAuditEntities(ModelBuilder modelBuilder)\n+    {\n+        modelBuilder.Entity<RequestAuditLog>(entity =>\n+        {\n+            entity.HasKey(r => r.Id);\n+            entity.Property(r => r.QueueId).IsRequired().HasMaxLength(50);\n+            entity.Property(r => r.RequestId).IsRequired().HasMaxLength(50);\n+            entity.Property(r => r.CorrelationId).HasMaxLength(50);\n+            entity.Property(r => r.TenantId).HasMaxLength(50);\n+            entity.Property(r => r.UserId).HasMaxLength(50);\n+            entity.Property(r => r.Method).IsRequired().HasMaxLength(10);\n+            entity.Property(r => r.Path).IsRequired().HasMaxLength(500);\n+            entity.Property(r => r.IpAddress).HasMaxLength(45);\n+            entity.Property(r => r.ErrorMessage).HasMaxLength(1000);\n+\n+            // Indexes for performance\n+            entity.HasIndex(r => r.QueueId).IsUnique();\n+            entity.HasIndex(r => r.RequestId);\n+            entity.HasIndex(r => r.TenantId);\n+            entity.HasIndex(r => new { r.TenantId, r.Status });\n+            entity.HasIndex(r => r.QueuedAt);\n+            entity.HasIndex(r => r.CreatedAt);\n+\n+            // Foreign key relationships\n+            entity.HasOne(r => r.Tenant)\n+                  .WithMany()\n+                  .HasForeignKey(r => r.TenantId)\n+                  .OnDelete(DeleteBehavior.SetNull);\n+\n+            entity.HasOne(r => r.User)\n+                  .WithMany()\n+                  .HasForeignKey(r => r.UserId)\n+                  .OnDelete(DeleteBehavior.SetNull);\n+        });\n+    }\n+\n     private void SeedDefaultData(ModelBuilder modelBuilder)\n     {\n         // Seed default permissions\n         var permissions = SystemPermissions.GetDefaultPermissions();\n"}, {"date": 1751237886343, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -21,12 +21,12 @@\n     public DbSet<RolePermission> RolePermissions { get; set; }\n     public DbSet<TenantPlugin> TenantPlugins { get; set; }\n \n     // Notification Management\n-    public DbSet<NotificationLog> NotificationLogs { get; set; }\n-    public DbSet<MessageTemplate> MessageTemplates { get; set; }\n-    public DbSet<ScheduledMessage> ScheduledMessages { get; set; }\n-    public DbSet<WebhookEndpoint> WebhookEndpoints { get; set; }\n+    public DbSet<PluginCore.Models.NotificationLog> NotificationLogs { get; set; }\n+    public DbSet<PluginCore.Models.MessageTemplate> MessageTemplates { get; set; }\n+    public DbSet<PluginCore.Models.ScheduledMessage> ScheduledMessages { get; set; }\n+    public DbSet<PluginCore.Models.WebhookEndpoint> WebhookEndpoints { get; set; }\n \n     // Audit and Queue Management\n     public DbSet<RequestAuditLog> RequestAuditLogs { get; set; }\n \n"}, {"date": 1751237911467, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -189,9 +189,9 @@\n     }\n \n     private void ConfigureNotificationEntities(ModelBuilder modelBuilder)\n     {\n-        modelBuilder.Entity<NotificationLog>(entity =>\n+        modelBuilder.Entity<PluginCore.Models.NotificationLog>(entity =>\n         {\n             entity.HasKey(n => n.Id);\n             entity.Property(n => n.TenantId).IsRequired();\n             entity.Property(n => n.MessageId).IsRequired().HasMaxLength(100);\n@@ -200,21 +200,21 @@\n             entity.Property(n => n.Subject).HasMaxLength(500);\n             entity.Property(n => n.Provider).HasMaxLength(100);\n             entity.Property(n => n.CorrelationId).HasMaxLength(50);\n             entity.Property(n => n.UserId).HasMaxLength(100);\n-            \n+\n             entity.HasIndex(n => n.TenantId);\n             entity.HasIndex(n => n.MessageId);\n             entity.HasIndex(n => new { n.TenantId, n.Status });\n-            \n+\n             entity.OwnsOne(n => n.Metadata, metadata =>\n             {\n                 metadata.ToJson();\n             });\n         });\n \n         // Add other notification entities as needed\n-        modelBuilder.Entity<MessageTemplate>(entity =>\n+        modelBuilder.Entity<PluginCore.Models.MessageTemplate>(entity =>\n         {\n             entity.HasKey(mt => mt.Id);\n             entity.Property(mt => mt.TenantId).IsRequired();\n             entity.Property(mt => mt.Name).IsRequired().HasMaxLength(100);\n"}, {"date": 1751237945553, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -223,28 +223,28 @@\n             \n             entity.HasIndex(mt => new { mt.TenantId, mt.Name }).IsUnique();\n         });\n \n-        modelBuilder.Entity<ScheduledMessage>(entity =>\n+        modelBuilder.Entity<PluginCore.Models.ScheduledMessage>(entity =>\n         {\n             entity.HasKey(sm => sm.Id);\n             entity.Property(sm => sm.TenantId).IsRequired();\n             entity.Property(sm => sm.MessageId).IsRequired().HasMaxLength(100);\n             entity.Property(sm => sm.Channel).IsRequired().HasMaxLength(50);\n             entity.Property(sm => sm.Recipient).IsRequired().HasMaxLength(255);\n-            \n+\n             entity.HasIndex(sm => sm.TenantId);\n             entity.HasIndex(sm => new { sm.TenantId, sm.ScheduledTime });\n         });\n \n-        modelBuilder.Entity<WebhookEndpoint>(entity =>\n+        modelBuilder.Entity<PluginCore.Models.WebhookEndpoint>(entity =>\n         {\n             entity.HasKey(we => we.Id);\n             entity.Property(we => we.TenantId).IsRequired();\n             entity.Property(we => we.Name).IsRequired().HasMaxLength(100);\n             entity.Property(we => we.Url).IsRequired().HasMaxLength(500);\n             entity.Property(we => we.Secret).HasMaxLength(100);\n-            \n+\n             entity.HasIndex(we => new { we.TenantId, we.Name }).IsUnique();\n         });\n     }\n \n"}, {"date": 1751238385979, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,9 @@\n \n /// <summary>\n /// Main database context for NotifyMaster with multi-tenancy support\n /// </summary>\n-public class NotifyMasterDbContext : DbContext\n+public class NotifyMasterDbContext : DbContext, INotifyMasterDbContext\n {\n     public NotifyMasterDbContext(DbContextOptions<NotifyMasterDbContext> options) : base(options)\n     {\n     }\n"}], "date": 1751218231645, "name": "Commit-0", "content": "using Microsoft.EntityFrameworkCore;\nusing PluginCore.Models;\n\nnamespace NotifyMaster.Database;\n\n/// <summary>\n/// Main database context for NotifyMaster with multi-tenancy support\n/// </summary>\npublic class NotifyMasterDbContext : DbContext\n{\n    public NotifyMasterDbContext(DbContextOptions<NotifyMasterDbContext> options) : base(options)\n    {\n    }\n\n    // Tenant and User Management\n    public DbSet<Tenant> Tenants { get; set; }\n    public DbSet<User> Users { get; set; }\n    public DbSet<Role> Roles { get; set; }\n    public DbSet<Permission> Permissions { get; set; }\n    public DbSet<UserRole> UserRoles { get; set; }\n    public DbSet<UserPermission> UserPermissions { get; set; }\n    public DbSet<RolePermission> RolePermissions { get; set; }\n    public DbSet<TenantPlugin> TenantPlugins { get; set; }\n\n    // Notification Management\n    public DbSet<NotificationLog> NotificationLogs { get; set; }\n    public DbSet<MessageTemplate> MessageTemplates { get; set; }\n    public DbSet<ScheduledMessage> ScheduledMessages { get; set; }\n    public DbSet<WebhookEndpoint> WebhookEndpoints { get; set; }\n\n    protected override void OnModelCreating(ModelBuilder modelBuilder)\n    {\n        base.OnModelCreating(modelBuilder);\n\n        // Configure tenant entity\n        ConfigureTenant(modelBuilder);\n        \n        // Configure user management entities\n        ConfigureUserManagement(modelBuilder);\n        \n        // Configure notification entities\n        ConfigureNotificationEntities(modelBuilder);\n        \n        // Seed default data\n        SeedDefaultData(modelBuilder);\n    }\n\n    private void ConfigureTenant(ModelBuilder modelBuilder)\n    {\n        modelBuilder.Entity<Tenant>(entity =>\n        {\n            entity.HasKey(t => t.Id);\n            entity.Property(t => t.Name).IsRequired().HasMaxLength(100);\n            entity.Property(t => t.Domain).IsRequired().HasMaxLength(100);\n            entity.HasIndex(t => t.Domain).IsUnique();\n            entity.Property(t => t.Description).HasMaxLength(500);\n            \n            // Configure complex properties as JSON\n            entity.OwnsOne(t => t.Settings, settings =>\n            {\n                settings.ToJson();\n            });\n            \n            entity.OwnsOne(t => t.Limits, limits =>\n            {\n                limits.ToJson();\n            });\n            \n            entity.OwnsOne(t => t.Usage, usage =>\n            {\n                usage.ToJson();\n            });\n        });\n\n        modelBuilder.Entity<TenantPlugin>(entity =>\n        {\n            entity.HasKey(tp => tp.Id);\n            entity.Property(tp => tp.TenantId).IsRequired();\n            entity.Property(tp => tp.PluginName).IsRequired().HasMaxLength(100);\n            \n            entity.HasOne(tp => tp.Tenant)\n                  .WithMany(t => t.Plugins)\n                  .HasForeignKey(tp => tp.TenantId)\n                  .OnDelete(DeleteBehavior.Cascade);\n                  \n            entity.OwnsOne(tp => tp.Configuration, config =>\n            {\n                config.ToJson();\n            });\n            \n            entity.HasIndex(tp => new { tp.TenantId, tp.PluginName }).IsUnique();\n        });\n    }\n\n    private void ConfigureUserManagement(ModelBuilder modelBuilder)\n    {\n        modelBuilder.Entity<User>(entity =>\n        {\n            entity.HasKey(u => u.Id);\n            entity.Property(u => u.TenantId).IsRequired();\n            entity.Property(u => u.Username).IsRequired().HasMaxLength(100);\n            entity.Property(u => u.Email).IsRequired().HasMaxLength(255);\n            entity.Property(u => u.PasswordHash).IsRequired();\n            entity.Property(u => u.FirstName).HasMaxLength(100);\n            entity.Property(u => u.LastName).HasMaxLength(100);\n            \n            entity.HasOne(u => u.Tenant)\n                  .WithMany(t => t.Users)\n                  .HasForeignKey(u => u.TenantId)\n                  .OnDelete(DeleteBehavior.Cascade);\n                  \n            entity.HasIndex(u => new { u.TenantId, u.Email }).IsUnique();\n            entity.HasIndex(u => new { u.TenantId, u.Username }).IsUnique();\n            \n            entity.OwnsOne(u => u.Profile, profile =>\n            {\n                profile.ToJson();\n            });\n        });\n\n        modelBuilder.Entity<Role>(entity =>\n        {\n            entity.HasKey(r => r.Id);\n            entity.Property(r => r.Name).IsRequired().HasMaxLength(100);\n            entity.Property(r => r.Description).HasMaxLength(500);\n            entity.HasIndex(r => new { r.Name, r.Scope }).IsUnique();\n        });\n\n        modelBuilder.Entity<Permission>(entity =>\n        {\n            entity.HasKey(p => p.Id);\n            entity.Property(p => p.Name).IsRequired().HasMaxLength(100);\n            entity.Property(p => p.Description).HasMaxLength(500);\n            entity.Property(p => p.Resource).IsRequired().HasMaxLength(50);\n            entity.Property(p => p.Action).IsRequired().HasMaxLength(50);\n            entity.HasIndex(p => new { p.Resource, p.Action }).IsUnique();\n        });\n\n        // Configure many-to-many relationships\n        modelBuilder.Entity<UserRole>(entity =>\n        {\n            entity.HasKey(ur => new { ur.UserId, ur.RoleId });\n            \n            entity.HasOne(ur => ur.User)\n                  .WithMany(u => u.Roles)\n                  .HasForeignKey(ur => ur.UserId)\n                  .OnDelete(DeleteBehavior.Cascade);\n                  \n            entity.HasOne(ur => ur.Role)\n                  .WithMany(r => r.Users)\n                  .HasForeignKey(ur => ur.RoleId)\n                  .OnDelete(DeleteBehavior.Cascade);\n        });\n\n        modelBuilder.Entity<UserPermission>(entity =>\n        {\n            entity.HasKey(up => new { up.UserId, up.PermissionId });\n            \n            entity.HasOne(up => up.User)\n                  .WithMany(u => u.Permissions)\n                  .HasForeignKey(up => up.UserId)\n                  .OnDelete(DeleteBehavior.Cascade);\n                  \n            entity.HasOne(up => up.Permission)\n                  .WithMany(p => p.Users)\n                  .HasForeignKey(up => up.PermissionId)\n                  .OnDelete(DeleteBehavior.Cascade);\n        });\n\n        modelBuilder.Entity<RolePermission>(entity =>\n        {\n            entity.HasKey(rp => new { rp.RoleId, rp.PermissionId });\n            \n            entity.HasOne(rp => rp.Role)\n                  .WithMany(r => r.Permissions)\n                  .HasForeignKey(rp => rp.RoleId)\n                  .OnDelete(DeleteBehavior.Cascade);\n                  \n            entity.HasOne(rp => rp.Permission)\n                  .WithMany(p => p.Roles)\n                  .HasForeignKey(rp => rp.PermissionId)\n                  .OnDelete(DeleteBehavior.Cascade);\n        });\n    }\n\n    private void ConfigureNotificationEntities(ModelBuilder modelBuilder)\n    {\n        modelBuilder.Entity<NotificationLog>(entity =>\n        {\n            entity.HasKey(n => n.Id);\n            entity.Property(n => n.TenantId).IsRequired();\n            entity.Property(n => n.MessageId).IsRequired().HasMaxLength(100);\n            entity.Property(n => n.Channel).IsRequired().HasMaxLength(50);\n            entity.Property(n => n.Recipient).IsRequired().HasMaxLength(255);\n            entity.Property(n => n.Subject).HasMaxLength(500);\n            entity.Property(n => n.Provider).HasMaxLength(100);\n            entity.Property(n => n.CorrelationId).HasMaxLength(50);\n            entity.Property(n => n.UserId).HasMaxLength(100);\n            \n            entity.HasIndex(n => n.TenantId);\n            entity.HasIndex(n => n.MessageId);\n            entity.HasIndex(n => new { n.TenantId, n.Status });\n            \n            entity.OwnsOne(n => n.Metadata, metadata =>\n            {\n                metadata.ToJson();\n            });\n        });\n\n        // Add other notification entities as needed\n        modelBuilder.Entity<MessageTemplate>(entity =>\n        {\n            entity.HasKey(mt => mt.Id);\n            entity.Property(mt => mt.TenantId).IsRequired();\n            entity.Property(mt => mt.Name).IsRequired().HasMaxLength(100);\n            entity.Property(mt => mt.Subject).HasMaxLength(500);\n            entity.Property(mt => mt.Content).IsRequired();\n            \n            entity.HasIndex(mt => new { mt.TenantId, mt.Name }).IsUnique();\n        });\n\n        modelBuilder.Entity<ScheduledMessage>(entity =>\n        {\n            entity.HasKey(sm => sm.Id);\n            entity.Property(sm => sm.TenantId).IsRequired();\n            entity.Property(sm => sm.MessageId).IsRequired().HasMaxLength(100);\n            entity.Property(sm => sm.Channel).IsRequired().HasMaxLength(50);\n            entity.Property(sm => sm.Recipient).IsRequired().HasMaxLength(255);\n            \n            entity.HasIndex(sm => sm.TenantId);\n            entity.HasIndex(sm => new { sm.TenantId, sm.ScheduledTime });\n        });\n\n        modelBuilder.Entity<WebhookEndpoint>(entity =>\n        {\n            entity.HasKey(we => we.Id);\n            entity.Property(we => we.TenantId).IsRequired();\n            entity.Property(we => we.Name).IsRequired().HasMaxLength(100);\n            entity.Property(we => we.Url).IsRequired().HasMaxLength(500);\n            entity.Property(we => we.Secret).HasMaxLength(100);\n            \n            entity.HasIndex(we => new { we.TenantId, we.Name }).IsUnique();\n        });\n    }\n\n    private void SeedDefaultData(ModelBuilder modelBuilder)\n    {\n        // Seed default permissions\n        var permissions = SystemPermissions.GetDefaultPermissions();\n        var permissionEntities = permissions.Select((p, index) => new Permission\n        {\n            Id = Guid.NewGuid().ToString(),\n            Name = p.Name,\n            Description = p.Description,\n            Resource = p.Resource,\n            Action = p.Action,\n            IsSystemPermission = true\n        }).ToArray();\n\n        modelBuilder.Entity<Permission>().HasData(permissionEntities);\n\n        // Seed default roles\n        var roles = SystemRoles.GetDefaultRoles();\n        var roleEntities = roles.Select(r => new Role\n        {\n            Id = Guid.NewGuid().ToString(),\n            Name = r.Name,\n            Description = r.Description,\n            Scope = r.Scope,\n            IsSystemRole = true,\n            CreatedAt = DateTime.UtcNow\n        }).ToArray();\n\n        modelBuilder.Entity<Role>().HasData(roleEntities);\n\n        // Seed role permissions\n        var rolePermissions = new List<RolePermission>();\n        foreach (var role in roles)\n        {\n            var roleEntity = roleEntities.First(r => r.Name == role.Name);\n            foreach (var permissionName in role.Permissions)\n            {\n                var permissionEntity = permissionEntities.FirstOrDefault(p => p.Name == permissionName);\n                if (permissionEntity != null)\n                {\n                    rolePermissions.Add(new RolePermission\n                    {\n                        RoleId = roleEntity.Id,\n                        PermissionId = permissionEntity.Id,\n                        AssignedAt = DateTime.UtcNow\n                    });\n                }\n            }\n        }\n\n        modelBuilder.Entity<RolePermission>().HasData(rolePermissions);\n    }\n}\n\n\n"}]}