// Using statements are handled by GlobalUsings.cs

namespace NotifyMaster.Core.Services;

/// <summary>
/// Service responsible for authorization and permission checking
/// </summary>
public interface IAuthorizationService
{
    /// <summary>
    /// Checks if a user has permission to perform an action on a resource
    /// </summary>
    Task<bool> HasPermissionAsync(string userId, string tenantId, string resource, string action, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Checks if a user has a specific role
    /// </summary>
    Task<bool> HasRoleAsync(string userId, string tenantId, string roleName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all permissions for a user
    /// </summary>
    Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets all roles for a user
    /// </summary>
    Task<IReadOnlyList<Role>> GetUserRolesAsync(string userId, string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Checks if a user can access a tenant
    /// </summary>
    Task<bool> CanAccessTenantAsync(string userId, string tenantId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Checks if a user is a system administrator
    /// </summary>
    Task<bool> IsSystemAdminAsync(string userId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of authorization service
/// </summary>
public class AuthorizationService : IAuthorizationService
{
    private readonly IUserService _userService;
    private readonly ITenantService _tenantService;
    private readonly ILogger<AuthorizationService> _logger;

    public AuthorizationService(
        IUserService userService,
        ITenantService tenantService,
        ILogger<AuthorizationService> logger)
    {
        _userService = userService;
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<bool> HasPermissionAsync(string userId, string tenantId, string resource, string action, CancellationToken cancellationToken = default)
    {
        try
        {
            // System admins have all permissions
            if (await IsSystemAdminAsync(userId, cancellationToken))
            {
                return true;
            }

            // Check if user can access the tenant
            if (!await CanAccessTenantAsync(userId, tenantId, cancellationToken))
            {
                return false;
            }

            // Check specific permission
            return await _userService.HasPermissionAsync(userId, tenantId, resource, action, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission {Resource}:{Action} for user {UserId} in tenant {TenantId}", 
                resource, action, userId, tenantId);
            return false;
        }
    }

    public async Task<bool> HasRoleAsync(string userId, string tenantId, string roleName, CancellationToken cancellationToken = default)
    {
        try
        {
            // System admins have all roles
            if (await IsSystemAdminAsync(userId, cancellationToken))
            {
                return true;
            }

            // Check if user can access the tenant
            if (!await CanAccessTenantAsync(userId, tenantId, cancellationToken))
            {
                return false;
            }

            // Check specific role
            return await _userService.HasRoleAsync(userId, tenantId, roleName, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking role {RoleName} for user {UserId} in tenant {TenantId}", 
                roleName, userId, tenantId);
            return false;
        }
    }

    public async Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if user can access the tenant
            if (!await CanAccessTenantAsync(userId, tenantId, cancellationToken))
            {
                return Array.Empty<Permission>();
            }

            return await _userService.GetUserPermissionsAsync(userId, tenantId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permissions for user {UserId} in tenant {TenantId}", userId, tenantId);
            return Array.Empty<Permission>();
        }
    }

    public async Task<IReadOnlyList<Role>> GetUserRolesAsync(string userId, string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check if user can access the tenant
            if (!await CanAccessTenantAsync(userId, tenantId, cancellationToken))
            {
                return Array.Empty<Role>();
            }

            return await _userService.GetUserRolesAsync(userId, tenantId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for user {UserId} in tenant {TenantId}", userId, tenantId);
            return Array.Empty<Role>();
        }
    }

    public async Task<bool> CanAccessTenantAsync(string userId, string tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            // System admins can access all tenants
            if (await IsSystemAdminAsync(userId, cancellationToken))
            {
                return true;
            }

            // Get user and check if they belong to the tenant
            var user = await _userService.GetUserAsync(userId, cancellationToken);
            if (user == null)
            {
                return false;
            }

            // Check if user belongs to the tenant
            return user.TenantId == tenantId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking tenant access for user {UserId} in tenant {TenantId}", userId, tenantId);
            return false;
        }
    }

    public async Task<bool> IsSystemAdminAsync(string userId, CancellationToken cancellationToken = default)
    {
        try
        {
            var user = await _userService.GetUserAsync(userId, cancellationToken);
            if (user == null)
            {
                return false;
            }

            // Check if user has SuperAdmin role with system scope
            var roles = await _userService.GetUserRolesAsync(userId, user.TenantId, cancellationToken);
            return roles.Any(r => r.Name == NotifyMaster.Core.Models.SystemRoles.SuperAdmin && r.Scope == RoleScope.System);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking system admin status for user {UserId}", userId);
            return false;
        }
    }
}
