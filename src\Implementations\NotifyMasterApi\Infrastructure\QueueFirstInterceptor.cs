using FluentValidation.Results;
using NotifyMaster.Core.Interfaces;
using NotifyMaster.Entities;

namespace NotifyMasterApi.Infrastructure;

/// <summary>
/// FastEndpoints interceptor that implements queue-first architecture
/// Pattern: Endpoint → Queue → Database → Response
/// </summary>
public class QueueFirstInterceptor : IPreProcessor<object>
{
    private readonly IQueueService _queueService;
    private readonly NotifyMasterDbContext _dbContext;
    private readonly ITenantContext _tenantContext;
    private readonly ILogger<QueueFirstInterceptor> _logger;

    public QueueFirstInterceptor(
        IQueueService queueService,
        NotifyMasterDbContext dbContext,
        ITenantContext tenantContext,
        ILogger<QueueFirstInterceptor> logger)
    {
        _queueService = queueService;
        _dbContext = dbContext;
        _tenantContext = tenantContext;
        _logger = logger;
    }

    public async Task PreProcessAsync(object req, HttpContext ctx, List<ValidationFailure> failures, CancellationToken ct)
    {
        try
        {
            // Skip queue processing for certain endpoints
            if (ShouldSkipQueueProcessing(ctx))
            {
                return;
            }

            // Create queue message from request
            var queueMessage = CreateQueueMessage(req, ctx);

            // Dual-write pattern: Queue + Lightweight Audit Log (atomic operation)
            using var transaction = await _dbContext.Database.BeginTransactionAsync(ct);

            try
            {
                // Step 1: Write lightweight audit log first (fast insert)
                await WriteLightweightAuditLog(queueMessage, ct);

                // Step 2: Queue the request
                var queueResult = await _queueService.EnqueueAsync("api-requests", queueMessage, ct);
                if (!queueResult.IsSuccess)
                {
                    await transaction.RollbackAsync(ct);
                    _logger.LogError("Failed to queue request: {Error}", queueResult.Message);
                    failures.Add(new ValidationFailure("Queue", "Failed to queue request for processing"));
                    return;
                }

                // Step 3: Commit transaction (both audit log and queue successful)
                await transaction.CommitAsync(ct);

                // Step 4: Set queue ID in context for response tracking
                ctx.Items["QueueId"] = queueMessage.Id;
                ctx.Items["QueuedAt"] = queueMessage.QueuedAt;

                _logger.LogInformation("Request {RequestId} queued and logged successfully with ID {QueueId}",
                    queueMessage.RequestId, queueMessage.Id);
            }
            catch (Exception)
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in queue-first interceptor");
            failures.Add(new ValidationFailure("Queue", "Internal error processing request"));
        }
    }

    private static bool ShouldSkipQueueProcessing(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        
        // Skip queue processing for these endpoints
        var skipPaths = new[]
        {
            "/health",
            "/api/health",
            "/hangfire",
            "/scalar",
            "/api/config",
            "/favicon.ico",
            "/api/auth/login",
            "/api/auth/refresh",
            "/api/setup"
        };

        return skipPaths.Any(skipPath => path?.StartsWith(skipPath) == true);
    }

    private QueuedApiRequest CreateQueueMessage(object request, HttpContext context)
    {
        var requestId = Guid.NewGuid().ToString();
        var correlationId = context.Request.Headers["X-Correlation-ID"].FirstOrDefault() ?? requestId;

        return new QueuedApiRequest
        {
            Id = Guid.NewGuid().ToString(),
            RequestId = requestId,
            CorrelationId = correlationId,
            TenantId = _tenantContext.TenantId,
            UserId = _tenantContext.UserId,
            Method = context.Request.Method,
            Path = context.Request.Path,
            QueryString = context.Request.QueryString.Value,
            Headers = context.Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()),
            RequestBody = JsonSerializer.Serialize(request),
            UserAgent = context.Request.Headers.UserAgent.ToString(),
            IpAddress = context.Connection.RemoteIpAddress?.ToString(),
            QueuedAt = DateTime.UtcNow,
            Status = QueuedRequestStatus.Queued,
            Priority = DetermineRequestPriority(context),
            Metadata = new Dictionary<string, object>
            {
                ["ContentType"] = context.Request.ContentType ?? "application/json",
                ["ContentLength"] = context.Request.ContentLength ?? 0,
                ["Scheme"] = context.Request.Scheme,
                ["Host"] = context.Request.Host.Value ?? "",
                ["Protocol"] = context.Request.Protocol
            }
        };
    }



    private static RequestPriority DetermineRequestPriority(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        
        // High priority paths
        if (path?.Contains("/emergency") == true || path?.Contains("/urgent") == true)
            return RequestPriority.High;
            
        // Low priority paths
        if (path?.Contains("/metrics") == true || path?.Contains("/analytics") == true)
            return RequestPriority.Low;
            
        return RequestPriority.Normal;
    }

    /// <summary>
    /// Write lightweight audit log for immediate request tracking
    /// Uses implicit conversion for clean, maintainable code
    /// </summary>
    private async Task WriteLightweightAuditLog(QueuedApiRequest queueMessage, CancellationToken ct)
    {
        // Implicit conversion from QueuedApiRequest to RequestAuditLog
        RequestAuditLog auditLog = queueMessage;

        _dbContext.RequestAuditLogs.Add(auditLog);
        await _dbContext.SaveChangesAsync(ct);

        _logger.LogDebug("Lightweight audit log created for queue ID {QueueId}", queueMessage.Id);
    }
}

/// <summary>
/// Post-processor to update audit log and add queue information to response headers
/// </summary>
public class QueueFirstPostProcessor : IPostProcessor<object, object>
{
    private readonly NotifyMasterDbContext _dbContext;
    private readonly ILogger<QueueFirstPostProcessor> _logger;

    public QueueFirstPostProcessor(NotifyMasterDbContext dbContext, ILogger<QueueFirstPostProcessor> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task PostProcessAsync(object req, object res, HttpContext ctx, IReadOnlyCollection<ValidationFailure> failures, CancellationToken ct)
    {
        try
        {
            var queueId = ctx.Items["QueueId"] as string;
            if (string.IsNullOrEmpty(queueId))
                return;

            var queuedAt = ctx.Items["QueuedAt"] as DateTime?;
            var processingTime = queuedAt.HasValue ? DateTime.UtcNow - queuedAt.Value : TimeSpan.Zero;

            // Update audit log with completion information
            await UpdateAuditLogAsync(queueId, ctx, failures, processingTime, ct);

            // Add queue information to response headers
            ctx.Response.Headers.Add("X-Queue-Id", queueId);
            ctx.Response.Headers.Add("X-Processing-Time-Ms", processingTime.TotalMilliseconds.ToString("F0"));
            ctx.Response.Headers.Add("X-Queued-At", queuedAt?.ToString("O") ?? "");
            ctx.Response.Headers.Add("X-Queue-Status", failures.Any() ? "Failed" : "Completed");

            _logger.LogDebug("Updated audit log and added queue headers for queue ID {QueueId}", queueId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in queue-first post-processor");
            // Don't throw here - we don't want to fail the response
        }
    }

    private async Task UpdateAuditLogAsync(string queueId, HttpContext context, IReadOnlyCollection<ValidationFailure> failures, TimeSpan processingTime, CancellationToken ct)
    {
        try
        {
            var auditLog = await _dbContext.RequestAuditLogs
                .FirstOrDefaultAsync(log => log.QueueId == queueId, ct);

            if (auditLog != null)
            {
                auditLog.Status = failures.Any() ? QueuedRequestStatus.Failed : QueuedRequestStatus.Completed;
                auditLog.StatusCode = context.Response.StatusCode;
                auditLog.ProcessedAt = DateTime.UtcNow;
                auditLog.ProcessingTimeMs = (int)processingTime.TotalMilliseconds;
                auditLog.ResponseSize = context.Response.ContentLength;

                if (failures.Any())
                {
                    auditLog.ErrorMessage = string.Join("; ", failures.Select(f => f.ErrorMessage));
                    auditLog.ErrorDetails = JsonSerializer.Serialize(failures.Select(f => new
                    {
                        f.PropertyName,
                        f.ErrorMessage,
                        f.ErrorCode
                    }));
                }

                await _dbContext.SaveChangesAsync(ct);
                _logger.LogDebug("Updated audit log for queue ID {QueueId} with status {Status}", queueId, auditLog.Status);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update audit log for queue ID {QueueId}", queueId);
            // Don't throw - audit log update failure shouldn't fail the response
        }
    }
}

/// <summary>
/// Models for queue-first architecture
/// </summary>
public class QueuedApiRequest
{
    public string Id { get; set; } = string.Empty;
    public string RequestId { get; set; } = string.Empty;
    public string CorrelationId { get; set; } = string.Empty;
    public string? TenantId { get; set; }
    public string? UserId { get; set; }
    public string Method { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string? QueryString { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
    public string RequestBody { get; set; } = string.Empty;
    public string? UserAgent { get; set; }
    public string? IpAddress { get; set; }
    public DateTime QueuedAt { get; set; }
    public QueuedRequestStatus Status { get; set; }
    public RequestPriority Priority { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Implicit conversion from QueuedApiRequest to RequestAuditLog entity
    /// Creates a lightweight audit log with essential fields only
    /// </summary>
    public static implicit operator RequestAuditLog(QueuedApiRequest queueRequest)
    {
        return new RequestAuditLog
        {
            Id = Guid.NewGuid().ToString(),
            QueueId = queueRequest.Id,
            RequestId = queueRequest.RequestId,
            CorrelationId = queueRequest.CorrelationId,
            TenantId = queueRequest.TenantId,
            UserId = queueRequest.UserId,
            Method = queueRequest.Method,
            Path = queueRequest.Path,
            IpAddress = queueRequest.IpAddress,
            QueuedAt = queueRequest.QueuedAt,
            Status = queueRequest.Status,
            CreatedAt = DateTime.UtcNow
            // Intentionally skip heavy fields like RequestBody and Metadata for performance
        };
    }

    /// <summary>
    /// Explicit conversion from RequestAuditLog entity back to QueuedApiRequest
    /// Useful for reprocessing or debugging scenarios
    /// </summary>
    public static explicit operator QueuedApiRequest(RequestAuditLog auditLog)
    {
        return new QueuedApiRequest
        {
            Id = auditLog.QueueId,
            RequestId = auditLog.RequestId,
            CorrelationId = auditLog.CorrelationId ?? string.Empty,
            TenantId = auditLog.TenantId,
            UserId = auditLog.UserId,
            Method = auditLog.Method,
            Path = auditLog.Path,
            IpAddress = auditLog.IpAddress,
            QueuedAt = auditLog.QueuedAt,
            Status = auditLog.Status,
            // Note: Headers, RequestBody, and Metadata are not stored in lightweight audit log
            Headers = new Dictionary<string, string>(),
            RequestBody = string.Empty,
            Metadata = new Dictionary<string, object>()
        };
    }
}

public enum QueuedRequestStatus
{
    Queued,
    Processing,
    Completed,
    Failed,
    Cancelled
}

public enum RequestPriority
{
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
}

public enum RequestStatus
{
    Queued,
    Processing,
    Completed,
    Failed,
    Cancelled
}
