@inject IAuthService AuthService
@inject IPluginService PluginService
@inject IBreadcrumbService BreadcrumbService
@inject NavigationManager Navigation
@implements IDisposable

<MudNavMenu Class="sidebar-nav">
    <!-- Dashboard -->
    @if (_hasPermission("dashboard.view"))
    {
        <MudNavLink Href="/dashboard"
                    Icon="@Icons.Material.Filled.Dashboard"
                    Match="NavLinkMatch.All"
                    OnClick="@(() => SetBreadcrumb("Dashboard", Icons.Material.Filled.Dashboard))">
            <span class="nav-text">Dashboard</span>
        </MudNavLink>
    }

    <!-- Tenant Management -->
    @if (_hasPermission("tenants.view"))
    {
        <MudNavGroup Text="Tenant Management"
                     Icon="@Icons.Material.Filled.Business"
                     Expanded="@_expandedGroups.Contains("tenants")"
                     ExpandedChanged="@((bool expanded) => ToggleGroup("tenants", expanded))">
            <MudNavLink Href="/tenants"
                        Icon="@Icons.Material.Filled.List"
                        OnClick="@(() => SetBreadcrumb("Tenants", Icons.Material.Filled.Business, "/tenants"))">
                <span class="nav-text">All Tenants</span>
            </MudNavLink>
            @if (_hasPermission("tenants.create"))
            {
                <MudNavLink Href="/tenants/create"
                            Icon="@Icons.Material.Filled.Add"
                            OnClick="@(() => SetBreadcrumb("Create Tenant", Icons.Material.Filled.Add, "/tenants/create"))">
                    <span class="nav-text">Create Tenant</span>
                </MudNavLink>
            }
        </MudNavGroup>
    }

    <!-- User & Role Management -->
    @if (_hasPermission("users.view") || _hasPermission("roles.view"))
    {
        <MudNavGroup Text="User & Role Management"
                     Icon="@Icons.Material.Filled.People"
                     Expanded="@_expandedGroups.Contains("users")"
                     ExpandedChanged="@((bool expanded) => ToggleGroup("users", expanded))">
            @if (_hasPermission("users.view"))
            {
                <MudNavLink Href="/users"
                            Icon="@Icons.Material.Filled.People"
                            OnClick="@(() => SetBreadcrumb("Users", Icons.Material.Filled.People, "/users"))">
                    <span class="nav-text">Users</span>
                </MudNavLink>
            }
            @if (_hasPermission("roles.view"))
            {
                <MudNavLink Href="/roles"
                            Icon="@Icons.Material.Filled.AdminPanelSettings"
                            OnClick="@(() => SetBreadcrumb("Roles", Icons.Material.Filled.AdminPanelSettings, "/roles"))">
                    <span class="nav-text">Roles & Permissions</span>
                </MudNavLink>
            }
        </MudNavGroup>
    }

    <!-- Plugin Management -->
    @if (_hasPermission("plugins.view"))
    {
        <MudNavLink Href="/plugins"
                    Icon="@Icons.Material.Filled.Extension"
                    OnClick="@(() => SetBreadcrumb("Plugins", Icons.Material.Filled.Extension, "/plugins"))">
            <span class="nav-text">Plugin Management</span>
            @if (_availablePlugins.Any())
            {
                <MudChip T="string" Size="Size.Small"
                         Color="Color.Success"
                         Variant="Variant.Filled"
                         Class="ml-auto plugin-count-chip">
                    @_availablePlugins.Count
                </MudChip>
            }
        </MudNavLink>
    }

    <!-- Messaging -->
    @if (_hasPermission("templates.view") || _hasAnyMessagingPlugin)
    {
        <MudNavGroup Text="Messaging"
                     Icon="@Icons.Material.Filled.Message"
                     Expanded="@_expandedGroups.Contains("messaging")"
                     ExpandedChanged="@((bool expanded) => ToggleGroup("messaging", expanded))">
            @if (_hasPermission("templates.view"))
            {
                <MudNavLink Href="/templates"
                            Icon="@Icons.Material.Filled.Description"
                            OnClick="@(() => SetBreadcrumb("Templates", Icons.Material.Filled.Description, "/templates"))">
                    <span class="nav-text">Templates</span>
                </MudNavLink>
            }

            <!-- Quick Send Options (only show if plugins are available) -->
            @if (_hasEmailPlugin)
            {
                <MudNavLink Href="/send/email"
                            Icon="@Icons.Material.Filled.Email"
                            OnClick="@(() => SetBreadcrumb("Send Email", Icons.Material.Filled.Email, "/send/email"))">
                    <span class="nav-text">Send Email</span>
                </MudNavLink>
            }
            @if (_hasSmsPlugin)
            {
                <MudNavLink Href="/send/sms"
                            Icon="@Icons.Material.Filled.Sms"
                            OnClick="@(() => SetBreadcrumb("Send SMS", Icons.Material.Filled.Sms, "/send/sms"))">
                    <span class="nav-text">Send SMS</span>
                </MudNavLink>
            }
            @if (_hasPushPlugin)
            {
                <MudNavLink Href="/send/push"
                            Icon="@Icons.Material.Filled.NotificationsActive"
                            OnClick="@(() => SetBreadcrumb("Send Push", Icons.Material.Filled.NotificationsActive, "/send/push"))">
                    <span class="nav-text">Send Push</span>
                </MudNavLink>
            }
            @if (_hasWhatsAppPlugin)
            {
                <MudNavLink Href="/send/whatsapp"
                            Icon="@Icons.Material.Filled.Chat"
                            OnClick="@(() => SetBreadcrumb("Send WhatsApp", Icons.Material.Filled.Chat, "/send/whatsapp"))">
                    <span class="nav-text">Send WhatsApp</span>
                </MudNavLink>
            }
            @if (_hasSlackPlugin)
            {
                <MudNavLink Href="/send/slack"
                            Icon="@Icons.Material.Filled.Forum"
                            OnClick="@(() => SetBreadcrumb("Send Slack", Icons.Material.Filled.Forum, "/send/slack"))">
                    <span class="nav-text">Send Slack</span>
                </MudNavLink>
            }
        </MudNavGroup>
    }

    <!-- API Keys -->
    @if (_hasPermission("apikeys.view"))
    {
        <MudNavLink Href="/apikeys"
                    Icon="@Icons.Material.Filled.Key"
                    OnClick="@(() => SetBreadcrumb("API Keys", Icons.Material.Filled.Key, "/apikeys"))">
            <span class="nav-text">API Keys</span>
        </MudNavLink>
    }

    <!-- Monitoring -->
    @if (_hasPermission("logs.view") || _hasPermission("audit.view") || _hasPermission("events.view"))
    {
        <MudNavGroup Text="Monitoring" Icon="@Icons.Material.Filled.Monitoring" Expanded="false">
            @if (_hasPermission("events.view"))
            {
                <MudNavLink Href="/events" Icon="@Icons.Material.Filled.Event">
                    Events
                </MudNavLink>
            }
            @if (_hasPermission("logs.view"))
            {
                <MudNavLink Href="/logs" Icon="@Icons.Material.Filled.Article">
                    System Logs
                </MudNavLink>
            }
            @if (_hasPermission("audit.view"))
            {
                <MudNavLink Href="/audit" Icon="@Icons.Material.Filled.History">
                    Audit Logs
                </MudNavLink>
            }
        </MudNavGroup>
    }

    <!-- Settings -->
    @if (_hasPermission("settings.view"))
    {
        <MudNavLink Href="/settings" Icon="@Icons.Material.Filled.Settings">
            Settings
        </MudNavLink>
    }

    <MudDivider Class="my-2" />

    <!-- Quick Actions -->
    <MudNavGroup Text="Quick Actions" Icon="@Icons.Material.Filled.FlashOn" Expanded="false">
        <MudNavLink Href="/send/email" Icon="@Icons.Material.Filled.Email">
            Send Email
        </MudNavLink>
        <MudNavLink Href="/send/sms" Icon="@Icons.Material.Filled.Sms">
            Send SMS
        </MudNavLink>
        <MudNavLink Href="/send/push" Icon="@Icons.Material.Filled.NotificationsActive">
            Send Push
        </MudNavLink>
    </MudNavGroup>

    <!-- Help & Support -->
    <MudNavGroup Text="Help" Icon="@Icons.Material.Filled.Help" Expanded="false">
        <MudNavLink Href="/docs" Icon="@Icons.Material.Filled.MenuBook">
            Documentation
        </MudNavLink>
        <MudNavLink Href="/support" Icon="@Icons.Material.Filled.Support">
            Support
        </MudNavLink>
        <MudNavLink Href="/about" Icon="@Icons.Material.Filled.Info">
            About
        </MudNavLink>
    </MudNavGroup>
</MudNavMenu>

@code {
    private List<string> _userPermissions = new();
    private List<Plugin> _availablePlugins = new();
    private HashSet<string> _expandedGroups = new();

    // Plugin availability flags
    private bool _hasEmailPlugin = false;
    private bool _hasSmsPlugin = false;
    private bool _hasPushPlugin = false;
    private bool _hasWhatsAppPlugin = false;
    private bool _hasSlackPlugin = false;
    private bool _hasAnyMessagingPlugin = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadPermissions();
        await LoadPluginAvailability();

        // Load expanded groups from local storage
        LoadExpandedGroups();

        AuthService.OnAuthStateChanged += OnAuthStateChanged;
    }

    private async Task LoadPermissions()
    {
        _userPermissions = await AuthService.GetUserPermissionsAsync();
    }

    private async Task LoadPluginAvailability()
    {
        try
        {
            _availablePlugins = await PluginService.GetPluginsAsync();

            _hasEmailPlugin = _availablePlugins.Any(p => p.Type.Equals("Email", StringComparison.OrdinalIgnoreCase) && p.IsEnabled);
            _hasSmsPlugin = _availablePlugins.Any(p => p.Type.Equals("SMS", StringComparison.OrdinalIgnoreCase) && p.IsEnabled);
            _hasPushPlugin = _availablePlugins.Any(p => p.Type.Equals("Push", StringComparison.OrdinalIgnoreCase) && p.IsEnabled);
            _hasWhatsAppPlugin = _availablePlugins.Any(p => p.Provider?.Contains("WhatsApp", StringComparison.OrdinalIgnoreCase) == true && p.IsEnabled);
            _hasSlackPlugin = _availablePlugins.Any(p => p.Provider?.Contains("Slack", StringComparison.OrdinalIgnoreCase) == true && p.IsEnabled);

            _hasAnyMessagingPlugin = _hasEmailPlugin || _hasSmsPlugin || _hasPushPlugin || _hasWhatsAppPlugin || _hasSlackPlugin;
        }
        catch (Exception ex)
        {
            // Log error but don't break the UI
            Console.WriteLine($"Failed to load plugin availability: {ex.Message}");
        }
    }

    private void LoadExpandedGroups()
    {
        // In a real implementation, load from local storage
        // For now, expand commonly used groups
        _expandedGroups.Add("messaging");
    }

    private void ToggleGroup(string groupName, bool expanded)
    {
        if (expanded)
        {
            _expandedGroups.Add(groupName);
        }
        else
        {
            _expandedGroups.Remove(groupName);
        }

        // In a real implementation, save to local storage
    }

    private void SetBreadcrumb(string text, string icon, string? href = null)
    {
        BreadcrumbService.SetBreadcrumbs(
            new BreadcrumbItem { Text = "Home", Href = "/dashboard", Icon = Icons.Material.Filled.Home },
            new BreadcrumbItem { Text = text, Href = href, Icon = icon, IsActive = true }
        );
    }

    private bool _hasPermission(string permission)
    {
        return _userPermissions.Contains(permission);
    }

    private async void OnAuthStateChanged(User? user)
    {
        await LoadPermissions();
        await LoadPluginAvailability();
        StateHasChanged();
    }

    public void Dispose()
    {
        AuthService.OnAuthStateChanged -= OnAuthStateChanged;
    }
}

<style>
    .mud-nav-menu {
        padding: 8px 0;
    }

    .mud-nav-link {
        border-radius: 0 25px 25px 0;
        margin: 2px 8px 2px 0;
        padding: 8px 16px;
    }

    .mud-nav-link:hover {
        background-color: var(--mud-palette-action-hover);
    }

    .mud-nav-link.active {
        background-color: var(--mud-palette-primary);
        color: var(--mud-palette-primary-text);
    }

    .mud-nav-group .mud-nav-link {
        padding-left: 32px;
        font-size: 0.875rem;
    }

    .mud-nav-group-header {
        padding: 8px 16px;
        font-weight: 500;
        color: var(--mud-palette-text-secondary);
    }
</style>
